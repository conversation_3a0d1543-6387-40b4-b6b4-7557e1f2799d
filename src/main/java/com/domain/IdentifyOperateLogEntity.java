package com.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

/**
 * (IdentifyOperateLog)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 17:31:04
 */
@Data
public class IdentifyOperateLogEntity  {
@TableId(value = "id", type = IdType.AUTO)
    private Long id;
@TableField(value = "operator")
    private String operator;
@TableField(value = "department")
    private String department;
/**
     * 新增人员(含身份)
     */
@TableField(value = "operate_type")
    private String operateType;
@TableField(value = "operate_object")
    private String operateObject;
@TableField(value = "operate_detail")
    private String operateDetail;
@TableField(value = "operate_status")
    private Integer operateStatus;
@TableField(value = "fail_detail")
    private String failDetail;
@TableField(value = "create_time")
    private Date createTime;
@TableField(value = "last_update_time")
    private Date lastUpdateTime;




}

