<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.NewStudentGroupMapper">

    <resultMap id="BaseResultMap" type="com.domain.NewStudentGroupEntity">
        <!--@Table new_student_group-->
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="groupOwner" column="group_owner" jdbcType="VARCHAR"/>
            <result property="groupOwnerNo" column="group_owner_no" jdbcType="VARCHAR"/>
            <result property="department" column="department" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="VARCHAR"/>
            <result property="personNum" column="person_num" jdbcType="INTEGER"/>
            <result property="remart" column="remart" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateTime" column="last_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
id,
group_name,
group_owner,
group_owner_no,
department,
year,
person_num,
remart,

create_time,
last_update_time

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_group(group_name, group_owner, group_owner_no, department, year, person_num, remart, create_time, last_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.groupName}, #{entity.groupOwner}, #{entity.groupOwnerNo}, #{entity.department}, #{entity.year}, #{entity.personNum}, #{entity.remart}, #{entity.createTime}, #{entity.lastUpdateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_group(group_name, group_owner, group_owner_no, department, year, person_num, remart, create_time, last_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.groupName}, #{entity.groupOwner}, #{entity.groupOwnerNo}, #{entity.department}, #{entity.year}, #{entity.personNum}, #{entity.remart}, #{entity.createTime}, #{entity.lastUpdateTime})
        </foreach>
        on duplicate key update
group_name = values(group_name) , group_owner = values(group_owner) , group_owner_no = values(group_owner_no) , department = values(department) , year = values(year) , person_num = values(person_num) , remart = values(remart) , create_time = values(create_time) , last_update_time = values(last_update_time)     </insert>

</mapper>

