<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.UserRemoveRuleMapper">

    <resultMap id="BaseResultMap" type="com.domain.UserRemoveRuleEntity">
        <!--@Table user_remove_rule-->
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="autoType" column="auto_type" jdbcType="INTEGER"/>
            <result property="autoStatus" column="auto_status" jdbcType="INTEGER"/>
            <result property="operateType" column="operate_type" jdbcType="INTEGER"/>
            <result property="delayDays" column="delay_days" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="dealDate" column="deal_date" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
id,
auto_type,
auto_status,
operate_type,
delay_days,
create_time,
update_time,
deal_date

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.user_remove_rule(auto_type, auto_status, operate_type, delay_days, create_time, update_time, deal_date)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.autoType}, #{entity.autoStatus}, #{entity.operateType}, #{entity.delayDays}, #{entity.createTime}, #{entity.updateTime}, #{entity.dealDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.user_remove_rule(auto_type, auto_status, operate_type, delay_days, create_time, update_time, deal_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.autoType}, #{entity.autoStatus}, #{entity.operateType}, #{entity.delayDays}, #{entity.createTime}, #{entity.updateTime}, #{entity.dealDate})
        </foreach>
        on duplicate key update
auto_type = values(auto_type) , auto_status = values(auto_status) , operate_type = values(operate_type) , delay_days = values(delay_days) , create_time = values(create_time) , update_time = values(update_time) , deal_date = values(deal_date)     </insert>

</mapper>

