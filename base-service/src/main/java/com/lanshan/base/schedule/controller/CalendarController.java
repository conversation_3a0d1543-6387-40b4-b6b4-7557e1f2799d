package com.lanshan.base.schedule.controller;


import com.alibaba.nacos.common.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.dto.oa.calendar.WxCpOaCalendarDTO;
import com.lanshan.base.api.qo.schedule.CalendarSaveQo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOaCalendarService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.oa.calendar.WxCpOaCalendar;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(ServiceConstant.WORK_SCHEDULE)
@Api(tags = "日历管理", hidden = true)
public class CalendarController {

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @ApiOperation("创建日历")
    @PostMapping(value = "calendar/add")
    public Result<String> add(@RequestParam String corpId,
                              @RequestParam String agentId,
                              @RequestBody CalendarSaveQo qo) throws WxErrorException {
        //获取企微服务
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        WxCpOaCalendarService calendarService = wxCpService.getOaCalendarService();
        //创建日历
        WxCpOaCalendar wxCpOaCalendar = WxCpGsonBuilder.create().fromJson(JacksonUtils.toJson(qo), WxCpOaCalendar.class);
        return Result.build(calendarService.add(wxCpOaCalendar));
    }

    @ApiOperation("更新日历")
    @PostMapping(value = "calendar/update")
    public Result<Object> update(@RequestParam String corpId,
                                 @RequestParam String agentId,
                                 @RequestBody CalendarSaveQo qo) throws WxErrorException {
        //获取企微服务
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        WxCpOaCalendarService calendarService = wxCpService.getOaCalendarService();
        //更新日历
        WxCpOaCalendar wxCpOaCalendar = WxCpGsonBuilder.create().fromJson(JacksonUtils.toJson(qo), WxCpOaCalendar.class);
        calendarService.update(wxCpOaCalendar);
        return Result.build();
    }

    @ApiOperation("删除日历")
    @PostMapping(value = "calendar/del")
    public Result<Object> del(@RequestParam String corpId,
                              @RequestParam String agentId,
                              @RequestParam String calId) throws WxErrorException {
        //获取企微服务
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        WxCpOaCalendarService calendarService = wxCpService.getOaCalendarService();
        //删除日历
        calendarService.delete(calId);
        return Result.build();
    }

    @ApiOperation("获取日历")
    @PostMapping(value = "calendar/get")
    public Result<List<WxCpOaCalendarDTO>> get(@RequestParam String corpId,
                                               @RequestParam String agentId,
                                               @RequestParam List<String> calIds) throws WxErrorException {
        //获取企微服务
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        WxCpOaCalendarService calendarService = wxCpService.getOaCalendarService();
        //获取日历
        List<WxCpOaCalendarDTO> wxCpOaCalendarDTOList = JacksonUtils.toObj(WxCpGsonBuilder.create().toJson(calendarService.get(calIds)),
                new TypeReference<>() {
                });
        return Result.build(wxCpOaCalendarDTOList);
    }
}
