package com.lanshan.base.starter.wxcpsdk.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.starter.wxcpsdk.dao.AgentConfigDao;
import com.lanshan.base.starter.wxcpsdk.entity.AgentConfig;
import com.lanshan.base.starter.wxcpsdk.service.AgentConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应用配置表(AgentConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-12 09:01:53
 */
@Service("agentConfigService")
public class AgentConfigServiceImpl extends ServiceImpl<AgentConfigDao, AgentConfig> implements AgentConfigService {

    /**
     * 设置应用配置
     * @param agentConfig 应用配置
     * @return 应用配置id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long setAgentConfig(AgentConfig agentConfig) {
        LambdaQueryWrapper<AgentConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentConfig::getCorpId, agentConfig.getCorpId());
        queryWrapper.eq(AgentConfig::getAgentId, agentConfig.getAgentId());
        AgentConfig dbAgentConfig = this.getOne(queryWrapper);

        if(dbAgentConfig == null || dbAgentConfig.getAgentId() == null){
            dbAgentConfig = agentConfig;
        }else{
            BeanUtil.copyProperties(agentConfig, dbAgentConfig, CopyOptions.create().setIgnoreNullValue(true));
        }
        super.saveOrUpdate(dbAgentConfig);
        return dbAgentConfig.getId();
    }
}

