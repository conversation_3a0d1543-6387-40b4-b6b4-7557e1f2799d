package com.lanshan.base.starter.wxcpsdk;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.base.api.enums.AgentTypeEnum;
import com.lanshan.base.starter.wxcpsdk.entity.AgentConfig;
import com.lanshan.base.starter.wxcpsdk.service.AgentConfigService;
import me.chanjar.weixin.common.error.WxRuntimeException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WxCpService服务工厂类
 *
 * <AUTHOR>
 */
@Component
public class WxCpServiceFactory {

    private final Map<String, WxCpService> cpServices = new ConcurrentHashMap<>(128);

    @Resource
    private AgentConfigService agentConfigService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private HttpProxyConfig httpProxyConfig;

    private static final String DEFAULT_TOKEN = "LoN24yty7xwbMp";
    private static final String DEFAULT_AES_KEY = "FeB3IPQqzIEn6HLDPhfghHck76hVS4a2UqZappHL8bK";

    /**
     * 获取 WxCpService
     *
     * @param corpId  企业ID
     * @param agentId 应用ID
     * @return WxCpService
     */
    public WxCpService get(String corpId, String agentId) throws WxRuntimeException {

        // 所需WxCpService 未初始化, 需要从数据库读取配置并初始化，然后存入MAP
        return cpServices.computeIfAbsent(getServiceKey(corpId, agentId), key -> {
            AgentConfig agentConfig = agentConfigService.getOne(getQueryWrapper(corpId, agentId));
            if (agentConfig == null) {
                throw new WxRuntimeException(String.format("未配置此service:corpId=%s;agentId=%s", corpId, agentId));
            }
            WxCpRedissonConfigImpl config = newWxCpRedissonConfig();

            if (httpProxyConfig.isEnable()) {
                config.setHttpProxyHost(httpProxyConfig.getHost());
                config.setHttpProxyPort(httpProxyConfig.getPort());
                config.setHttpProxyUsername(httpProxyConfig.getUsername());
                config.setHttpProxyPassword(httpProxyConfig.getPassword());
            }
            config.setCorpId(agentConfig.getCorpId());
            config.setAgentId(agentConfig.getAgentId());
            config.setCorpSecret(agentConfig.getSecret());
            config.setToken(StringUtils.isEmpty(agentConfig.getToken()) ? DEFAULT_TOKEN : agentConfig.getToken());
            config.setAesKey(StringUtils.isEmpty(agentConfig.getAeskey()) ? DEFAULT_AES_KEY : agentConfig.getAeskey());
            WxCpService service = new WxCpServiceImpl();
            service.setWxCpConfigStorage(config);
            return service;
        });
    }

    public WxCpRedissonConfigImpl newWxCpRedissonConfig() {
        return new WxCpRedissonConfigImpl(redissonClient, "workRedis:");
    }

    /**
     * 设置指定key值WxCpService
     *
     * @param key     键值
     * @param service WxCpService
     */
    public void put(String key, WxCpService service) {
        cpServices.put(key, service);
    }

    /**
     * 删除指定key的WxCpService
     *
     * @param key 键值
     */
    public void delete(String key) {
        cpServices.remove(key);
    }

    /**
     * 清空WxCpService, 便于重新加载
     */
    public void deleteAll() {
        cpServices.clear();
    }

    /**
     * 获取所有应用配置列表
     *
     * @return List<AgentConfig>
     */
    public List<AgentConfig> getAgentConfig() {
        return agentConfigService.list();
    }

    /**
     * 根据企业ID和应用ID获取应用配置信息
     *
     * @param corpId  企业ID
     * @param agentId 应用ID
     * @return AgentConfig
     */
    public AgentConfig getOneAgentConfig(String corpId, String agentId) {
        return agentConfigService.getOne(getQueryWrapper(corpId, agentId));
    }

    /**
     * 获取默认的 WxCpService
     *
     * @return WxCpService
     */
    public WxCpService getDefaultService() {
        LambdaQueryWrapper<AgentConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentConfig::getAgentType, AgentTypeEnum.DEFAULT.getCode());
        List<AgentConfig> list = agentConfigService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new WxRuntimeException("未配置默认的WxCpService！");
        }
        return get(list.get(0).getCorpId(), AgentTypeEnum.DEFAULT.getCode());
    }

    /**
     * 构建应用配置查询条件
     *
     * @param corpId  企业ID
     * @param agentId 应用ID
     * @return LambdaQueryWrapper
     */
    private LambdaQueryWrapper<AgentConfig> getQueryWrapper(String corpId, String agentId) {
        // agentId为纯数字的情况，直接根据agentId查询
        if (StringUtils.isNumeric(agentId)) {
            return new LambdaQueryWrapper<AgentConfig>()
                    .eq(AgentConfig::getCorpId, corpId)
                    .eq(AgentConfig::getAgentId, Integer.valueOf(agentId));
        } else {
            return new LambdaQueryWrapper<AgentConfig>()
                    .eq(AgentConfig::getCorpId, corpId)
                    .eq(AgentConfig::getAgentType, agentId);
        }
    }

    /**
     * 获取服务key
     *
     * @param corpId  企业ID
     * @param agentId 应用ID
     * @return String
     */
    public String getServiceKey(String corpId, String agentId) {
        return corpId + ":" + agentId;
    }
}
