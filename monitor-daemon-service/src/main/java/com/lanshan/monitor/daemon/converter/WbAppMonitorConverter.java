package com.lanshan.monitor.daemon.converter;


import com.lanshan.monitor.daemon.entity.WbAppMonitor;
import com.lanshan.monitor.daemon.model.dto.BatchSetupMonitorDTO;
import com.lanshan.monitor.daemon.model.vo.WbAppMonitorVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 应用监控(WbAppMonitor)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppMonitorConverter {

    WbAppMonitorConverter INSTANCE = Mappers.getMapper(WbAppMonitorConverter.class);

    WbAppMonitorVO toVO(WbAppMonitor entity);

    WbAppMonitor toEntity(WbAppMonitorVO vo);

    List<WbAppMonitorVO> toVO(List<WbAppMonitor> entityList);

    List<WbAppMonitor> toEntity(List<WbAppMonitorVO> voList);

    /**
     * 批量设置通知DTO转换实体
     *
     * @param batchSetupMonitorDTO 批量设置通知对象
     * @return 实体对象
     */
    WbAppMonitor batchSetupNotifyDTOTOEntity(BatchSetupMonitorDTO batchSetupMonitorDTO);
}


