package com.lanshan.monitor.daemon.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * (WbAppMonitorLog)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WbAppMonitorLog extends Model<WbAppMonitorLog> {
    /**
     * 主键
     */
    private Long id;
    /**
     * web_app 主键
     */
    private Long appId;
    /**
     * 健康状态
     */
    private Boolean heathStatus;
    /**
     * CPU 总的使用率
     */
    private Double cpuUsed;
    /**
     * 内存总量 单位 GB
     */
    private Double totalMemory;
    /**
     * 内存剩余量 单位 GB
     */
    private Double freeMemory;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

