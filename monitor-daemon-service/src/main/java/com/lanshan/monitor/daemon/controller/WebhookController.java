package com.lanshan.monitor.daemon.controller;

import com.lanshan.monitor.daemon.service.WebhookService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @description
 */
@RequiredArgsConstructor
@RequestMapping("/webhook")
@RestController
public class WebhookController {

    private final WebhookService webhookService;

    @PostMapping("/message/send")
    public void send(@RequestBody String messageString) {
        webhookService.send(messageString);
    }
}
