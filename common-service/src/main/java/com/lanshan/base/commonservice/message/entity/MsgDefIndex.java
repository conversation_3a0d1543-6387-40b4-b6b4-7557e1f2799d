package com.lanshan.base.commonservice.message.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;
import org.dromara.easyes.annotation.rely.RefreshPolicy;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 消息定义索引文件实体
 */
@Data
@IndexName(value = "msg-def", refreshPolicy = RefreshPolicy.IMMEDIATE)
public class MsgDefIndex implements Serializable {
    private static final long serialVersionUID = 310104394108547420L;

    @ApiModelProperty("主键")
    @IndexId(type = IdType.CUSTOMIZE)
    private Long id;

    @ApiModelProperty("类型  text：纯文本 textcard：文本卡片 news：图文")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String type;

    @ApiModelProperty("标题")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String title;

    @ApiModelProperty("描述")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String description;

    @ApiModelProperty("原始描述")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String descriptionOriginal;

    @ApiModelProperty("链接地址")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String linkUrl;

    @ApiModelProperty("包装之后的链接地址")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String parcelLinkUrl;

    @ApiModelProperty("链接应用id")
    @IndexField(fieldType = FieldType.LONG)
    private Long linkAppId;

    @ApiModelProperty("图片地址")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String imgUrl;

    @ApiModelProperty("消息来源 1：应用 2：管理员")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer source;

    @ApiModelProperty("应用id")
    @IndexField(fieldType = FieldType.LONG)
    private Long appId;

    @ApiModelProperty("应用名称")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String appName;

    @ApiModelProperty("企微应用id")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String agentId;

    @ApiModelProperty("发布者")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String publisher;

    @ApiModelProperty("发布类型 1：立即发送 2：定时发送")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer publishType;

    @ApiModelProperty("发布渠道  WEIXIN_CORP_CHANNEL：企业微信 SMS_CHANNEL：短信 EMAIL_CHANNEL：邮箱")
    @IndexField(fieldType = FieldType.KEYWORD)
    private List<String> publishChannel;

    @ApiModelProperty("发布渠道发送状态  0：失败 1：成功")
    @IndexField(fieldType = FieldType.INTEGER)
    private List<Integer> publishChannelSendStatus;

    @ApiModelProperty("企微发布状态 0：失败 1：成功")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer cpPublishStatus;

    @ApiModelProperty("发布时间")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("用户列表")
    @IndexField(fieldType = FieldType.KEYWORD)
    private List<String> userList;

    @ApiModelProperty("部门列表")
    @IndexField(fieldType = FieldType.LONG)
    private List<Long> departmentList;

    @ApiModelProperty("标签列表")
    @IndexField(fieldType = FieldType.LONG)
    private List<Long> tagList;

    @ApiModelProperty("发送用户数量")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer sendUserCount;

    @ApiModelProperty("已读用户数量")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer readUserCount;

    @ApiModelProperty("状态 1：待发送 2：已发送 3：已撤回")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer status;

    @ApiModelProperty("是否必读 0：否 1：是")
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isMustRead;

    @ApiModelProperty("企微消息id")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String cpMsgId;

    @ApiModelProperty("创建者")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String creator;

    @ApiModelProperty("创建者名称")
    @IndexField(fieldType = FieldType.KEYWORD)
    private String creatorName;

    @ApiModelProperty("创建时间")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty("更新时间")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}

