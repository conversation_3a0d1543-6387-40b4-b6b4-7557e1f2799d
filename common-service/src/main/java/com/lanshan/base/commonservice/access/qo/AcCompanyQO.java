package com.lanshan.base.commonservice.access.qo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


@ApiModel(value = "接入方QO")
@Data
@ToString
public class AcCompanyQO implements Serializable {

    private static final long serialVersionUID = -6383729814459099265L;
    
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "接入方名称")
    private String name;

    @ApiModelProperty(value = "责任人")
    private String contactPerson;

    @ApiModelProperty(value = "责任人电话")
    private String contactNumber;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "登录账号")
    private String loginName;

    @ApiModelProperty(value = "开放文档地址")
    private String openDocUrl;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;
}

