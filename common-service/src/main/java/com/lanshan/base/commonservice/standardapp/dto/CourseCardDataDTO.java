package com.lanshan.base.commonservice.standardapp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("课程卡片设置数据对象")
public class CourseCardDataDTO implements Serializable {
    private static final long serialVersionUID = 5778472265122339652L;

    @ApiModelProperty(value = "用户工号")
    private String userId;

    @ApiModelProperty(value = "课程卡片url")
    private String url;

    @ApiModelProperty(value = "课程表应用agentID")
    private String agentId;
}
