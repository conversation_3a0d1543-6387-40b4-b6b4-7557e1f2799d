package com.lanshan.base.commonservice.schooldata.hbou.dto;

import lombok.Data;

import java.util.List;

/**
 * 待办查询结果DTO
 */
@Data
public class SearchTodoResultDTO {

    private boolean needTotal;

    private List<TodoInfo> data;
    private Integer dataCount;
    private Integer total;
    private Integer pages;
    private Integer size;
    private List<Object> sortPairs;
    private boolean showTotal;
    private String sortOrder;
    private long page;
    private long startAt;

    @Data
    public static class TodoInfo {

        private Long id;
        /**
         * 待办标题
         */
        private String title;

        /**
         * 发送人姓名
         */
        private String sender;

        /**
         * 发送人工号
         */
        private String senderNumber;

        /**
         * 发送时间
         */
        private String sendDate;

        /**
         * 接收日期
         */
        private String receiveDate;

        /**
         * 完成时间
         */
        private String completeTime;

        /**
         * pc 端地址
         */
        private String pcUrl;

        /**
         * 手机端地址
         */
        private String h5Url;

        /**
         * 节点名称
         */
        private String nodeName;

        /**
         * 接收人工号
         */
        private String workNumber;

        /**
         * 接收人姓名
         */
        private String name;

        /**
         * 创建时间
         */
        private String createTime;
    }
}



