package com.lanshan.base.commonservice.workbench.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 热门搜索关键词(WbSearchHotKeyword)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class WbSearchHotKeyword extends Model<WbSearchHotKeyword> {
    private static final long serialVersionUID = 2355405523272923178L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 搜索关键词
     */
    private String keyword;
    /**
     * 搜索次数
     */
    private Long searchTimes;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

