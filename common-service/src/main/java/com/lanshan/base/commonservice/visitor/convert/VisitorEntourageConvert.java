package com.lanshan.base.commonservice.visitor.convert;

import com.lanshan.base.commonservice.visitor.entity.VisitorEntourage;
import com.lanshan.base.commonservice.visitor.qo.VisitorEntourageQo;
import com.lanshan.base.commonservice.visitor.vo.VisitorEntourageVo;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(builder = @Builder(disableBuilder = true))
public interface VisitorEntourageConvert {

    VisitorEntourageConvert INSTANCE = Mappers.getMapper(VisitorEntourageConvert.class);

    VisitorEntourageVo toVo(VisitorEntourage entity);

    List<VisitorEntourageVo> toVo(List<VisitorEntourage> entityList);

    List<VisitorEntourage> toEntity(List<VisitorEntourageQo> qoList);

}
