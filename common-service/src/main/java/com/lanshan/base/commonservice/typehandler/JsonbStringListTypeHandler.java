package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * JSONB到List<String>的类型处理器
 * 
 * 专门用于处理数据库JSONB字段到Java List<String>类型的转换
 * 
 * 使用场景：
 * 1. 数据库中存储字符串数组，如：["1","2","3"]
 * 2. 需要将JSON字符串数组转换为Java List<String>对象
 * 3. 适用于标签、权限、分类等字符串列表
 * 
 * 使用方式：
 * @TableField(typeHandler = JsonbStringListTypeHandler.class)
 * private List<String> tags;
 * 
 * 或在XML中：
 * #{tags,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringListTypeHandler}
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({List.class})
public class JsonbStringListTypeHandler extends UniversalJsonbTypeHandler<List<String>> {
    
    /**
     * 默认构造函数，处理List<String>类型
     */
    public JsonbStringListTypeHandler() {
        super(new TypeReference<List<String>>() {});
    }
}
