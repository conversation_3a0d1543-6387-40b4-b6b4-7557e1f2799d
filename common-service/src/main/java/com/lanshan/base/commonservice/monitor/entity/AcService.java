package com.lanshan.base.commonservice.monitor.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用服务信息表(AcService)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class AcService extends Model<AcService> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 服务名称
     */
    private String name;
    /**
     * 服务部署服务器IP
     */
    private String hostIp;
    /**
     * 服务端口
     */
    private Integer port;
    /**
     * 启动命令
     */
    private String startUrl;
    /**
     * 停止命令
     */
    private String stopUrl;
    /**
     * 重启命令
     */
    private String restartUrl;
    /**
     * 健康检查地址
     */
    private String healthCheckUrl;
    /**
     * 服务当前状态。1：服务中；2：停止服务；99：服务异常
     */
    private Integer status;
    /**
     * 服务警报通知人ID; 多个用“,”分隔
     */
    private String contactUser;
    /**
     * 服务警报通知部门；多个用 “,” 分割
     */
    private String contactDept;
    /**
     * 服务警报通知标签；多个用 “,” 分割
     */
    private String contactTag;
    /**
     *配置的支持类型 1 启动 2 重启 3 停止 以逗号分割
     */
    private String supportType;
    /**
     *服务启动时间
     */
    private Date startTime;
    /**
     *服务结束时间
     */
    private Date endTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

