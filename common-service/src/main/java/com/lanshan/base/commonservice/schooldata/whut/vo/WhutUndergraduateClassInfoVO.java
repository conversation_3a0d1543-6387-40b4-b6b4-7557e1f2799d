package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 本科生班级基本信息表(WhutUndergraduateClassInfo)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "本科生班级基本信息表VO")
@Data
@ToString
public class WhutUndergraduateClassInfoVO implements Serializable {

    @ApiModelProperty(value = "自增主键ID")
    private Long id;

    @ApiModelProperty(value = "班级编码")
    private String bjbm;

    @ApiModelProperty(value = "班级名称")
    private String bjmc;

    @ApiModelProperty(value = "班级全称")
    private String bjqc;

    @ApiModelProperty(value = "专业编码")
    private String zybm;

    @ApiModelProperty(value = "所属年级")
    private String ssjn;

    @ApiModelProperty(value = "学制")
    private String xz;

    @ApiModelProperty(value = "建班年月")
    private String jbny;

    @ApiModelProperty(value = "毕业年度")
    private String bynd;

    @ApiModelProperty(value = "班长学号")
    private String bzxh;

    @ApiModelProperty(value = "班级性质")
    private String bjxz;

    @ApiModelProperty(value = "男生人数")
    private String nsrs;

    @ApiModelProperty(value = "女生人数")
    private String nvsrs;

    @ApiModelProperty(value = "时间戳")
    private Date tstam;

    @ApiModelProperty(value = "群聊ID")
    private String chatid;

    @ApiModelProperty(value = "建群状态（0:待建群 1:已建群）")
    private Integer status;
}

