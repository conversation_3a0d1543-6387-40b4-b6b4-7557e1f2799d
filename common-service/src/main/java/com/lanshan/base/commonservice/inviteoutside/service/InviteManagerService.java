package com.lanshan.base.commonservice.inviteoutside.service;

import com.lanshan.base.commonservice.inviteoutside.pojo.InviteSetting;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InviteManagerService {

    /**
     * 获取邀请配置
     *
     * @return 邀请配置
     */
    InviteSetting getInviteSetting();

    /**
     * 修改设置
     *
     * @param companyId 服务商 ID
     * @param status 启用禁用 true 启用 false 禁用
     * @return true 成功 false 失败
     */
    Boolean enableDisable(Long companyId, Boolean status);


    /**
     * 修改设置
     *
     * @param inviteSetting 邀请配置
     * @return true 成功 false 失败
     */
    Boolean changeInviteSetting(InviteSetting inviteSetting);

}
