package com.lanshan.base.commonservice.message.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("消息定义分页查询条件")
public class MsgDefPageQo extends PageQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发布者")
    private String publisher;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("发布类型 1：立即发送 2：定时发送")
    private Integer publishType;

    @ApiModelProperty("消息来源 1：应用 2：管理员")
    private Integer source;

    @ApiModelProperty("应用id")
    private Long appId;

    @ApiModelProperty("类型 text：纯文本 textcard：文本卡片 news：图文")
    private String type;

    @ApiModelProperty("状态 1：待发送 2：已发送 3：已撤回")
    private Integer status;
}

