package com.lanshan.base.commonservice.standardapp.controller;


import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.service.StdNetworkTrafficUsageInfoService;
import com.lanshan.base.commonservice.standardapp.vo.NetworkTrafficStatisticVO;
import com.lanshan.base.commonservice.standardapp.vo.NetworkTrafficUsageInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网络流量使用情况(NetworkTrafficUsageInfo)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("networkTrafficUsageInfo")
@Api(tags = "网络流量使用情况(NetworkTrafficUsageInfo)控制层", hidden = true)
public class StdNetworkTrafficUsageInfoController {
    /**
     * 服务对象
     */
    @Resource
    private StdNetworkTrafficUsageInfoService stdNetworkTrafficUsageInfoService;


    @ApiOperation("获取流量使用信息")
    @GetMapping("/get")
    public Result<NetworkTrafficUsageInfoVO> get() {
        return Result.build(this.stdNetworkTrafficUsageInfoService.getNetworkTrafficUsageInfo(SecurityContextHolder.getUserId()));
    }

    @ApiOperation("获取流量使用统计信息")
    @GetMapping("/statistic")
    public Result<List<NetworkTrafficStatisticVO>> statistic(String dimensional) {
        List<NetworkTrafficStatisticVO> networkTrafficStatisticList = stdNetworkTrafficUsageInfoService.getNetworkTrafficStatistic(SecurityContextHolder.getUserId(), dimensional, 6);
        return Result.build(networkTrafficStatisticList);
    }

}

