package com.lanshan.base.commonservice.system.controller;


import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.constant.CacheConstant;
import com.lanshan.base.api.dto.AjaxResult;
import com.lanshan.base.api.dto.user.LoginUser;
import com.lanshan.base.api.page.TableDataInfo;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.RyStringUtils;
import com.lanshan.base.commonservice.system.entity.SysUserOnline;
import com.lanshan.base.commonservice.system.service.ISysUserOnlineService;
import com.lanshan.base.starter.log.annotation.Log;
import com.lanshan.base.starter.log.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/online")
public class SysUserOnlineController extends BaseController {
    @Autowired
    private ISysUserOnlineService sysUserOnlineService;

    @Autowired
    private RedisService redisService;

    @RequiresPermissions("monitor:online:list")
    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName) {
        Collection<String> keys = redisService.keys(CacheConstant.ACCESS_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<>();
        for (String key : keys) {
            LoginUser user = redisService.getCacheObject(key);
            if (RyStringUtils.isNotEmpty(ipaddr) && RyStringUtils.isNotEmpty(userName)) {
                userOnlineList.add(sysUserOnlineService.selectOnlineByInfo(ipaddr, userName, user));
            } else if (RyStringUtils.isNotEmpty(ipaddr)) {
                userOnlineList.add(sysUserOnlineService.selectOnlineByIpaddr(ipaddr, user));
            } else if (RyStringUtils.isNotEmpty(userName)) {
                userOnlineList.add(sysUserOnlineService.selectOnlineByUserName(userName, user));
            } else {
                userOnlineList.add(sysUserOnlineService.loginUserToUserOnline(user));
            }
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));
        return getDataTable(userOnlineList);
    }

    /**
     * 强退用户
     */
    @RequiresPermissions("monitor:online:forceLogout")
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    @PostMapping("/{tokenId}/delete")
    public AjaxResult forceLogout(@PathVariable String tokenId) {
        redisService.deleteObject(CacheConstant.ACCESS_TOKEN_KEY + tokenId);
        return success();
    }
}
