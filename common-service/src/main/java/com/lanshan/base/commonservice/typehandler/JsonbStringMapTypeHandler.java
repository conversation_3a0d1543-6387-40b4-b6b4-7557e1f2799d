package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.Map;

/**
 * JSONB到Map<String, String>的类型处理器
 * 
 * 专门用于处理数据库JSONB字段到Java Map<String, String>类型的转换
 * 
 * 使用场景：
 * 1. 数据库中存储字符串键值对，如：{"key1":"value1","key2":"value2"}
 * 2. 需要将JSON对象转换为Java Map<String, String>对象
 * 3. 适用于纯字符串的配置信息、标签映射等
 * 
 * 使用方式：
 * @TableField(typeHandler = JsonbStringMapTypeHandler.class)
 * private Map<String, String> labels;
 * 
 * 或在XML中：
 * #{labels,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringMapTypeHandler}
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({Map.class})
public class JsonbStringMapTypeHandler extends UniversalJsonbTypeHandler<Map<String, String>> {
    
    /**
     * 默认构造函数，处理Map<String, String>类型
     */
    public JsonbStringMapTypeHandler() {
        super(new TypeReference<Map<String, String>>() {});
    }
}
