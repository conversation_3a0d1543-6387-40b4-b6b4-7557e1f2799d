package com.lanshan.base.commonservice.visitor.enums;

import lombok.Getter;

/**
 * 访客-操作类型
 */
@Getter
public enum VisitorOperateTypeEnum {
    //提交预约、提交邀约、取消预约、取消邀约、审核通过、审核驳回

    SUBMIT_APPOINTMENT(1, "提交预约"),
    SUBMIT_INVITATION(2, "提交邀约"),
    CANCEL_APPOINTMENT(3, "取消预约"),
    CANCEL_INVITATION(4, "取消邀约"),
    APPROVE(5, "审核通过"),
    REJECT(6, "审核驳回");

    private final int code;
    private final String msg;

    VisitorOperateTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
