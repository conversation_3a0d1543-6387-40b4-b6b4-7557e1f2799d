package com.lanshan.base.commonservice.message.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 湖南中医药短信验证码发送处理类
 */
@Component("hnucmSmsCodeSend")
@Slf4j
@RequiredArgsConstructor
public class HnucmSmsCodeSend implements SmsPhoneCodeSend {

    final private RedissonClient redissonClient;

    /**
     * 发送手机验证码
     *
     * @param phone 手机号
     * @param code  验证码
     */
    @Override
    public void sendPhoneCode(String phone, String code) {
        RBucket<String> bucket = redissonClient.getBucket("hnucm:sms:api:token");
        String token = bucket.get();
        if (token == null) {
            token = HnucmSmsSend.getToken();
            if (token == null || token.isEmpty()) {
                return;
            }
            bucket.set(token, 7200, TimeUnit.SECONDS);
        }
        String rspCode = HnucmSmsSend.sendMessage(phone, code, token);
        // 如果返回500003，则重新获取token
        if ("500003".equals(rspCode)) {
            token = HnucmSmsSend.getToken();
            if (token == null || token.isEmpty()) {
                return;
            }
            bucket.set(token, 7200, TimeUnit.SECONDS);
            HnucmSmsSend.sendMessage(phone, code, token);
        }
    }

    /**
     * 发送手机验证码
     *
     * @param phone  手机号
     * @param code   验证码
     * @param userId 用户id
     */
    @Override
    public void sendPhoneCode(String phone, String code, String userId) {
        sendPhoneCode(phone, code);
    }
}
