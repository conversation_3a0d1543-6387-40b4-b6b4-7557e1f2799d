package com.lanshan.base.commonservice.standardapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standardapp.dto.StdCalenderWeekDTO;
import com.lanshan.base.commonservice.standardapp.entity.StdTeachCalender;
import com.lanshan.base.commonservice.standardapp.vo.StdCalenderTermVO;
import com.lanshan.base.commonservice.standardapp.vo.StdCalenderWeekVO;
import com.lanshan.base.commonservice.standardapp.vo.StdTeachCalenderVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 教学日历(StdTeachCalender)表服务接口
 *
 * <AUTHOR>
 */
public interface StdTeachCalenderService extends IService<StdTeachCalender> {

    /**
     * 初始化数据
     *
     * @param year 年份
     */
    @Transactional(rollbackFor = Exception.class)
    void initData(Integer year);

    @Transactional(rollbackFor = Exception.class)
    void updateWeekOfTerm(String teachYear);

    /**
     * 获取学期教学日历
     *
     * @param teachYear 年份
     * @param term      学期
     * @param weekNo    第几周
     * @return 教学日历
     */
    List<StdTeachCalenderVO> getTeachCalendar(String teachYear, Integer term, Integer weekNo);

    /**
     * 获取所有可用的(学年, 学期)组合
     * <AUTHOR> yang.
     * @since 2025/5/8 16:43
     */
    List<StdCalenderTermVO> getYearAndTermList();

    /**
     * 根据学年+学期，查询该学期的所有周次
     * <AUTHOR> yang.
     * @since 2025/5/8 16:43
     */
    List<StdCalenderWeekVO> getWeekListByYearAndTerm(StdCalenderWeekDTO dto);

    /**
     * 根据学年+学期+周次，查询该周次范围内的日期
     * <AUTHOR> yang.
     * @since 2025/5/8 17:15
     * @return yyyy-mm-dd格式字符串集合
     */
    List<String> getRangeDayByWeek(StdCalenderWeekDTO dto);
}

