package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息列表(UserInfo)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class UserInfo extends Model<UserInfo> {
    private static final long serialVersionUID = -6122518766004977452L;
    /**
     * 工号
     */
    @TableId
    private String userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 证件号码（加密）
     */
    private String identityNumber;
    /**
     * 证件类型（0否、1外交、2留学生、3港澳台） 等待学校数据，待定
     */
    private String identityType;
    /**
     * 手机号
     */
    private String phoneNo;
    /**
     * 用户类型(0：其他人员 1：教职工 2：本科生 3：研究生 4: 新生)
     */
    private String userType;
    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 部门名称
     */
    private String deptName;
    private Date createDate;
    private Date updateDate;
    /**
     * 证件号码 420****3211
     */
    private String identityNumberWrapper;
    /**
     * 是否已加密（数据治理时标记false,定时任务加密成功后标记true）
     */
    private Boolean encrypted;

    /**
     * 是否允许证件核验
     */
    private Boolean allowIdentityCheck;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.userId;
    }
}
