package com.lanshan.base.commonservice.standardapp.enums;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 网络流量统计维度枚举
 */
public enum NetTraStatisticDimensionalEnum {

    MONTH("month", "月"),
    DAY("day", "日");

    private final String value;
    private final String name;

    NetTraStatisticDimensionalEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }


    public String getName() {
        return name;
    }
}
