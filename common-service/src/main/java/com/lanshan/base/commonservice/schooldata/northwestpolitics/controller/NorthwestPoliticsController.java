package com.lanshan.base.commonservice.schooldata.northwestpolitics.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.common.utils.ExcelUtils;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.*;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.*;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.NorthwestPoliticsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 学校数据
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/northwestPolitics/record")
public class NorthwestPoliticsController {

    @Resource
    private SdDwxxService sdDwxxService;
    @Resource
    private SdJzgxxService sdJzgxxService;
    @Resource
    private SdXsxjxxService sdXsxjxxService;
    @Resource
    private SdXsxxService sdXsxxService;
    @Resource
    private SdBsyjsxjxxService sdBsyjsxjxxService;
    @Resource
    private SdSsyjsxjxxService sdSsyjsxjxxService;


    @PostMapping(value = "/syncData",produces = "application/json;charset=UTF-8")
    public Result<Object> syncData() {
        log.info("同步西北政法大学数据开始");
        TimeInterval timer = DateUtil.timer();
        TimeInterval timerTotal = DateUtil.timer();

        sdDwxxService.syncSdDwxx();
        log.info("同步学校机构耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdJzgxxService.syncSdJzgxx();
        log.info("同步教职工信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdXsxjxxService.syncSdXsxjxx();
        log.info("同步学籍信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdXsxxService.syncSdXsx();
        log.info("同步学生信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdBsyjsxjxxService.syncBsyjsxjxx();
        log.info("同步博士研究生学籍信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        sdSsyjsxjxxService.syncSsyjsxjxx();
        log.info("同步硕士研究生学籍信息耗时：{}", DateUtil.formatBetween(timer.intervalRestart()));

        log.info("同步西北政法大学数据结束总耗时：{}", DateUtil.formatBetween(timerTotal.intervalMs()));
        return Result.build();
    }

    @PostMapping(value = "/exportStaffInfoIntegratedList",produces = "application/json;charset=UTF-8")
    public void exportStaffInfoIntegratedList(HttpServletResponse response){
        List<StaffInfoIntegratedDTO> staffInfoList = NorthwestPoliticsUtil.getStaffInfoIntegrated(null);
        String today = DateUtil.today();
        ExcelUtils.exportExcelByRecords("教职工基本信息_综合_"+today, staffInfoList, StaffInfoIntegratedDTO.class, response);
    }

    @PostMapping(value = "/exportStaffInfoList",produces = "application/json;charset=UTF-8")
    public void exportStaffInfoList(HttpServletResponse response){
        List<StaffInfoDTO> staffInfoList = NorthwestPoliticsUtil.getStaffInfo(null);
        String today = DateUtil.today();
        ExcelUtils.exportExcelByRecords("教职工基本信息_"+today, staffInfoList, StaffInfoDTO.class, response);
    }

    @PostMapping(value = "/exportStaffContactInfo",produces = "application/json;charset=UTF-8")
    public void exportStaffContactInfo(HttpServletResponse response){
        List<StaffContactDTO> staffInfoList = NorthwestPoliticsUtil.getStaffContactInfo(null);
        String today = DateUtil.today();
        ExcelUtils.exportExcelByRecords("教职工联系信息_"+today, staffInfoList, StaffContactDTO.class, response);
    }

    @PostMapping(value = "/exportSchoolInfoList",produces = "application/json;charset=UTF-8")
    public void exportSchoolInfoList(HttpServletResponse response){
        List<SchoolInfoDTO> schoolInfo = NorthwestPoliticsUtil.getSchoolInfo(null);
        String today = DateUtil.today();
        ExcelUtils.exportExcelByRecords("学校机构基本信息_"+today, schoolInfo, SchoolInfoDTO.class, response);
    }

    @PostMapping(value = "/exportStudentBasicInfo",produces = "application/json;charset=UTF-8")
    public void  exportStudentBasicInfo(HttpServletResponse response){
        List<StudentBasicInfoDTO> records = NorthwestPoliticsUtil.getStudentBasicInfo(null);
        String today = DateUtil.today();
        ExcelUtils.exportExcelByRecords("学生基本信息_"+today, records, StudentBasicInfoDTO.class, response);
    }

    @PostMapping(value = "/exportStudentStatusInfo",produces = "application/json;charset=UTF-8")
    public void  exportStudentStatusInfo(HttpServletResponse response){
        List<StudentStatusInfoDTO> records = NorthwestPoliticsUtil.getStudentStatusInfo(null);
        String today = DateUtil.today();
        ExcelUtils.exportExcelByRecords("本专科生_学籍信息_"+today, records, StudentStatusInfoDTO.class, response);
    }


    @PostMapping(value = "/exportStudentContactInfo",produces = "application/json;charset=UTF-8")
    public void  exportStudentContactInfo(HttpServletResponse response){
        String today = DateUtil.today();
        List<StudentContactDTO> records = NorthwestPoliticsUtil.getStudentContactInfo(null);
        ExcelUtils.exportExcelByRecords("本专科生_联系信息_"+today, records, StudentContactDTO.class, response);
    }

    @PostMapping(value = "/exportDoctorStatusInfo",produces = "application/json;charset=UTF-8")
    public void  exportDoctorStatusInfo(HttpServletResponse response){
        String today = DateUtil.today();
        List<DoctorStatusInfoDTO> records = NorthwestPoliticsUtil.getDoctorStatusInfo(null);
        ExcelUtils.exportExcelByRecords("博士研究生学籍信息_"+today, records, DoctorStatusInfoDTO.class, response);
    }

    @PostMapping(value = "/exportMasterStatusInfo",produces = "application/json;charset=UTF-8")
    public void  exportMasterStatusInfo(HttpServletResponse response){
        String today = DateUtil.today();
        List<MasterStatusInfoDTO> records = NorthwestPoliticsUtil.getMasterStatusInfo(null);
        ExcelUtils.exportExcelByRecords("硕士研究生学籍信息_"+today, records, MasterStatusInfoDTO.class, response);
    }

}
