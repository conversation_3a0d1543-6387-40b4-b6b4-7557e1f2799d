package com.lanshan.base.commonservice.schooldata.whut.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.whut.dao.WhutDepartmentDao;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutDepartment;
import com.lanshan.base.commonservice.schooldata.whut.qo.ViewEchoCharlieQo;
import com.lanshan.base.commonservice.schooldata.whut.service.WhutDepartmentService;
import com.lanshan.base.commonservice.schooldata.whut.vo.ResponseSet;
import com.lanshan.base.commonservice.schooldata.whut.vo.ViewEchoCharlieResponseSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 院系所单位信息表(WhutDepartment)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("whutDepartmentService")
public class WhutDepartmentServiceImpl extends ServiceImpl<WhutDepartmentDao, WhutDepartment> implements WhutDepartmentService {

    @Resource
    private DrpServiceImpl drpService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public List<WhutDepartment> listByLevelAndIsSchoolData(Integer level, int isSchoolData) {
        return this.list(Wrappers.<WhutDepartment>lambdaQuery()
                .eq(Objects.nonNull(level), WhutDepartment::getLevel, level)
                .eq(WhutDepartment::getIsSchoolData, isSchoolData));
    }

    @Override
    public List<WhutDepartment> listByLevelAndDwmc(int level, Set<String> designatedDeptSet) {
        return this.list(Wrappers.<WhutDepartment>lambdaQuery().eq(WhutDepartment::getLevel, level).in(WhutDepartment::getDwmc, designatedDeptSet));
    }

    // 批量大小
    private static final int BATCH_SIZE = 5000;
    // 每页数据量
    private static final long PAGE_SIZE = 1500L;
    // 并发获取数据的线程数
    private static final int FETCH_THREADS = 5;

    @Override
    public void syncData() {
        long startTime = System.currentTimeMillis();
        log.info("开始同步单位基本信息");

        // 获取第一页数据，主要是为了获取总页数
        ViewEchoCharlieQo firstPageQo = new ViewEchoCharlieQo();
        firstPageQo.setPer_page(PAGE_SIZE);
        firstPageQo.setPage(1L);
        ResponseSet<ViewEchoCharlieResponseSet> firstPageResponse = drpService.viewEchoCharlie(firstPageQo);
        if (ObjectUtil.isNull(firstPageResponse)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("同步单位基本信息错误");
        }

        ViewEchoCharlieResponseSet firstPageResult = firstPageResponse.getResult();
        long maxPage = firstPageResult.getMax_page();
        log.info("单位基本信息总页数: {}", maxPage);

        // 创建线程池用于并行获取数据
        ExecutorService fetchExecutor = Executors.newFixedThreadPool(FETCH_THREADS);
        List<Future<List<WhutDepartment>>> futures = new ArrayList<>();

        // 处理第一页数据
        List<WhutDepartment> firstPageData = processPageData(firstPageResult.getData());

        // 并行获取其他页数据
        for (long pageIndex = 2; pageIndex <= maxPage; pageIndex++) {
            final long currentPage = pageIndex;
            futures.add(fetchExecutor.submit(() -> fetchPageData(currentPage)));
        }

        // 收集所有页的数据
        List<WhutDepartment> allData = new ArrayList<>(firstPageData);
        for (Future<List<WhutDepartment>> future : futures) {
            try {
                List<WhutDepartment> pageData = future.get();
                if (CollUtil.isNotEmpty(pageData)) {
                    allData.addAll(pageData);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("获取单位数据异常", e);
                Thread.currentThread().interrupt();
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("获取单位数据异常");
            }
        }

        // 关闭线程池
        fetchExecutor.shutdown();
        try {
            if (!fetchExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                fetchExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            fetchExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("成功获取单位数据，总数量: {}, 耗时: {}ms", allData.size(), System.currentTimeMillis() - startTime);

        // 清空表并批量保存数据
        long saveStartTime = System.currentTimeMillis();

        // 设置事务超时时间为30分钟
        transactionTemplate.setTimeout(1800); // 30分钟 = 1800秒

        // 先清空表（单独事务）
        transactionTemplate.execute(status -> {
            try {
                this.baseMapper.truncate();
                log.info("清空单位表完成");
                return null;
            } catch (Exception e) {
                log.error("清空单位表异常", e);
                status.setRollbackOnly();
                throw e;
            }
        });

        // 分批保存数据（每批一个事务）
        if (CollUtil.isNotEmpty(allData)) {
            int total = allData.size();
            int batchCount = (total + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

            for (int i = 0; i < batchCount; i++) {
                final int batchIndex = i;
                transactionTemplate.execute(status -> {
                    try {
                        int fromIndex = batchIndex * BATCH_SIZE;
                        int toIndex = Math.min((batchIndex + 1) * BATCH_SIZE, total);
                        List<WhutDepartment> batch = allData.subList(fromIndex, toIndex);

                        this.saveBatch(batch, BATCH_SIZE);
                        log.info("保存单位数据进度: {}/{}, 当前批次: {}", toIndex, total, batchIndex + 1);
                        return null;
                    } catch (Exception e) {
                        log.error("保存单位数据异常，批次: {}", batchIndex + 1, e);
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            }
        }

        log.info("同步单位基本信息完成，总耗时: {}ms, 数据保存耗时: {}ms",
                System.currentTimeMillis() - startTime, System.currentTimeMillis() - saveStartTime);
    }

    /**
     * 获取指定页的数据
     */
    private List<WhutDepartment> fetchPageData(long pageIndex) {
        try {
            ViewEchoCharlieQo viewEchoCharlieQo = new ViewEchoCharlieQo();
            viewEchoCharlieQo.setPer_page(PAGE_SIZE);
            viewEchoCharlieQo.setPage(pageIndex);

            ResponseSet<ViewEchoCharlieResponseSet> response = drpService.viewEchoCharlie(viewEchoCharlieQo);
            if (ObjectUtil.isNull(response)) {
                log.error("获取单位数据失败，页码: {}", pageIndex);
                return Collections.emptyList();
            }

            ViewEchoCharlieResponseSet result = response.getResult();
            List<ViewEchoCharlieResponseSet.Info> data = result.getData();
            log.info("成功获取单位数据，页码: {}, 数量: {}", pageIndex, data.size());

            return processPageData(data);
        } catch (Exception e) {
            log.error("获取单位数据异常，页码: {}", pageIndex, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理页面数据，转换为实体对象
     */
    private List<WhutDepartment> processPageData(List<ViewEchoCharlieResponseSet.Info> data) {
        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return Optional.ofNullable(data).orElseGet(Collections::emptyList)
                .stream()
                .map(info -> BeanUtil.copyProperties(info, WhutDepartment.class))
                .collect(Collectors.toList());
    }
}

