package com.lanshan.base.commonservice.standardapp.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.converter.StdLessonTimeConverter;
import com.lanshan.base.commonservice.standardapp.dto.CourseCardDataDTO;
import com.lanshan.base.commonservice.standardapp.dto.StdCalenderWeekDTO;
import com.lanshan.base.commonservice.standardapp.entity.StdLessonTime;
import com.lanshan.base.commonservice.standardapp.qo.*;
import com.lanshan.base.commonservice.standardapp.service.StdCourseScheduleService;
import com.lanshan.base.commonservice.standardapp.service.StdLessonTimeService;
import com.lanshan.base.commonservice.standardapp.service.StdTeachCalenderService;
import com.lanshan.base.commonservice.standardapp.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 课程安排信息表(StdCourseSchedule)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/course/schedule")
@Api(tags = "课程表API")
public class StdCourseScheduleController {
    /**
     * 服务对象
     */
    @Resource
    private StdCourseScheduleService stdCourseScheduleService;

    @Resource
    private StdTeachCalenderService stdTeachCalendarService;

    @Resource
    private StdLessonTimeService stdLessonTimeService;

    @GetMapping("/info")
    @ApiOperation("查询课程表")
    public Result<List<CourseWeekVO>> getTermCourse(@RequestParam String teachYear, @RequestParam Integer term) {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(stdCourseScheduleService.getTermCourse(userId, teachYear, term));
    }

    @GetMapping("/info/test")
    @ApiOperation("查询课程表测试")
    public Result<List<CourseWeekVO>> getTermCourseTest(@RequestParam String userId, @RequestParam String teachYear, @RequestParam Integer term, @RequestParam(required = false) Integer weekNo) {
        return Result.build(stdCourseScheduleService.getTermCourse(userId, teachYear, term, weekNo));
    }

    @GetMapping("/info/ai")
    @ApiOperation("查询指定日期课程表")
    public Result<List<StdCourseScheduleVO>> getTermCourseAi(@RequestParam String userId, @RequestParam(required = false) String date) {
        return Result.build(stdCourseScheduleService.getTermCourse(userId, date));
    }

    @GetMapping("/info/today/soon")
    @ApiOperation("查询用户当日最近的课程")
    public Result<List<StdCourseScheduleVO>> getTermCourseToday(@RequestParam String userId) {
        return Result.build(stdCourseScheduleService.getTermCourseTodaySoon(userId));
    }

    @GetMapping("/calendar")
    @ApiOperation("查询校历信息")
    public Result<List<StdTeachCalenderVO>> getTeachCalendar(@RequestParam String teachYear,
                                                             @RequestParam(required = false) Integer term,
                                                             @RequestParam(required = false) Integer weekNo) {
        return Result.build(stdTeachCalendarService.getTeachCalendar(teachYear, term, weekNo));
    }

    @GetMapping("/lesson/time")
    @ApiOperation("查询课程节次时间信息")
    @Cacheable(value = "course:lesson:time")
    public Result<List<StdLessonTimeVO>> getLessonTime() {
        List<StdLessonTime> list = stdLessonTimeService.list(
                new LambdaQueryWrapper<StdLessonTime>()
                        .orderByAsc(StdLessonTime::getCampusAreaId, StdLessonTime::getLessonNo)
        );
        List<StdLessonTimeVO> vos = StdLessonTimeConverter.INSTANCE.toVO(list);
        return Result.build(vos);
    }


    @PostMapping("init/calendar")
    @ApiOperation("初始化校历数据")
    public Result<Boolean> initTeachCalendar(@RequestParam Integer year) {
        stdTeachCalendarService.initData(year);
        return Result.build(true);
    }

    @PostMapping("update/term")
    @ApiOperation("更新学期和周信息")
    public Result<Boolean> updateWeekTerm(@RequestParam String teachYear) {
        stdTeachCalendarService.updateWeekOfTerm(teachYear);
        return Result.build(true);
    }

    @PostMapping("set/user/course/card/data")
    @ApiOperation("设置指定用户的课程卡片数据")
    public Result<Boolean> setUserCourseCardData(@RequestBody CourseCardDataDTO dto) {
        stdCourseScheduleService.updateUserWebViewData(dto.getUserId(), dto.getUrl(), dto.getAgentId());
        return Result.build(true);
    }

    @PostMapping("set/all/user/course/card/data")
    @ApiOperation("批量设置所有用户的课程卡片数据")
    public Result<Boolean> setAllUserCourseCardData(@RequestBody CourseCardDataDTO dto) {
        stdCourseScheduleService.updateAllUserCourseCardUrl(dto.getUrl(), dto.getAgentId());
        return Result.build(true);
    }

    @GetMapping("/getCurrentUserType")
    @ApiOperation("获取当前用户类型")
    public Result<String> getCurrentUserType() {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(stdCourseScheduleService.getCurrentUserType(userId));
    }

    @GetMapping("/listAllPermitSetting")
    @ApiOperation("获取所有设置信息")
    public Result<List<CourseSettingVO>> listAllPermitSetting() {
        return Result.build(stdCourseScheduleService.listAllPermitSetting());
    }

    @GetMapping("/getClassTree")
    @ApiOperation("获取院系班级树形数据")
    public Result<List<ClassTreeVO>> getClassTree(String type) {
        return Result.build(stdCourseScheduleService.getClassTree(type));
    }

    @PostMapping("/listIdleRateInfo")
    @ApiOperation("获取空闲率信息")
    public Result<Map<String, Map<String, List<ClassIdleRateInfoVO>>>> listIdleRateInfo(@Validated @RequestBody IdleRateInfoQO idleRateInfoQO) {
        return Result.build(stdCourseScheduleService.listIdleRateInfo(idleRateInfoQO));
    }

    @PostMapping("/stdCourseScheduleList")
    @ApiOperation("获取教学班列表")
    public Result<IPage<StdCourseScheduleVO>> stdCourseScheduleList(@Validated @RequestBody StdCourseScheduleListQo stdCourseScheduleListQo) {
        return Result.build(stdCourseScheduleService.getStdCourseScheduleList(stdCourseScheduleListQo));
    }

    @PostMapping("/getCourseStudentList")
    @ApiOperation("获取课表源数据列表")
    public Result<IPage<CourseStudentVO>> getCourseStudentList(@Validated @RequestBody CourseStudentQueryQo courseStudentQueryQo) {
        return Result.build(stdCourseScheduleService.getCourseStudentList(courseStudentQueryQo));
    }

    @PostMapping("/getCourseGroupChatList")
    @ApiOperation("获取课程群聊列表")
    public Result<IPage<CourseGroupChatVO>> getCourseGroupChatList(@Validated @RequestBody CourseGroupChatQo courseGroupChatQo) {
        return Result.build(stdCourseScheduleService.getCourseGroupChatList(courseGroupChatQo));
    }

    @ApiOperation("导出课程群建群失败原因")
    @PostMapping(value = "exportCourseGroupChatErrorList")
    public void exportCourseGroupChatErrorList(@Validated @RequestBody CourseGroupChatQo courseGroupChatQo, HttpServletResponse response) throws IOException {
        stdCourseScheduleService.exportCourseGroupChatErrorList(courseGroupChatQo, response);
    }

    @ApiOperation("获取课程群聊详情")
    @GetMapping(value = "getCourseGroupChatDetail")
    public Result<CourseGroupChatVO> getCourseGroupChatDetail(String id) {
        return Result.build(stdCourseScheduleService.getCourseGroupChatDetail(id));
    }

    @ApiOperation("获取所有课程列表")
    @GetMapping("/getAllCourse")
    public Result<List<CourseListVO>> getAllCourse() {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(stdCourseScheduleService.getAllCourse(userId));
    }

    @ApiOperation("获取课程列表周信息")
    @GetMapping("/listCourseListWeek")
    public Result<List<CourseListWeekVO>> listCourseListWeek(String courseId, Integer weekNo) {
        return Result.build(stdCourseScheduleService.listCourseListWeek(courseId, weekNo));
    }

    @ApiOperation("获取单个课程信息")
    @GetMapping("/getCourse")
    public Result<CourseListVO> getCourse(String courseId) {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(stdCourseScheduleService.getCourse(userId, courseId));
    }

    @ApiOperation("添加在线课程")
    @PostMapping("/addCourseLive")
    public Result<Boolean> addCourseLive(@RequestBody AddCourseLiveQO qo) {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(stdCourseScheduleService.addCourseLive(userId, qo));
    }

    @ApiOperation("获取课程直播信息")
    @PostMapping("/getCourseLiveInfo")
    public Result<StdCourseStudentVO> getCourseLiveInfo() {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(stdCourseScheduleService.getCourseLiveInfo(userId));
    }

    @ApiOperation("发送群聊消息")
    @PostMapping("/sendGroupMessage")
    public Result<Boolean> sendGroupMessage(@RequestBody SendGroupMessageQO qo) {
        stdCourseScheduleService.sendGroupMessage(qo);
        return Result.build(true);
    }

    @ApiOperation("获取课程安排详情")
    @GetMapping("/getCourseScheduleDetail")
    public Result<StdCourseScheduleDetailVO> getCourseScheduleDetail(String id) {
        return Result.build(stdCourseScheduleService.getCourseScheduleDetail(id));
    }

    @ApiOperation("从原数据拉取教学班数据")
    @GetMapping("/updateCourseSchedule")
    public Result<Boolean> updateCourseSchedule() {
        stdCourseScheduleService.updateCourseSchedule();
        return Result.build(true);
    }

    @ApiOperation("从中间库拉取课表源数据")
    @GetMapping("/updateCourseStudent")
    public Result<Boolean> updateCourseStudent() {
        stdCourseScheduleService.updateCourseStudent();
        return Result.build(true);
    }

    @ApiOperation("获取学年列表")
    @GetMapping("/getTeachYears")
    public Result<List<String>> getTeachYears() {
        return Result.build(stdCourseScheduleService.getTeachYears());
    }

    @ApiOperation("批量更新群聊")
    @GetMapping("/batchUpdateGroupChat")
    public Result<Boolean> batchUpdateGroupChat() {
        stdCourseScheduleService.batchUpdateGroupChat();
        return Result.build(true);
    }

    @ApiOperation("获取课程直播列表")
    @GetMapping("/getCourseLiveList")
    public Result<List<StdCourseStudentVO>> getCourseLiveList(String courseId) {
        return Result.build(stdCourseScheduleService.getCourseLiveList(courseId));
    }

    /**
     * 获取所有可用的(学年, 学期)组合
     * <AUTHOR> yang.
     * @since 2025/5/8 16:43
     */
    @PostMapping(value = "/getYearAndTermList",produces = "application/json;charset=UTF-8")
    public Result<List<StdCalenderTermVO>> getYearAndTermList() {
        List<StdCalenderTermVO> list =  stdTeachCalendarService.getYearAndTermList();
        return Result.build(list);
    }

    /**
     * 根据学年+学期，查询该学期的所有周次
     * <AUTHOR> yang.
     * @since 2025/5/8 16:43
     */
    @PostMapping(value = "/getWeekListByYearAndTerm",produces = "application/json;charset=UTF-8")
    public Result<List<StdCalenderWeekVO>> getWeekListByYearAndTerm(@RequestBody StdCalenderWeekDTO dto) {
        List<StdCalenderWeekVO> list =  stdTeachCalendarService.getWeekListByYearAndTerm(dto);
        return Result.build(list);
    }

    /**
     * 根据学年+学期+周次，查询该周次范围内的日期
     * <AUTHOR> yang.
     * @since 2025/5/8 17:15
     * @return yyyy-mm-dd格式字符串集合
     */
    @PostMapping(value = "/getRangeDayByWeek",produces = "application/json;charset=UTF-8")
    public Result<List<String>> getRangeDayByWeek(@RequestBody StdCalenderWeekDTO dto) {
        List<String> list =  stdTeachCalendarService.getRangeDayByWeek(dto);
        return Result.build(list);
    }

}

