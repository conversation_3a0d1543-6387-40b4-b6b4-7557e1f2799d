package com.lanshan.base.commonservice.visitor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.visitor.convert.VisitorEntourageConvert;
import com.lanshan.base.commonservice.visitor.entity.VisitorEntourage;
import com.lanshan.base.commonservice.visitor.enums.VisitorCertificateTypeEnum;
import com.lanshan.base.commonservice.visitor.mapper.VisitorEntourageMapper;
import com.lanshan.base.commonservice.visitor.service.VisitorEntourageService;
import com.lanshan.base.commonservice.visitor.vo.VisitorEntourageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 访客随行人员表(VisitorEntourage)服务实现类
 */
@Slf4j
@Service
public class VisitorEntourageServiceImpl extends ServiceImpl<VisitorEntourageMapper, VisitorEntourage> implements VisitorEntourageService {

    @Override
    public List<VisitorEntourageVo> listEntourage(Long recordId) {
        LambdaQueryWrapper<VisitorEntourage> queryWrapper = Wrappers.lambdaQuery(VisitorEntourage.class);
        queryWrapper.eq(VisitorEntourage::getRecordId, recordId);
        //查询访客随行人员
        List<VisitorEntourage> list = super.list(queryWrapper);
        if(CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<VisitorEntourageVo> voList = VisitorEntourageConvert.INSTANCE.toVo(list);
        for (VisitorEntourageVo vo : voList) {
            //设置证件类型
            vo.setCertificateTypeDesc(EnumUtil.getFieldBy(VisitorCertificateTypeEnum::getMsg, VisitorCertificateTypeEnum::getCode, vo.getCertificateType()));
        }

        return voList;
    }

}
