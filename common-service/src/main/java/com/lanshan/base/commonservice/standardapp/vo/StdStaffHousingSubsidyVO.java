package com.lanshan.base.commonservice.standardapp.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 教工住房补贴
 * (StaffHousingSubsidy)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "教工住房补贴VO")
@Data
@ToString
public class StdStaffHousingSubsidyVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "工号")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "补贴日期")
    private String subsidyDate;

    @ApiModelProperty(value = "金额")
    private String amount;

    @ApiModelProperty(value = "发放时间")
    private String issueTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

