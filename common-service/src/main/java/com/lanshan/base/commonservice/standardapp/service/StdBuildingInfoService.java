package com.lanshan.base.commonservice.standardapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standardapp.entity.StdBuildingInfo;
import com.lanshan.base.commonservice.standardapp.vo.StdBuildingInfoVO;

import java.util.List;

/**
 * 楼栋信息(StdBuildingInfo)表服务接口
 */
public interface StdBuildingInfoService extends IService<StdBuildingInfo> {

    /**
     * 查询楼栋信息列表
     * @return 楼栋信息列表
     */
    List<StdBuildingInfoVO> listBuildingInfo();

}

