package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 本科生基础信息表(WhutUndergraduate)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "本科生基础信息表VO")
@Data
@ToString
public class WhutUndergraduateVO implements Serializable {

    @ApiModelProperty(value = "自增主键ID")
    private Long id;

    @ApiModelProperty(value = "企业微信用户id")
    private String qywxUserId;

    @ApiModelProperty(value = "学号")
    private String xh;

    @ApiModelProperty(value = "考生号")
    private String ksh;

    @ApiModelProperty(value = "姓名")
    private String xm;

    @ApiModelProperty(value = "外文姓名")
    private String wwxm;

    @ApiModelProperty(value = "姓名拼音")
    private String xmpy;

    @ApiModelProperty(value = "曾用名")
    private String cym;

    @ApiModelProperty(value = "性别码")
    private String xbm;

    @ApiModelProperty(value = "性别码代码名称")
    private String xbmdmmc;

    @ApiModelProperty(value = "出生日期")
    private Date csrq;

    @ApiModelProperty(value = "出生地")
    private String csd;

    @ApiModelProperty(value = "籍贯")
    private String jg;

    @ApiModelProperty(value = "民族")
    private String mz;

    @ApiModelProperty(value = "国籍/地区码")
    private String gjdqm;

    @ApiModelProperty(value = "身份证件类型")
    private String sfzjlx;

    @ApiModelProperty(value = "身份证件号")
    private String sfzjh;

    @ApiModelProperty(value = "身份证件有效期")
    private String sfzjyxq;

    @ApiModelProperty(value = "婚姻状况码")
    private String hyzkm;

    @ApiModelProperty(value = "港澳台侨外码")
    private String gatqwm;

    @ApiModelProperty(value = "政治面貌")
    private String zzmm;

    @ApiModelProperty(value = "健康状况")
    private String jkzk;

    @ApiModelProperty(value = "信仰宗教码")
    private String xyzjm;

    @ApiModelProperty(value = "血型码")
    private String xxm;

    @ApiModelProperty(value = "是否独生子女")
    private String sfdszn;

    @ApiModelProperty(value = "来源地区")
    private String lydq;

    @ApiModelProperty(value = "学生来源")
    private String xsly;

    @ApiModelProperty(value = "户口性质")
    private String hkxz;

    @ApiModelProperty(value = "户口所在地")
    private String hkszd;

    @ApiModelProperty(value = "邮政编码")
    private String yzbm;

    @ApiModelProperty(value = "通讯地址")
    private String txdz;

    @ApiModelProperty(value = "联系电话")
    private String lxdh;

    @ApiModelProperty(value = "电子邮箱")
    private String dzyx;

    @ApiModelProperty(value = "即时通讯号")
    private String jstxh;

    @ApiModelProperty(value = "校园卡号")
    private String yktzh;
}

