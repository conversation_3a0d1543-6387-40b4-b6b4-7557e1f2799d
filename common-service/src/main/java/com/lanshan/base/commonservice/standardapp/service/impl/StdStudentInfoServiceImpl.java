package com.lanshan.base.commonservice.standardapp.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.standardapp.converter.StdStudentInfoConverter;
import com.lanshan.base.commonservice.standardapp.dao.StdStudentInfoDao;
import com.lanshan.base.commonservice.standardapp.entity.StdStudentInfo;
import com.lanshan.base.commonservice.standardapp.service.StdStudentInfoService;
import com.lanshan.base.commonservice.standardapp.vo.ClassTreeVO;
import com.lanshan.base.commonservice.standardapp.vo.StdStudentInfoVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 课表-学生信息表(StdStudentInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdStudentInfoService")
public class StdStudentInfoServiceImpl extends ServiceImpl<StdStudentInfoDao, StdStudentInfo> implements StdStudentInfoService {

    @Override
    public List<ClassTreeVO> getClassTree(String type) {
        if ("2".equalsIgnoreCase(type)) {
            return baseMapper.getTeachClassTree();
        }
        return baseMapper.getClassTree();
    }

    @Override
    public Map<String, List<StdStudentInfoVO>> getClassMapByClass(List<String> classNumbers) {
        List<StdStudentInfo> list = this.list(Wrappers.<StdStudentInfo>lambdaQuery().in(StdStudentInfo::getClassNumber, classNumbers));
        return Optional.ofNullable(list).orElseGet(ArrayList::new).stream().map(StdStudentInfoConverter.INSTANCE::toVO)
                .collect(Collectors.groupingBy(StdStudentInfoVO::getClassNumber));
    }
}

