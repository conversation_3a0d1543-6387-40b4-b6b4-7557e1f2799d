package com.lanshan.base.commonservice.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "common.double-check")
public class DoubleCheckProperties {

    private Long newEnrollmentStuDept;

    /**
     * 是否发送欢迎信息
     */
    private Boolean msgAndJoinGroupFlag;

    /**
     * 是否只加入群
     */
    private Boolean onlyJoinGroupFlag;

    /**
     * 是否检查报道
     */
    private Boolean checkReportFlag;
}
