package com.lanshan.base.commonservice.welcomenewstudent.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 新生基本信息采集表查询对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "新生基本信息采集表查询参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class NewStudentInfoCollectionQO extends PageQo {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "新生基本信息表ID")
    private Long newStudentDataId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "民族")
    private String nationality;

    @ApiModelProperty(value = "出生日期")
    private Date birthDate;

    @ApiModelProperty(value = "身份证号码")
    private String idCardNumber;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "常住地址")
    private String permanentAddress;

    @ApiModelProperty(value = "家长联系方式信息")
    private Object parentContactInfo;

    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    @ApiModelProperty(value = "户口所在地")
    private String householdAddress;

    @ApiModelProperty(value = "学生类别")
    private String studentCategory;

    @ApiModelProperty(value = "是否有意愿参加意外伤害保险")
    private Boolean accidentInsuranceFlag;

    @ApiModelProperty(value = "身高")
    private String height;

    @ApiModelProperty(value = "体重")
    private String weight;

    @ApiModelProperty(value = "鞋码")
    private String shoeSize;

    @ApiModelProperty(value = "日常用品")
    private String dailyNecessities;
} 