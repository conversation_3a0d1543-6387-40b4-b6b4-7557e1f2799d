package com.lanshan.base.commonservice.workbench.vo;


import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用企业微信设置信息表(WbAppWxset)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "应用企业微信设置信息表VO")
@Data
@ToString
public class WbAppWxsetVO implements Serializable {

    private static final long serialVersionUID = 6186492446446604246L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "企业微信应用ID")
    private Integer agentId;

    @ApiModelProperty(value = "应用在企业微信的设置")
    private JSONObject configSet;

    @ApiModelProperty(value = "是否默认展示。否的话就是自定义展示模板")
    private Boolean isShowDefault;

    @ApiModelProperty(value = "应用展示模板设置")
    private JSONObject showTemplateSet;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

