package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.dao.StdStudentChargeInfoDao;
import com.lanshan.base.commonservice.standardapp.entity.StdStudentChargeInfo;
import com.lanshan.base.commonservice.standardapp.service.StdStudentChargeInfoService;
import org.springframework.stereotype.Service;

/**
 * 学生收费统计查询表(StdStudentChargeInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdStudentChargeInfoService")
public class StdStudentChargeInfoServiceImpl extends ServiceImpl<StdStudentChargeInfoDao, StdStudentChargeInfo> implements StdStudentChargeInfoService {

    @Override
    public StdStudentChargeInfo getStdStudentChargeInfo() {
        String userId = SecurityContextHolder.getUserId();
        LambdaQueryWrapper<StdStudentChargeInfo> qw = new LambdaQueryWrapper<>();
        qw.eq(StdStudentChargeInfo::getUserId, userId);
        StdStudentChargeInfo stdStudentChargeInfo = this.getOne(qw);
        if (ObjectUtil.isNull(stdStudentChargeInfo)) {
            return null;
        }
        return stdStudentChargeInfo;
    }
}

