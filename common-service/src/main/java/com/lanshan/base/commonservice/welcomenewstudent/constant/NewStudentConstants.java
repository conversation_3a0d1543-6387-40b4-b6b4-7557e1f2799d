package com.lanshan.base.commonservice.welcomenewstudent.constant;

/**
 * 新生相关常量定义
 *
 * <AUTHOR>
 */
public class NewStudentConstants {

    /**
     * 新生任务ID常量
     */
    public static class TaskId {
        /**
         * 基本信息完成任务ID
         */
        public static final Long BASIC_INFO_COMPLETED = 2L;

        /**
         * 交通信息完成任务ID
         */
        public static final Long TRAFFIC_INFO_COMPLETED = 3L;

        /**
         * 签到任务ID
         */
        public static final Long CHECK_IN = 4L;
    }

    /**
     * 缓存相关常量
     */
    public static class Cache {
        /**
         * 数据大屏缓存键前缀
         */
        public static final String DASHBOARD_CACHE_PREFIX = "dashboard:new_student:";

        /**
         * 缓存过期时间（秒）- 5分钟
         */
        public static final long CACHE_EXPIRE_SECONDS = 300L;
    }

    /**
     * 性别常量
     */
    public static class Gender {
        public static final String MALE = "男";
        public static final String FEMALE = "女";
    }

    /**
     * 默认值常量
     */
    public static class Default {
        /**
         * 最近签到查询默认条数
         */
        public static final Integer RECENT_CHECK_IN_LIMIT = 100;
    }
} 