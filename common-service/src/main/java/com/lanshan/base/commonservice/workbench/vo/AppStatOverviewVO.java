package com.lanshan.base.commonservice.workbench.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 应用统计概览VO
 */
@Data
@ApiModel(value = "应用统计概览VO", description = "应用统计概览VO")
public class AppStatOverviewVO {

    @ApiModelProperty(value = "用户使用次数")
    private Integer todayUserUsed;

    @ApiModelProperty(value = "学生使用次数")
    private Integer todayStuUsed;

    @ApiModelProperty(value = "老师使用次数")
    private Integer todayTchUsed;

    @ApiModelProperty(value = "用户点击次数")
    private Integer todayUserClick;

    @ApiModelProperty(value = "学生点击次数")
    private Integer todayStuClick;

    @ApiModelProperty(value = "教师点击次数")
    private Integer todayTchClick;

    @ApiModelProperty(value = "用户当月点击次数")
    private Integer userMonthClick;

    @ApiModelProperty(value = "学生当月点击次数")
    private Integer stuMonthClick;

    @ApiModelProperty(value = "教师当月点击次数")
    private Integer tchMonthClick;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "当月使用人数")
    private Integer monthUsed;

    @ApiModelProperty(value = "当月学生使用人数")
    private Integer stuMonthUsed;

    @ApiModelProperty(value = "当月教师使用人数")
    private Integer tchMonthUsed;

    @ApiModelProperty(value = "学生本科生使用人数")
    private Integer todayStuUndergraduateUsed;

    @ApiModelProperty(value = "学生研究生使用人数")
    private Integer todayStuMasterUsed;

    @ApiModelProperty(value = "其他使用人数")
    private Integer todayOtherUsed;

    @ApiModelProperty(value = "学生本科生点击次数")
    private Integer todayStuUndergraduateClick;

    @ApiModelProperty(value = "学生研究生点击次数")
    private Integer todayStuMasterClick;

    @ApiModelProperty(value = "其他点击次数")
    private Integer todayOtherClick;

    @ApiModelProperty(value = "本科生当月使用人数")
    private Integer stuUndergraduateMonthUsed;

    @ApiModelProperty(value = "本科生当月点击次数")
    private Integer stuUndergraduateMonthClick;

    @ApiModelProperty(value = "研究生当月使用人数")
    private Integer stuMasterMonthUsed;

    @ApiModelProperty(value = "研究生当月点击次数")
    private Integer stuMasterMonthClick;

    @ApiModelProperty(value = "其他当月使用人数")
    private Integer otherMonthUsed;

    @ApiModelProperty(value = "其他当月点击次数")
    private Integer otherMonthClick;
}
