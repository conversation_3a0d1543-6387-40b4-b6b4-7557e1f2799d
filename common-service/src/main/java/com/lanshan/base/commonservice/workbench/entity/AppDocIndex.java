package com.lanshan.base.commonservice.workbench.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 * 应用信息索引文件实体
 */
@Data
@Document(indexName = "#{@appIndexName}")
public class AppDocIndex implements Serializable {

    private static final long serialVersionUID = 7890605835660490480L;
    @Id
    @Field(type = FieldType.Long)
    private Long id;
    /**
     * 企业微信中的应用ID
     */
    @Field(type = FieldType.Integer)
    private Integer agentId;
    /**
     * 应用名称
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String appName;

    /**
     * 应用名称拼音。搜索权重次于名称
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String appNamePy;

    /**
     * 应用类型。1：企业微信应用；2.一张表应用；
     */
    @Field(type = FieldType.Text)
    private String appType;

    /**
     * 是否是小程序
     */
    @Field(type = FieldType.Boolean)
    private Boolean isMiniProgram;

    /**
     * 小程序ID，如果应用类型是微信小程序。
     */
    @Field(type = FieldType.Text)
    private String miniProgramId;

    /**
     * logo地址
     */
    @Field(type = FieldType.Text)
    private String logoUrl;
    /**
     * 应用主页地址
     */
    @Field(type = FieldType.Text)
    private String homeUrl;

    /**
     * 应用标题关键字。搜索权重仅次于名称
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String seoTitle;
    /**
     * 应用搜索关键字。与应用相关的词，搜索权重次于name, title
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String seoKeyword;
    /**
     * 应用描述。主要用于搜索
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String description;

    /**
     * 应用描述拼音。搜索权重次于描述
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String descriptionPy;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = {}, pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'+08:00' || strict_date_optional_time || epoch_millis")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 备注
     */
    @Field(type = FieldType.Text, analyzer = "hanlp_index")
    private String remark;

    /**
     * 应用所属类目。1： 普通应用； 2：应用专区
     */
    @Field(type = FieldType.Text)
    private String appCatalog;

    /**
     * 应用关联的应用标签
     */
    @Field(type = FieldType.Text, analyzer = "hanlp")
    private String tagNames;

    /**
     * 应用关联的应用标签拼音
     */
    @Field(type = FieldType.Text, analyzer = "hanlp")
    private String tagNamesPy;
}
