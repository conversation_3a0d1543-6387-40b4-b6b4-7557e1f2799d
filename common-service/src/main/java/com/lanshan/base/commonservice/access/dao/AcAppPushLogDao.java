package com.lanshan.base.commonservice.access.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.access.dto.AcAppPushLogGroupCountDto;
import com.lanshan.base.commonservice.access.entity.AcAppPushLog;
import com.lanshan.base.commonservice.access.qo.AcAppPushLogCountQO;
import com.lanshan.base.commonservice.access.vo.AcPushLogTodayCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 应用数据推送日志(AcAppPushLog)数据库访问层
 */

@Mapper
public interface AcAppPushLogDao extends BaseMapper<AcAppPushLog> {

    /**
     * 查询当天推送日志次数
     * @param param 查询条件
     * @return 当天推送日志次数
     */
    List<AcPushLogTodayCountVO> listTodayPushCount(@Param("param") AcAppPushLogCountQO param);

    /**
     * 查询推送日志分组次数
     * @param date 日期
     * @return 推送日志分组次数
     */
    List<AcAppPushLogGroupCountDto> listPushLogGroupCount(Date date);
}

