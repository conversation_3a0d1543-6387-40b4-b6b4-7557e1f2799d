package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.commonservice.inviteoutside.pojo.vo.OutsiderInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2024/7/4 16:19
 */
public class OutsiderInfoVOListJacksonTypeHandler extends AbsObjectJacksonTypeHandler<List<OutsiderInfoVO>> {
    @Override
    protected TypeReference<List<OutsiderInfoVO>> getTypeReference() {
        return new TypeReference<List<OutsiderInfoVO>>() {
        };
    }
}
