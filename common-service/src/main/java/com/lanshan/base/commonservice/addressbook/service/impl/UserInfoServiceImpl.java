package com.lanshan.base.commonservice.addressbook.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.addressbook.dao.UserInfoDao;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 用户信息列表(UserInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("userInfoService")
public class UserInfoServiceImpl extends ServiceImpl<UserInfoDao, UserInfo> implements UserInfoService {

    /**
     * @param idCard 身份证号（密文）
     * @return 用户信息列表
     */
    @Override
    public List<UserInfo> getUserInfoByIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return Collections.emptyList();
        }
        return super.list(new LambdaQueryWrapper<UserInfo>()
                .eq(UserInfo::getIdentityNumber, idCard));
    }
}

