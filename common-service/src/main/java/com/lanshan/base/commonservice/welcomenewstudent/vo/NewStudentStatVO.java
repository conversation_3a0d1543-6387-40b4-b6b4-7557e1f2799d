package com.lanshan.base.commonservice.welcomenewstudent.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生统计数据
 */
@ApiModel("新生统计数据")
@Data
public class NewStudentStatVO {

    /**
     * 学院编号
     */
    @ApiModelProperty("学院编号")
    private String collegeCode;

    /**
     * 学院名称
     */
    @ApiModelProperty("学院名称")
    private String collegeName;

    /**
     * 新生总数
     */
    @ApiModelProperty("新生总数")
    private Integer total;

    /**
     * 认证总数
     */
    @ApiModelProperty("认证总数")
    private Integer authTotal;

    /**
     * 开通总数
     */
    @ApiModelProperty("开通总数")
    private Integer openTotal;

    /**
     * 签到总数
     */
    @ApiModelProperty("签到总数")
    private Integer checkInTotal;
}
