package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdStudentInfo;
import com.lanshan.base.commonservice.standardapp.vo.StdStudentInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 课表-学生信息表(StdStudentInfo)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdStudentInfoConverter {

    StdStudentInfoConverter INSTANCE = Mappers.getMapper(StdStudentInfoConverter.class);

    StdStudentInfoVO toVO(StdStudentInfo entity);

    StdStudentInfo toEntity(StdStudentInfoVO vo);

    List<StdStudentInfoVO> toVO(List<StdStudentInfo> entityList);

    List<StdStudentInfo> toEntity(List<StdStudentInfoVO> voList);
}


