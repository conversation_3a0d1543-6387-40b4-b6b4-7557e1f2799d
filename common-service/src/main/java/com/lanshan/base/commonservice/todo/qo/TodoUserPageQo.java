package com.lanshan.base.commonservice.todo.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("待办分页查询条件")
public class TodoUserPageQo extends PageQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("待办定义id")
    private Long todoDefId;

    @ApiModelProperty(value = "用户id")
    private String userid;

    @ApiModelProperty(value = "用户名称")
    private String name;

}

