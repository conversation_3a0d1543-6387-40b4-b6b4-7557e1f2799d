package com.lanshan.base.commonservice.message.entity;

import java.util.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息搜索历史(MsgSearchHistory)实体
 */
@Data
public class MsgSearchHistory implements Serializable {
    private static final long serialVersionUID = -56280847679188382L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("用户id")
    private String userid;

    @ApiModelProperty("搜索类型 1：关键字 2：应用")
    private Integer type;

    @ApiModelProperty("搜索内容")
    private String content;

    @ApiModelProperty("创建时间")
    private Date createDate;

    @ApiModelProperty("是否删除 0：否 1：是")
    private Integer isDeleted;
}

