package com.lanshan.base.commonservice.standardapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.standardapp.entity.StdReward;
import com.lanshan.base.commonservice.standardapp.vo.StdRewardYearMonthVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 酬金查询表(StdReward)表数据库访问层
 *
 * <AUTHOR>
 */
public interface StdRewardDao extends BaseMapper<StdReward> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<StdReward> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<StdReward> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<StdReward> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<StdReward> entities);

    /**
     * 获取年月列表
     *
     * @param userId 用户id
     * @return List<StdRewardYearMonthVO>
     */
    List<StdRewardYearMonthVO> getYearMonthList(String userId);
}

