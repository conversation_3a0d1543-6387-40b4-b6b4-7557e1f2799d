package com.lanshan.base.commonservice.workbench.dto;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 应用区域搜索DTO
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AppZoneSearchDTO", description = "应用区域搜索DTO")
@Data
public class AppZoneSearchDTO extends PageQo implements Serializable {
    private static final long serialVersionUID = 844761851021969861L;

    @ApiModelProperty(value = "搜索关键字")
    private String title;

    @ApiModelProperty(value = "应用分组id")
    private Long groupId;
}
