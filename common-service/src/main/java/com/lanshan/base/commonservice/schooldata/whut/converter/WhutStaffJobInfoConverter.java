package com.lanshan.base.commonservice.schooldata.whut.converter;


import com.lanshan.base.commonservice.schooldata.whut.entity.WhutStaffJobInfo;
import com.lanshan.base.commonservice.schooldata.whut.vo.WhutStaffJobInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 教职工党政职务信息表(WhutStaffJobInfo)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WhutStaffJobInfoConverter {

    WhutStaffJobInfoConverter INSTANCE = Mappers.getMapper(WhutStaffJobInfoConverter.class);

    WhutStaffJobInfoVO toVO(WhutStaffJobInfo entity);

    WhutStaffJobInfo toEntity(WhutStaffJobInfoVO vo);
    
    List<WhutStaffJobInfoVO> toVO(List<WhutStaffJobInfo> entityList);

    List<WhutStaffJobInfo> toEntity(List<WhutStaffJobInfoVO> voList);
}


