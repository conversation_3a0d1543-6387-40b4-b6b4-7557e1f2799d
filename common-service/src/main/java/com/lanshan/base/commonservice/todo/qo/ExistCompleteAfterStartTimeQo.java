package com.lanshan.base.commonservice.todo.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("查询操作开始时间后的待办是否完成")
public class ExistCompleteAfterStartTimeQo implements Serializable {
    private static final long serialVersionUID = 622855448848981657L;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("待办定义id")
    private Long todoDefId;

}

