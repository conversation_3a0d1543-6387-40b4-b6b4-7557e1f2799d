package com.lanshan.base.commonservice.welcomenewstudent.converter;


import com.lanshan.base.commonservice.welcomenewstudent.entity.StudentDormitory;
import com.lanshan.base.commonservice.welcomenewstudent.vo.StudentDormitoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生宿舍分配表(StudentDormitory)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StudentDormitoryConverter {

    StudentDormitoryConverter INSTANCE = Mappers.getMapper(StudentDormitoryConverter.class);

    StudentDormitoryVO toVO(StudentDormitory entity);

    StudentDormitory toEntity(StudentDormitoryVO vo);

    List<StudentDormitoryVO> toVO(List<StudentDormitory> entityList);

    List<StudentDormitory> toEntity(List<StudentDormitoryVO> voList);
}


