package com.lanshan.base.commonservice.schooldata.sxri.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.sxri.converter.SxriFdyClassXxConverter;
import com.lanshan.base.commonservice.schooldata.sxri.dao.SxriFdyClassXxDao;
import com.lanshan.base.commonservice.schooldata.sxri.entity.SxriFdyClassXx;
import com.lanshan.base.commonservice.schooldata.sxri.properties.SxriProperties;
import com.lanshan.base.commonservice.schooldata.sxri.qo.SxriFdyClassXxQO;
import com.lanshan.base.commonservice.schooldata.sxri.service.SxriFdyClassXxService;
import com.lanshan.base.commonservice.schooldata.sxri.vo.SxriFdyClassXxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 陕铁院辅导员班级信息服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SxriFdyClassXxServiceImpl extends ServiceImpl<SxriFdyClassXxDao, SxriFdyClassXx> implements SxriFdyClassXxService {

    @Resource
    private SxriProperties sxriProperties;

    @Override
    public IPage<SxriFdyClassXxVO> page(SxriFdyClassXxQO qo) {
        // 根据QO创建分页参数
        Page<SxriFdyClassXx> page = new Page<>(qo.getPage(), qo.getSize());

        LambdaQueryWrapper<SxriFdyClassXx> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        wrapper.eq(qo.getId() != null, SxriFdyClassXx::getId, qo.getId())
                .like(StringUtils.isNotBlank(qo.getJsbh()), SxriFdyClassXx::getJsbh, qo.getJsbh())
                .like(StringUtils.isNotBlank(qo.getBjdm()), SxriFdyClassXx::getBjdm, qo.getBjdm())
                .like(StringUtils.isNotBlank(qo.getBjmc()), SxriFdyClassXx::getBjmc, qo.getBjmc())
                .eq(StringUtils.isNotBlank(qo.getRylx()), SxriFdyClassXx::getRylx, qo.getRylx());

        IPage<SxriFdyClassXx> entityPage = this.page(page, wrapper);

        // 转换为VO
        IPage<SxriFdyClassXxVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        voPage.setRecords(SxriFdyClassXxConverter.INSTANCE.toVO(entityPage.getRecords()));

        return voPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(SxriFdyClassXxQO qo) {
        // 数据验证
        validateRequiredFields(qo);

        // 检查重复
        if (isDuplicateRecord(qo)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("该记录已存在");
        }

        SxriFdyClassXx entity = SxriFdyClassXxConverter.INSTANCE.toEntity(qo);
        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(SxriFdyClassXxQO qo) {
        if (qo.getId() == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("主键不能为空");
        }

        SxriFdyClassXx entity = this.getById(qo.getId());
        if (entity == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("记录不存在");
        }

        SxriFdyClassXx updateEntity = SxriFdyClassXxConverter.INSTANCE.toEntity(qo);
        return this.updateById(updateEntity);
    }

    @Override
    public SxriFdyClassXxVO getDetail(Long id) {
        if (id == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("主键不能为空");
        }

        SxriFdyClassXx entity = this.getById(id);
        if (entity == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("记录不存在");
        }

        return SxriFdyClassXxConverter.INSTANCE.toVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("主键集合不能为空");
        }

        return super.removeByIds(ids);
    }

    @Override
    public void syncFdyData() {
        log.info("开始同步陕铁院辅导员数据");
        syncDataByType("1", sxriProperties.getFdyUrl(), "辅导员");
    }

    @Override
    public void syncBzrData() {
        log.info("开始同步陕铁院班主任数据");
        syncDataByType("2", sxriProperties.getBzrUrl(), "班主任");
    }

    /**
     * 通用同步方法（非事务方法，内部控制事务粒度）
     *
     * @param rylx     人员类型 1-辅导员 2-班主任
     * @param url      接口地址
     * @param typeName 类型名称（用于日志）
     */
    private void syncDataByType(String rylx, String url, String typeName) {
        if (StringUtils.isBlank(url)) {
            log.warn("{}接口地址未配置，跳过同步", typeName);
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalCount = 0;

        try {
            // 第一步：从API获取数据（非事务操作）
            log.info("开始从API拉取{}数据", typeName);
            List<SxriFdyClassXx> allData = fetchDataFromApi(url, rylx, typeName);

            if (CollUtil.isEmpty(allData)) {
                log.info("没有{}数据需要同步", typeName);
                return;
            }

            totalCount = allData.size();
            log.info("从API拉取{}数据完成，共{}条", typeName, totalCount);

            // 清空指定类型的数据
            clearDataByType(rylx, typeName);

            // 批量插入新数据
            batchInsertData(allData, typeName);

            long endTime = System.currentTimeMillis();
            log.info("同步{}数据完成，共{}条，耗时{}ms", typeName, totalCount, (endTime - startTime));

        } catch (Exception e) {
            log.error("同步{}数据失败", typeName, e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("同步" + typeName + "数据失败：" + e.getMessage());
        }
    }


    /**
     * 从API获取数据（非事务方法）
     */
    private List<SxriFdyClassXx> fetchDataFromApi(String url, String rylx, String typeName) {
        List<SxriFdyClassXx> allData = new ArrayList<>();
        int page = 1;
        int pageSize = sxriProperties.getPageSize();
        int maxPages = 1000; // 最大页数限制，防止无限循环

        JSONObject requestBody = new JSONObject();
        JSONObject order = new JSONObject();
        order.put("jsbh", "DESC");
        requestBody.put("order", order);

        while (page <= maxPages) {
            log.info("正在拉取{}数据，第{}页", typeName, page);

            requestBody.put("page", page);
            requestBody.put("pagesize", pageSize);
            try {
                // 构建请求并获取响应
                String response = sendRequest(url, requestBody);

                // 解析响应数据
                List<SxriFdyClassXx> pageData = parseResponse(response, rylx, typeName);

                if (CollUtil.isEmpty(pageData)) {
                    log.info("第{}页没有{}数据，结束拉取", page, typeName);
                    break;
                }

                allData.addAll(pageData);
                log.info("第{}页拉取{}数据{}条", page, typeName, pageData.size());

                // 检查是否还有下一页
                JSONObject jsonResponse = JSON.parseObject(response);
                JSONObject data = jsonResponse.getJSONObject("data");
                int total = data.getInteger("Total");

                if (page * pageSize >= total) {
                    log.info("已拉取完所有{}数据，共{}页", typeName, page);
                    break;
                }

                page++;

            } catch (Exception e) {
                log.error("拉取{}数据第{}页失败", typeName, page, e);
                throw e;
            }
        }

        if (page > maxPages) {
            log.warn("{}数据拉取达到最大页数限制：{}", typeName, maxPages);
        }

        return allData;
    }

    /**
     * 清空指定类型的数据（在事务中执行）
     */
    private void clearDataByType(String rylx, String typeName) {
        log.info("开始清空{}数据", typeName);
        LambdaQueryWrapper<SxriFdyClassXx> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(SxriFdyClassXx::getRylx, rylx);

        long deleteCount = this.count(deleteWrapper);
        this.remove(deleteWrapper);
        log.info("清空{}数据完成，删除{}条记录", typeName, deleteCount);
    }

    /**
     * 批量插入数据（在事务中执行）
     */
    private void batchInsertData(List<SxriFdyClassXx> allData, String typeName) {
        log.info("开始批量插入{}数据，共{}条", typeName, allData.size());

        // 分批插入，避免单次插入数据量过大
        List<List<SxriFdyClassXx>> batches = Lists.partition(allData, sxriProperties.getBatchSize());

        for (List<SxriFdyClassXx> batch : batches) {
            SxriFdyClassXxServiceImpl sxriFdyClassXxService = (SxriFdyClassXxServiceImpl) AopContext.currentProxy();
            // 使用新事务处理每个批次，避免长时间锁定
            sxriFdyClassXxService.batchInsertInNewTransaction(batch);
        }

        log.info("批量插入{}数据完成，共{}批，{}条记录", typeName, batches.size(), allData.size());
    }

    /**
     * 在新事务中批量插入数据
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW, timeout = 60)
    public void batchInsertInNewTransaction(List<SxriFdyClassXx> batch) {
        this.saveBatch(batch);
    }


    /**
     * 发送HTTP请求（带重试机制）
     */
    private String sendRequest(String url, JSONObject requestBody) {

        for (int retry = 0; retry < sxriProperties.getRetryCount(); retry++) {
            try {
                log.debug("发送请求到：{}，请求体：{}，重试次数：{}", url, requestBody.toJSONString(), retry);

                String response = HttpUtil.createPost(url)
                        .header("applyId", sxriProperties.getApplyId())
                        .header("secretKey", sxriProperties.getSecretKey())
                        .header("Content-Type", "application/json")
                        .timeout(sxriProperties.getTimeout())
                        .body(requestBody.toJSONString())
                        .execute()
                        .body();

                if (StrUtil.isBlank(response)) {
                    throw new RuntimeException("接口返回空响应");
                }

                return response;

            } catch (Exception e) {
                log.warn("请求失败，第{}次重试，错误：{}", retry + 1, e.getMessage());

                if (retry == sxriProperties.getRetryCount() - 1) {
                    throw new RuntimeException("请求失败，已重试" + sxriProperties.getRetryCount() + "次", e);
                }

                // 重试间隔
                try {
                    TimeUnit.MILLISECONDS.sleep(sxriProperties.getRetryInterval());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }

        throw new RuntimeException("请求失败");
    }

    /**
     * 解析响应数据
     */
    private List<SxriFdyClassXx> parseResponse(String response, String rylx, String typeName) {
        try {
            JSONObject jsonResponse = JSON.parseObject(response);

            if (jsonResponse.getInteger("status") != 200) {
                throw new RuntimeException("接口调用失败：" + jsonResponse.getString("msg"));
            }

            JSONObject data = jsonResponse.getJSONObject("data");
            JSONArray rows = data.getJSONArray("Rows");

            if (CollUtil.isEmpty(rows)) {
                return new ArrayList<>();
            }

            List<SxriFdyClassXx> result = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                JSONObject row = rows.getJSONObject(i);
                SxriFdyClassXx entity = buildEntity(row, rylx);
                if (entity != null) {
                    result.add(entity);
                }
            }

            return result;

        } catch (Exception e) {
            log.error("解析{}响应数据失败：{}", typeName, response, e);
            throw new RuntimeException("解析响应数据失败", e);
        }
    }

    /**
     * 构建实体对象
     */
    private SxriFdyClassXx buildEntity(JSONObject row, String rylx) {
        try {
            SxriFdyClassXx entity = new SxriFdyClassXx();
            entity.setJsbh(row.getString("jsbh"));
            entity.setBjdm(row.getString("bjdm"));
            entity.setBjmc(row.getString("bjmc"));
            entity.setDbkssj(row.getString("dbkssj"));
            entity.setDbjssj(row.getString("dbjssj"));
            entity.setRylx(rylx);

            // 数据验证
            if (StrUtil.isBlank(entity.getJsbh()) || StrUtil.isBlank(entity.getBjdm())) {
                log.warn("跳过无效数据：教师编号或班级代码为空，数据：{}", row.toJSONString());
                return null;
            }

            return entity;

        } catch (Exception e) {
            log.warn("构建实体对象失败，跳过该条数据：{}", row.toJSONString(), e);
            return null;
        }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(SxriFdyClassXxQO qo) {
        if (StringUtils.isBlank(qo.getJsbh())) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("教师编号不能为空");
        }
        if (StringUtils.isBlank(qo.getBjdm())) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("班级代码不能为空");
        }
    }

    /**
     * 检查记录是否重复
     */
    private boolean isDuplicateRecord(SxriFdyClassXxQO qo) {
        LambdaQueryWrapper<SxriFdyClassXx> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SxriFdyClassXx::getJsbh, qo.getJsbh())
                .eq(SxriFdyClassXx::getBjdm, qo.getBjdm())
                .eq(SxriFdyClassXx::getRylx, qo.getRylx());

        return this.count(wrapper) > 0;
    }
} 