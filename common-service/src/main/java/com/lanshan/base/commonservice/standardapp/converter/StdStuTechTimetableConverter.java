package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdStuTechTimetable;
import com.lanshan.base.commonservice.standardapp.vo.StdStuTechTimetableVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 课表-教师学生课表关联表(StdStuTechTimetable)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdStuTechTimetableConverter {

    StdStuTechTimetableConverter INSTANCE = Mappers.getMapper(StdStuTechTimetableConverter.class);

    StdStuTechTimetableVO toVO(StdStuTechTimetable entity);

    StdStuTechTimetable toEntity(StdStuTechTimetableVO vo);

    List<StdStuTechTimetableVO> toVO(List<StdStuTechTimetable> entityList);

    List<StdStuTechTimetable> toEntity(List<StdStuTechTimetableVO> voList);
}


