package com.lanshan.base.commonservice.schooldata.northwestpolitics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dao.SdXsxjxxMapper;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.StudentStatusInfoDTO;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.po.SdXsxjxx;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdXsxjxxService;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.NorthwestPoliticsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 学生信息表 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-07
 */

@Slf4j
@Service
public class SdXsxjxxServiceImpl extends ServiceImpl<SdXsxjxxMapper, SdXsxjxx> implements SdXsxjxxService {

    @Resource
    private SdXsxjxxMapper sdXsxjxxMapper;

    @Override
    public void syncSdXsxjxx() {
        List<StudentStatusInfoDTO> studentStatusInfo = NorthwestPoliticsUtil.getStudentStatusInfo(null);
        if (CollUtil.isEmpty(studentStatusInfo)) {
            log.info("本专科生_学籍信息为空，同步终止");
            return;
        }
        sdXsxjxxMapper.truncate();
        List<SdXsxjxx> records = BeanUtil.copyToList(studentStatusInfo, SdXsxjxx.class);
        SdXsxjxxServiceImpl proxy = (SdXsxjxxServiceImpl) AopContext.currentProxy();
        proxy.insertBatch(records);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<SdXsxjxx> records) {
        List<List<SdXsxjxx>> batchList = CollUtil.split(records, 2000);
        for (List<SdXsxjxx> batch : batchList) {
            sdXsxjxxMapper.insertBatch(batch);
        }
    }

}
