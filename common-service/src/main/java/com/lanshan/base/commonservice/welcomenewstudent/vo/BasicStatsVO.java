package com.lanshan.base.commonservice.welcomenewstudent.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础统计数据VO - 用于合并多个统计查询提高性能
 *
 * <AUTHOR>
 */
@Data
@ApiModel("基础统计数据")
public class BasicStatsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总新生数")
    private Long totalStudents;

    @ApiModelProperty("已加入企微总数")
    private Long joinedWechatCount;

    @ApiModelProperty("已签到人数")
    private Long checkedInCount;

    @ApiModelProperty("基本信息完成总数")
    private Long basicInfoCompletedCount;

    @ApiModelProperty("交通信息完成总数")
    private Long trafficInfoCompletedCount;
} 