package com.lanshan.base.commonservice.welcomenewstudent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.lanshan.base.commonservice.typehandler.ObjectListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 新生基本信息采集表(NewStudentInfoCollection)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName(value = "new_student_info_collection", autoResultMap = true)
public class NewStudentInfoCollection extends Model<NewStudentInfoCollection> {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 新生基本信息表ID
     */
    private Long newStudentDataId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 政治面貌
     */
    private String politicalStatus;

    /**
     * 民族
     */
    private String nationality;

    /**
     * 出生日期
     */
    private Date birthDate;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 常住地址
     */
    private String permanentAddress;

    /**
     * 家长联系方式信息，包含多个联系人的详细信息
     */
    @TableField(value = "parent_contact_info", typeHandler = ObjectListTypeHandler.class)
    private List<Object> parentContactInfo;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 户口所在地
     */
    private String householdAddress;

    /**
     * 学生类别
     */
    private String studentCategory;

    /**
     * 是否有意愿参加意外伤害保险
     */
    private Boolean accidentInsuranceFlag;

    /**
     * 身高
     */
    private String height;

    /**
     * 体重
     */
    private String weight;

    /**
     * 鞋码
     */
    private String shoeSize;

    /**
     * 日常用品多个用逗号","分割
     */
    private String dailyNecessities;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
} 