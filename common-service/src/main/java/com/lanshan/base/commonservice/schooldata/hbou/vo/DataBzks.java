package com.lanshan.base.commonservice.schooldata.hbou.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class DataBzks {
    @JSONField(name = "姓名")
    private String xm;

    @JSONField(name = "性别名称")
    private String xbmc;

    @JSONField(name = "学号")
    private String xh;
    @JSONField(name = "学生当前状态")
    private String xsdqzt;
    @JSONField(name = "入学年月")
    private String rxny;
    @JSONField(name = "年级")
    private String nj;
    @JSONField(name = "专业名称")
    private String zymc;
    @JSONField(name = "专业编码")
    private String zybm;
    @JSONField(name = "班级名称")
    private String bjmc;
    @JSONField(name = "班级编码")
    private String bjbm;
    @JSONField(name = "学院名称")
    private String xymc;
    @JSONField(name = "学院编码")
    private String xybh;
    @JSONField(name = "校区名称")
    private String xqmc;
    @JSONField(name = "校区编码")
    private String xqbm;
    @JSONField(name = "学生类别")
    private String xslb;
}
