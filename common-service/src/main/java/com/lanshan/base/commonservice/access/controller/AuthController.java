package com.lanshan.base.commonservice.access.controller;


import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.qo.ApiAccessTokenQo;
import com.lanshan.base.commonservice.access.service.AuthService;
import com.lanshan.base.commonservice.system.dto.UserBaseDTO;
import com.lanshan.base.commonservice.system.service.SysLoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 权限控制
 */
@RestController
@RequestMapping("/access/auth")
@Api(tags = "权限控制")
@RequiredArgsConstructor
public class AuthController {

    final private AuthService authService;

    final private SysLoginService sysLoginService;

    @ApiOperation("获取API访问token")
    @GetMapping("/getApiAccessToken")
    public Result<String> getApiAccessToken(String keyId, String appSecret) {
        return Result.build(authService.getApiAccessToken(keyId, appSecret).getTokenValue());
    }

    @ApiOperation("获取API访问token")
    @PostMapping("/getApiAccessToken")
    public Result<String> getApiAccessToken(@RequestBody ApiAccessTokenQo qo) {
        return Result.build(authService.getApiAccessToken(qo.getKeyId(), qo.getAppSecret()).getTokenValue());
    }


    @ApiOperation("通过ticket获取用户信息")
    @GetMapping("/getUserInfoByTicket")
    public Result<UserBaseDTO> getUserInfoByTicket(@RequestParam String ticket) {
        return Result.build(sysLoginService.getUserInfoByTicket(ticket));
    }
}
