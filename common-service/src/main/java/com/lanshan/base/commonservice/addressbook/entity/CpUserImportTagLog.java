package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 企业微信用户导入标签日志
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class CpUserImportTagLog extends Model<CpUserImportTagLog> {

    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 失败
     */
    private Boolean hasError;

    /**
     * 错误信息
     */
    private String errorMessage;
}
