package com.lanshan.base.commonservice.todo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.todo.entity.TodoUserOperateLog;
import com.lanshan.base.commonservice.todo.vo.TodoUserOperateLogVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface TodoUserOperateLogMapper extends BaseMapper<TodoUserOperateLog> {


    /**
     * 根据节点日志id查询操作日志列表
     *
     * @param nodeLogId 节点日志id
     * @return 操作日志列表
     */
    List<TodoUserOperateLogVO> listByNodeLogId(Long nodeLogId);

}
