package com.lanshan.base.commonservice.todo.service;

import com.lanshan.base.commonservice.todo.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 用户统计服务层
 */
public interface ToDoStatService {


    /**
     * 获取当前用户的统计概览数据
     *
     * @return 当前用户的统计概览
     */
    PersonalStatOverviewVO getPersonStatOverview(String userId,String whiteListSign);


    /**
     * 获取总览统计概览
     *
     * @return 总览统计概览
     */
    ManageStatOverviewVO getGeneralOverview();

    /**
     * 获取用户排名
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 用户排名
     */
    List<UserRankVO> getUserTodoRank(String startDate, String endDate);

    /**
     * 获取周次小时统计表
     *
     * @return 周次小时统计表
     */
    List<TodoHourWeekStatVO> listTodoHourWeekStat();

    /**
     * 获取应用统计信息
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 应用统计信息
     */
    List<AppStatVO> getAppStatInfo(String userId, String startDate, String endDate,String whiteListSign);

    /**
     * 获取应用排名
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 应用排名
     */
    List<CommonRankVO> getAppTodoRank(String startDate, String endDate);

    /**
     * 获取部门排名
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 部门排名
     */
    List<CommonRankVO> getDeptTodoRank(String startDate, String endDate);
}
