package com.lanshan.base.commonservice.access.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 应用数据推送操作类型(AcAppPushOperateType)实体
 */
@Data
public class AcAppPushOperateType implements Serializable {
    private static final long serialVersionUID = -52077701302122059L;

    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("接口调用接入方ID")
    private Long companyId;

    @ApiModelProperty("接口调用应用ID")
    private Long appId;

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("操作类型描述")
    private String operateTypeDesc;

}

