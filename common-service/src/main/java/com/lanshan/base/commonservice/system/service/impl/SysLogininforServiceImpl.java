package com.lanshan.base.commonservice.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.constant.SystemUserConstants;
import com.lanshan.base.api.utils.system.IpUtils;
import com.lanshan.base.commonservice.config.manager.factory.AsyncFactory;
import com.lanshan.base.commonservice.system.entity.SysLogininfor;
import com.lanshan.base.commonservice.system.mapper.SysLogininforMapper;
import com.lanshan.base.commonservice.system.qo.LoginInfoQo;
import com.lanshan.base.commonservice.system.service.ISysLogininforService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@Service("sysLogininforService")
public class SysLogininforServiceImpl extends ServiceImpl<SysLogininforMapper, SysLogininfor> implements ISysLogininforService {

    @Resource
    private SysLogininforMapper sysLogininforMapper;

    /**
     * 新增系统登录日志
     *
     * @param logininfor 访问日志对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertLogininfor(SysLogininfor logininfor) {
        recordLogininfor(logininfor.getUserName(), logininfor.getStatus(), logininfor.getMsg());
        return 1;
    }

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor) {
        return sysLogininforMapper.selectLogininforList(logininfor);
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteLogininforByIds(Long[] infoIds) {
        return sysLogininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanLogininfor() {
        sysLogininforMapper.cleanLogininfor();
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    @Override
    public void recordLogininfor(String username, String status, String message) {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr());
        logininfor.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, SystemUserConstants.LOGIN_SUCCESS, SystemUserConstants.LOGOUT, SystemUserConstants.REGISTER)) {
            logininfor.setStatus(SystemUserConstants.LOGIN_SUCCESS_STATUS);
        } else if (SystemUserConstants.LOGIN_FAIL.equals(status)) {
            logininfor.setStatus(SystemUserConstants.LOGIN_FAIL_STATUS);
        }
        logininfor.setAccessTime(new Date());
        AsyncFactory.addLoginInfo(logininfor);
    }

    /**
     * 分页查询登录日志
     *
     * @param qo 查询条件
     * @return IPage<SysLogininfor>
     */
    @Override
    public IPage<SysLogininfor> pageLoginInfoLog(LoginInfoQo qo) {
        Page<SysLogininfor> page = new Page<>(qo.getPage(), qo.getSize());
        LambdaQueryWrapper<SysLogininfor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(qo.getUserName()), SysLogininfor::getUserName, qo.getUserName())
                .like(StringUtils.isNotBlank(qo.getIpaddr()), SysLogininfor::getIpaddr, qo.getIpaddr())
                .between(qo.getBeginTime() != null && qo.getEndTime() != null, SysLogininfor::getAccessTime, qo.getBeginTime(), qo.getEndTime())
                .orderByDesc(SysLogininfor::getAccessTime, SysLogininfor::getInfoId);
        return this.page(page, queryWrapper);
    }
}
