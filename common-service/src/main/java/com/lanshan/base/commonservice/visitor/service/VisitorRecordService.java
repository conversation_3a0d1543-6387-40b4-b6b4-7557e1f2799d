package com.lanshan.base.commonservice.visitor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.api.qo.PageQo;
import com.lanshan.base.commonservice.visitor.entity.VisitorRecord;
import com.lanshan.base.commonservice.visitor.qo.*;
import com.lanshan.base.commonservice.visitor.vo.AppointQRcodeVO;
import com.lanshan.base.commonservice.visitor.vo.VisitorRecordVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 访客记录表(VisitorRecord)服务接口
 */
public interface VisitorRecordService extends IService<VisitorRecord> {

    /**
     * 保存预约
     * @param qo 新增入参
     */
    void saveAppointment(VisitorAppointmentSaveQo qo);

    /**
     * 保存邀约
     * @param qo 新增入参
     */
    void saveInvitation(VisitorInvitationSaveQo qo);

    /**
     * 分页查询预约
     * @param pageQo 分页入参
     * @return 分页结果
     */
    IPage<VisitorRecordVo> pageAppointment(PageQo pageQo);

    /**
     * 分页查询邀约
     * @param pageQo 分页入参
     * @return 分页结果
     */
    IPage<VisitorRecordVo> pageInvitation(PageQo pageQo);

    /**
     * 分页查询访问我的
     * @param qo 分页入参
     * @return 分页结果
     */
    IPage<VisitorRecordVo> pageVisitMe(VisitorRecordAuditPageQo qo);

    /**
     * 分页查询审核列表
     * @param qo 分页入参
     * @return 分页结果
     */
    IPage<VisitorRecordVo> pageAudit(VisitorRecordAuditPageQo qo);

    /**
     * 分页查询我的邀约
     * @param qo 分页入参
     * @return 分页结果
     */
    IPage<VisitorRecordVo> pageMyInvitation(VisitorRecordAuditPageQo qo);

    /**
     * 分页查询访客记录
     * @param qo 分页入参
     * @return 分页结果
     */
    IPage<VisitorRecordVo> pageRecord(VisitorRecordPageQo qo);


    /**
     * 取消预约
     * @param recordId 记录id
     */
    void cancelAppointment(Long recordId);

    /**
     * 审核
     * @param qo 审核入参
     */
    void audit(VisitorRecordProcessQo qo);

    /**
     * 取消邀约
     * @param id 记录id
     */
    void cancelInvitation(Long id);

    /**
     * 访客记录导出
     * @param pageQo 分页入参
     * @param response 响应
     */
    void exportRecord(VisitorRecordPageQo pageQo, HttpServletResponse response) throws IOException;

    /**
     * 获取预约二维码信息
     * @return 二维码信息
     */
    AppointQRcodeVO getAppointmentQRcodeInfo();

    /**
     * 通知
     */
    void notice();
}
