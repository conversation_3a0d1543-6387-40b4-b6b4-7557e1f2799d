package com.lanshan.base.commonservice.todo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.access.vo.AcAppVO;
import com.lanshan.base.commonservice.todo.entity.TodoDef;
import com.lanshan.base.commonservice.todo.qo.*;
import com.lanshan.base.commonservice.todo.vo.*;
import me.chanjar.weixin.common.error.WxErrorException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 待办定义表(TodoDef)服务接口
 */
public interface TodoDefService extends IService<TodoDef> {

    /**
     * 新增待办
     *
     * @param qo 待办参数
     */
    Long saveTodo(TodoSaveQo qo);

    /**
     * 更新待办
     *
     * @param qo 待办参数
     */
    void updateTodo(TodoUpdateQo qo);

    /**
     * 批量删除待办
     *
     * @param id 待办id
     */
    void removeTodo(Long id);

    /**
     * 完成待办
     *
     * @param idList 待办id列表
     */
    void batchCompleteTodo(List<Long> idList);


    /**
     * 分页查询待办
     *
     * @param pageQo 分页查询参数
     * @return 待办列表
     */
    IPage<TodoVo> pageTodo(TodoPageQo pageQo);

    /**
     * 查询逾期待办
     *
     * @param pageQo 分页查询参数
     * @return 逾期待办列表
     */
    List<TodoVo> listOverdueTodo(TodoPageQo pageQo);

    /**
     * 分页查询待办记录
     *
     * @param pageQo 分页查询参数
     * @return 待办记录列表
     */
    IPage<TodoDefVo> pageTodoDef(TodoDefPageQo pageQo);

    /**
     * 根据月份查询每日是否有待办
     *
     * @param yyyyMM 年月
     * @return 每日是否有待办
     */
    List<ExistTodoByMonthVo> existTodoByMonth(String yyyyMM,String whiteListSign);

    /**
     * 分页查询参与人
     *
     * @param pageQo 分页查询参数
     * @return 参与人列表
     */
    IPage<TodoUserVo> pageUser(TodoUserPageQo pageQo);

    /**
     * 批量取消完成待办
     *
     * @param idList 待办id列表
     */
    void batchCancelCompleteTodo(List<Long> idList);

    /**
     * 到期自动完成待办
     *
     * @param startDate 开始日期
     */
    void autoCompleteTodo(String startDate);

    /**
     * 删除已完成待办的日历
     *
     * @param date 日期
     */
    void delCompleteCalendar(String date);

    /**
     * 分页查询我收到的待办
     *
     * @param pageQo 分页查询参数
     * @return 我收到的待办列表
     */
    IPage<TodoVo> pageMyTodo(TodoPageQo pageQo);

    /**
     * 分页查询抄送给我的待办
     *
     * @param pageQo 分页查询参数
     * @return 抄送给我的待办列表
     */
    IPage<TodoFlowVo> pageCopyToMeTodo(TodoPageQo pageQo);

    /**
     * 分页查询我提交的待办
     *
     * @param pageQo 分页查询参数
     * @return 我提交的待办列表
     */
    IPage<TodoFlowVo> pageISubmitTodo(TodoPageQo pageQo);

    /**
     * 顺延待办
     *
     * @param qo 待办参数
     */
    void delay(TodoDelayQo qo);

    /**
     * 我收到的待办-逾期
     *
     * @param pageQo 分页查询参数
     * @return 逾期待办列表
     */
    List<TodoVo> listOverdueMyTodo(TodoPageQo pageQo);

    /**
     * 新增流程信息
     *
     * @param qo 流程信息
     */
    void saveFlowInfo(TodoSaveFlowInfoQo qo);

    /**
     * 删除流程信息
     *
     * @param qo 删除流程信息
     */
    void delFlowInfo(TodoDelFlowInfoQo qo);

    /**
     * 新增流程节点
     *
     * @param qo 流程节点
     */
    void saveFlowNode(TodoSaveFlowNodeQo qo);

    /**
     * 更新流程信息
     *
     * @param qo 流程信息
     */
    void updateFlowInfo(TodoUpdateFlowInfoQo qo);

    /**
     * 删除流程节点
     *
     * @param qo 流程节点
     */
    void delFlowNode(TodoDelFlowNodeQo qo);

    /**
     * 更新流程节点
     *
     * @param qo 流程节点
     */
    void updateFlowNode(TodoSaveFlowNodeQo qo);

    /**
     * 查询应用列表
     *
     * @param isAdmin 是否为管理员
     * @param todoType 待办类型 1 待完成 2 已完成 3 抄送我 4 我提交的
     * @return 应用列表
     */
    List<AcAppVO> listApp(boolean isAdmin, String todoType,String whiteListSign);

    /**
     * 获取最小创建时间
     *
     * @return 最小创建时间
     */
    TodoDef getMinCreateDate();

    /**
     * 发送提醒
     */
    void todoRemind();

    /**
     * 更新代办工作台展示数据
     */
    void updateTodoWorkbenchData(String needUpdateAll);

    /**
     * 查询我的代办日历列表
     * @param qo 查询参数
     * @return 我的代办日历列表
     */
    Map<Date, List<TodoVo>> listMyTodoCalendar(MyTodoCalendarQo qo);

    /**
     * 分页查询待办定义
     * @param pageQo 分页查询参数
     * @return 代办定义列表
     */
    IPage<TodoVo> pageTodoRecord(TodoRecordPageQo pageQo);

    /**
     * 获取待办信息
     * @return 待办信息
     */
    TodoInfoVo getTodoInfo();

    /**
     * 设置代办应用展示样式
     * @param type 展示样式
     */
    void setTodoAppShowStyle(String type) throws WxErrorException;

    /**
     * 获取待办流程信息
     * @param serialNo 流水号
     * @return 待办流程信息
     */
    TodoFlowInfoVO getFlowInfo(String serialNo);

    /**
     * 查询待办类型
     * @return 待办类型列表
     */
    List<String> listType();

    /**
     * 获取当前节点
     *
     * @param flowInfoId 待办流程信息id
     * @return 当前节点
     */
    TodoDef getCurrentNode(Long flowInfoId);

    /**
     * 增量保存流程节点
     *
     * @param qo 流程节点
     */
    void addUserTodo(AddUserTodoQO qo);

    /**
     * 完成待办
     *
     * @param qo 待办参数
     */
    void completeUserTodo(CompleteUserTodoQO qo);

    /**
     * 批量删除待办
     *
     * @param qo 待办参数
     */
    void delUserTodo(DelUserTodoQO qo);

    /**
     * 初始化待办工作台展示，初始待办数量为 0
     */
    void initTodoWorkbenchData();
}
