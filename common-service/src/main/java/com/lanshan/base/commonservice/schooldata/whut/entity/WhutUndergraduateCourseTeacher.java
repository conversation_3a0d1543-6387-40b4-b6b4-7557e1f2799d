package com.lanshan.base.commonservice.schooldata.whut.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 本科生课表信息-教师表(WhutUndergraduateCourseTeacher)表实体类
 */
@Data
public class WhutUndergraduateCourseTeacher implements Serializable{

    @ApiModelProperty(value = "选课课号")
    private String xkkh;

    @ApiModelProperty(value = "学年学期")
    private String xnxq;

    @ApiModelProperty(value = "课程编码")
    private String kcbm;

    @ApiModelProperty(value = "课程名称")
    private String kcmc;

    @ApiModelProperty(value = "教学班名称")
    private String jxbmc;

    @ApiModelProperty(value = "授课教师工号")
    private String skjsgh;

    @ApiModelProperty(value = "授课教师姓名")
    private String skjsxm;

}

