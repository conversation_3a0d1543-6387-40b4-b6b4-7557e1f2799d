package com.lanshan.base.commonservice.group.converter;


import java.util.List;

import com.lanshan.base.commonservice.addressbook.entity.CpDepartment;
import com.lanshan.base.commonservice.group.entity.MsgClassGroupChatApply;
import com.lanshan.base.commonservice.group.vo.ClassInfoVO;
import com.lanshan.base.commonservice.group.vo.MsgClassGroupChatApplyVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 班级建群申请(MsgClassGroupChatApply)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface MsgClassGroupChatApplyConverter {

    MsgClassGroupChatApplyConverter INSTANCE = Mappers.getMapper(MsgClassGroupChatApplyConverter.class);

    MsgClassGroupChatApplyVO toVO(MsgClassGroupChatApply entity);

    MsgClassGroupChatApply toEntity(MsgClassGroupChatApplyVO vo);
    
    List<MsgClassGroupChatApplyVO> toVO(List<MsgClassGroupChatApply> entityList);

    List<MsgClassGroupChatApply> toEntity(List<MsgClassGroupChatApplyVO> voList);

    @Mapping(target = "deptId", source = "id")
    @Mapping(target = "deptName", source = "name")
    @Mapping(target = "deptPath", source = "path")
    ClassInfoVO toClassInfoVO(CpDepartment entity);

    @Mapping(target = "deptId", source = "id")
    @Mapping(target = "deptName", source = "name")
    @Mapping(target = "deptPath", source = "path")
    List<ClassInfoVO> toClassInfoVO(List<CpDepartment> entityList);
}


