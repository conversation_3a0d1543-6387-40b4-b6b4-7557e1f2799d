package com.lanshan.base.commonservice.constant;

import org.apache.commons.lang3.StringUtils;

public class CommonServiceRedisKeys {

    private CommonServiceRedisKeys() {
    }

    /**
     * 通讯录操作 redis key
     */
    public static final String COMMON_SERVICE_ADDRESS_OPERATE = "COMMON_SERVICE:ADDRESS_OPERATE";

    /**
     * 企微备份 redis key
     */
    public static final String COMMON_SERVICE_CP_BAK = "COMMON_SERVICE:CP_BAK";

    /**
     * 清空企微备份 redis key
     */
    public static final String COMMON_SERVICE_CP_BAK_REMOVE = "COMMON_SERVICE:CP_BAK_REMOVE";

    /**
     * 企微恢复 redis key
     */
    public static final String COMMON_SERVICE_CP_RECOVER = "COMMON_SERVICE:CP_RECOVER";

    /**
     * 应用新组织架构 redis key
     */
    public static final String COMMON_SERVICE_APPLY_NEW_DEPT = "COMMON_SERVICE:APPLY_NEW_DEPT";

    /**
     * 通讯录同步 redis key
     */
    public static final String COMMON_SERVICE_ADDRESS_SYNC = "COMMON_SERVICE:ADDRESS_SYNC";

    /**
     * 通讯录回调用户标签时排除的用户id redis key
     */
    public static final String COMMON_SERVICE_CALLBACK_USER_TAG_EXCLUDE_USERID = "COMMON_SERVICE:CALLBACK_USER_TAG_EXCLUDE_USERID";

    /**
     * API map redis key
     */
    public static final String ACCESS_API_MAP = "ACCESS:API_MAP";

    /**
     * APP map redis key
     */
    public static final String ACCESS_APP_MAP = "ACCESS:APP_MAP";

    /**
     * API权限控制 根据类型和接口id分组 map redis key
     */
    public static final String ACCESS_API_CONTROL_MAP_BY_TYPE_AND_API_ID = "ACCESS:API_CONTROL_MAP_BY_TYPE_AND_API_ID";

    /**
     * API权限控制 根据类型和控制id分组 map redis key
     */
    public static final String ACCESS_API_CONTROL_MAP_BY_TYPE_AND_CONTROL_ID = "ACCESS:API_CONTROL_MAP_BY_TYPE_AND_CONTROL_ID";

    /**
     * 工作台应用 agentId-appId map redis key
     */
    public static final String WORKBENCH_AGENT_ID_APP_ID_MAP = "WORKBENCH:AGENT_ID_APP_ID_MAP";

    /**
     * 班级建群 redis key
     */
    public static final String GROUP_CHAT_CLASS_CREATE = "GROUP_CHAT:CLASS_CREATE:";

    /**
     * 辅导员 redis key
     */
    public static final String GROUP_CHAT_COUNSELOR = "GROUP_CHAT:COUNSELOR";

    /**
     * 课程建群 redis key
     */
    public static final String GROUP_CHAT_COURSE_CREATE = "GROUP_CHAT:COURSE_CREATE:";
    /**
     * 教职工党政职务信息 redis key
     */
    public static final String GROUP_CHAT_STAFF_JOB_INFO_MAP = "GROUP_CHAT:STAFF_JOB_INFO_MAP";

    /**
     * 部门负责人 redis key
     */
    public static final String GROUP_CHAT_DEPT_HEAD_MAP = "GROUP_CHAT:DEPT_HEAD_MAP";

    /**
     * 部门建群 redis key
     */
    public static final String GROUP_CHAT_DEPT_CREATE = "GROUP_CHAT:DEPT_CREATE:";

    /**
     * 用户map redis key
     */
    public static final String COMMON_SERVICE_USER_MAP = "COMMON_SERVICE:USERID_MAP";

    /**
     * 群聊应用设置 redis key
     */
    public static final String GROUP_CHAT_APP_SETTING_MAP = "GROUP_CHAT:APP_SETTING_MAP";

    /**
     * 课程教师 redis key
     */
    public static final String GROUP_CHAT_COURSE_TEACHER = "GROUP_CHAT:COURSE_TEACHER";

    /**
     * 是否可以邀请 redis key
     */
    public static final String GROUP_CHAT_CAN_INVITE = "GROUP_CHAT:CAN_INVITE:";

    /**
     * 邀请外部人员是否提交 redis key
     */
    public static final String INVITE_OUTSIDE_SUBMITTED = "COMMON_SERVICE:INVITE_OUTSIDE_SUBMITTED:";

    /**
     * 邀请外部人员设置 redis key
     */
    public static final String INVITE_OUTSIDE_JOIN_SETTING_KEY = "COMMON_SERVICE:INVITE_OUTSIDE_JOIN_SETTING_KEY";

    /**
     * 是否可以邀请 redis key
     */
    public static String getGroupChatCanInviteKey(String type, String studentType, String code) {
        String key = GROUP_CHAT_CAN_INVITE + type;
        if (StringUtils.isNotBlank(studentType)) {
            key += ":" + studentType;
        }
        if (StringUtils.isNotBlank(code)) {
            key += ":" + code;
        }
        return key;
    }

    /**
     * 工作台应用在企微的状态 redis key
     */
    public static final String WORKBENCH_APP_WX_STATUS_KEY = "workbench:app:wx:status";

    /**
     * 工作台应用在企微的状态 启用
     */
    public static final String WORKBENCH_APP_WX_STATUS_ENABLE = "1";

    /**
     * 工作台应用在企微的状态 停用
     */
    public static final String WORKBENCH_APP_WX_STATUS_DISABLE = "0";


    /**
     * 部门map redis key
     */
    public static final String COMMON_SERVICE_DEPARTMENT_MAP = "COMMON_SERVICE:DEPARTMENT_MAP";


    /**
     * 待办工作台展示变更的用户 userid 集合 redis key
     */
    public static final String TODO_SHOW_CHANGE_USER_ID_SET = "COMMON_SERVICE:SHOW_CHANGE_USER_ID_SET";

    /**
     * 待办工作台展示变更的用户 userid 集合锁 redis key
     */
    public static final String TODO_SHOW_CHANGE_USER_ID_SET_LOCK = "COMMON_SERVICE:SHOW_CHANGE_USER_ID_SET_LOCK";

    /**
     * 群聊 redis key
     */
    public static final String STD_GROUP_CHAT = "STD_GROUP_CHAT:";

    /**
     * 应用信息缓存 redis key
     */
    public static final String APP_INFO_CACHE_KEY = "app_info:";

    /**
     * 应用信息缓存过期时间
     */
    public static final long APP_INFO_CACHE_EXPIRE = 30 * 60;

    /**
     * 应用统计信息缓存过期时间
     */
    public static final String GENERAL_OVERVIEW_CACHE_KEY = "COMMON_SERVICE:GENERAL_OVERVIEW_CACHE";

    /**
     * 新生导入错误信息 redis key
     */
    public static final String USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST = "COMMON_SERVICE:USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST";

    /**
     * 新生群导入错误信息 redis key
     */
    public static final String NEW_STUDENT_GROUP_IMPORT_ERROR_LIST = "COMMON_SERVICE:NEW_STUDENT_GROUP_IMPORT_ERROR_LIST";

    /**
     * 新生用户状态缓存 redis key
     */
    public static final String NEW_STUDENT_USER_STATUS_CACHE = "COMMON_SERVICE:NEW_STUDENT_USER_STATUS_CACHE";

    /**
     * 新生群组编码缓存 redis key
     */
    public static final String NEW_STUDENT_GROUP_CODES_CACHE = "COMMON_SERVICE:NEW_STUDENT_GROUP_CODES_CACHE";
}
