package com.lanshan.base.commonservice.welcomenewstudent.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生任务查询参数
 */
@ApiModel("新生任务查询参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class NewStudentTaskQO extends PageQo {

    @ApiModelProperty(value = "任务名称")
    private String taskName;
}
