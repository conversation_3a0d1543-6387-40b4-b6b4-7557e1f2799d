package com.lanshan.base.commonservice.access.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.access.converter.AcDataConverter;
import com.lanshan.base.commonservice.access.dao.AcDataDao;
import com.lanshan.base.commonservice.access.entity.AcData;
import com.lanshan.base.commonservice.access.qo.AcDataPageQO;
import com.lanshan.base.commonservice.access.service.AcDataService;
import com.lanshan.base.commonservice.access.vo.AcDataVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 权限数据集(AcData)表服务实现类
 *
 * <AUTHOR>
 */
@Service("acDataService")
public class AcDataServiceImpl extends ServiceImpl<AcDataDao, AcData> implements AcDataService {

    @Override
    public AcDataVO getByDataKey(String dataKey) {
        //通过唯一标识获取数据权限
        LambdaQueryWrapper<AcData> queryWrapper = Wrappers.lambdaQuery(AcData.class);
        queryWrapper.eq(AcData::getDataKey, dataKey);
        AcData data = super.getOne(queryWrapper);

        //转换VO
        return AcDataConverter.INSTANCE.toVO(data);
    }

    @Override
    public IPage<AcDataVO> pageAcData(AcDataPageQO pageQO) {
        Page<AcData> page = new Page<>(pageQO.getPage(), pageQO.getSize());
        LambdaQueryWrapper<AcData> queryWrapper = Wrappers.lambdaQuery(AcData.class);

        //数据权限唯一标识
        if (StringUtils.isNotBlank(pageQO.getDataKey())) {
            queryWrapper.like(AcData::getDataKey, pageQO.getDataKey());
        }

        //数据权限名称
        if (StringUtils.isNotBlank(pageQO.getDataName())) {
            queryWrapper.like(AcData::getDataName, pageQO.getDataName());
        }

        //分页查询
        IPage<AcData> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQO.getPage(), pageQO.getSize());
        }

        //转换VO
        return result.convert(AcDataConverter.INSTANCE::toVO);
    }
}

