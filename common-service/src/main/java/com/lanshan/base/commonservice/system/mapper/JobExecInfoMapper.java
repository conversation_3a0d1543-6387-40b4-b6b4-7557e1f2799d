package com.lanshan.base.commonservice.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.system.entity.JobExecInfo;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface JobExecInfoMapper extends BaseMapper<JobExecInfo> {

    /**
     * 根据jobType获取最新的JobExecInfo
     *
     * @param jobId jobType
     * @return JobExecInfo
     */
    JobExecInfo getByJobType(String jobId);

    /**
     * 更新JobExecInfo
     *
     * @param jobId jobType
     * @return 影响行数
     */
    int updateByJobType(String jobId);
}
