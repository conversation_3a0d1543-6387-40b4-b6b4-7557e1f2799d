package com.lanshan.base.commonservice.access.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "推送日志VO")
@Data
@ToString
public class AcAppPushLogVO implements Serializable {

    private static final long serialVersionUID = -215428800556704430L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("接口调用接入方ID")
    private Long companyId;

    @ApiModelProperty("接口调用应用ID")
    private Long appId;

    @ApiModelProperty("操作类型")
    private String operateType;

    @ApiModelProperty("操作类型描述")
    private String operateTypeDesc;

    @ApiModelProperty("操作数据")
    private String operateParam;

    @ApiModelProperty("调用结果")
    private String responseResult;

    @ApiModelProperty("调用结果状态")
    private Integer responseStatus;

    @ApiModelProperty("接口调用结果状态")
    private String responseStatusDesc;

    @ApiModelProperty(value = "接口调用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}

