package com.lanshan.base.commonservice.addressbook.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("标签导入结果")
public class CpUserImportResutlVO {

    /**
     * 需要新增的标签名称列表
     */
    private List<String> needCreateTagNameList;

    private int successCount;

    private int failCount;

    private List<FailInfo>  failInfoList;

    @Data
    public static class FailInfo {
        private String userId;
        private String name;
        private String errorMessage;

        public FailInfo(String userId, String name, String errorMessage) {
            this.userId = userId;
            this.name = name;
            this.errorMessage = errorMessage;
        }
    }
}
