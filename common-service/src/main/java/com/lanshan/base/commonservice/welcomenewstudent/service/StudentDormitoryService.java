package com.lanshan.base.commonservice.welcomenewstudent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.welcomenewstudent.entity.StudentDormitory;
import com.lanshan.base.commonservice.welcomenewstudent.qo.StudentDormitoryQO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.StudentDormitoryVO;

/**
 * 学生宿舍分配表(StudentDormitory)表服务接口
 *
 * <AUTHOR>
 */
public interface StudentDormitoryService extends IService<StudentDormitory> {

    /**
     * 分页查询所有数据
     *
     * @param qo 查询条件
     * @return 查询结果
     */
    IPage<StudentDormitoryVO> pageByParam(StudentDormitoryQO qo);
}

