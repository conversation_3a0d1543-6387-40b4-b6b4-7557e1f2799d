package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.api.dto.common.UserIdDTO;
import com.lanshan.base.api.dto.file.FileInfo;
import com.lanshan.base.api.dto.system.SysConfigVo;
import com.lanshan.base.api.dto.user.BatchInviteDto;
import com.lanshan.base.api.dto.user.TagAddOrRemoveUsersDto;
import com.lanshan.base.api.dto.user.UserDeptIdsTagIdsDto;
import com.lanshan.base.api.dto.user.UserDto;
import com.lanshan.base.api.enums.*;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.feign.user.TagFeign;
import com.lanshan.base.api.feign.user.UserFeign;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.file.ExcelUtils;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.api.vo.user.TagInfoVo;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.dao.UserRemoveQueueMapper;
import com.lanshan.base.commonservice.addressbook.dto.UpdateUserTagsDto;
import com.lanshan.base.commonservice.addressbook.entity.*;
import com.lanshan.base.commonservice.addressbook.excel.UserExportDto;
import com.lanshan.base.commonservice.addressbook.excel.UserExportWithoutSensitiveInfoDto;
import com.lanshan.base.commonservice.addressbook.excel.UserImportDto;
import com.lanshan.base.commonservice.addressbook.excel.UserImportErrDto;
import com.lanshan.base.commonservice.addressbook.handler.AddressbookHandler;
import com.lanshan.base.commonservice.addressbook.handler.AddressbookHandlerFactory;
import com.lanshan.base.commonservice.addressbook.mapper.CpTagMapper;
import com.lanshan.base.commonservice.addressbook.mapper.CpUserMapper;
import com.lanshan.base.commonservice.addressbook.service.*;
import com.lanshan.base.commonservice.addressbook.vo.UserRemoveLogVo;
import com.lanshan.base.commonservice.common.entity.CommonUploadLog;
import com.lanshan.base.commonservice.common.service.CommonUploadLogService;
import com.lanshan.base.commonservice.config.properties.AddressbookProperties;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.constant.AddressbookConstant;
import com.lanshan.base.commonservice.constant.BatchUserConstant;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.constant.UserBindHandlerConstant;
import com.lanshan.base.commonservice.enums.OperateStatusEnum;
import com.lanshan.base.commonservice.enums.OperateTypeEnum;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;
import com.lanshan.base.commonservice.identify.mapper.StaffIdentifyRelationMapper;
import com.lanshan.base.commonservice.minio.service.MinIOService;
import com.lanshan.base.commonservice.standardapp.entity.StdStudentInfo;
import com.lanshan.base.commonservice.standardapp.service.StdStudentInfoService;
import com.lanshan.base.commonservice.system.service.impl.JobExecInfoServiceImpl;
import com.lanshan.base.commonservice.system.service.impl.SysConfigServiceImpl;
import com.lanshan.base.commonservice.task.batch.dto.JobExecCompDto;
import com.lanshan.base.commonservice.task.batch.entity.BatchJob;
import com.lanshan.base.commonservice.task.batch.entity.BatchJobExec;
import com.lanshan.base.commonservice.task.batch.enums.BatchTypeEnum;
import com.lanshan.base.commonservice.task.batch.service.BatchJobExecService;
import com.lanshan.base.commonservice.task.batch.service.BatchJobService;
import com.lanshan.base.commonservice.task.batch.vo.JobExecVo;
import com.lanshan.base.starter.redis.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxCpErrorMsgEnum;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户表(User)服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<CpUserMapper, CpUser> implements UserService {

    @Resource
    private UserFeign userFeign;

    @Resource
    private UserDepartmentRelationService userDepartmentRelationService;

    @Resource
    private CpUserMapper userMapper;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private UserTagRelationService userTagRelationService;

    @Resource
    private DepartmentTagRelationService departmentTagRelationService;

    @Resource
    private TagFeign tagFeign;

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private TagService tagService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    @Value("${common.user-import.user-import-template-minio-id}")
    private Long userImportTemplateMinioId;

    @Resource
    private MinIOService minIOService;

    @Resource
    private CommonUploadLogService commonUploadLogService;

    @Resource
    private RedisService redisService;

    @Resource
    private AddressbookHandlerFactory addressbookHandlerFactory;

    @Resource
    private AddressbookProperties addressbookProperties;

    @Resource
    private CpUserPhoneService cpUserPhoneService;
    @Autowired
    private SysConfigServiceImpl sysConfigService;

    @Autowired
    BatchJobService batchJobService;

    @Autowired
    BatchJobExecService batchJobExecService;
    @Autowired
    private JobExecInfoServiceImpl jobExecInfoService;

    @Resource
    StaffIdentifyRelationMapper staffIdentifyRelationMapper;
    @Autowired
    private UserInfoOperationLogService userInfoOperationLogService;
    @Autowired
    private UserRemoveQueueMapper userRemoveQueueMapper;
    @Autowired
    private CpUserMapper cpUserMapper;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CpTagMapper cpTagMapper;

    @Resource
    private StdStudentInfoService stdStudentInfoService;

    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncUserOperateToCp(String corpId, List<CpUserOperate> userOpList, Map<String, CpUser> userMap, boolean toInvite) {
        for (CpUserOperate operate : userOpList) {
            CpUser user = userMap.get(operate.getUserid());

            UserSaveQo cpUserParam = JacksonUtils.toObj(JacksonUtils.toJson(operate), UserSaveQo.class);
            cpUserParam.setToInvite(toInvite);
            // 使用 TransactionTemplate 处理事务
            transactionTemplate.execute(status -> {
                saveOrUpdateCpUser(corpId, operate, user, cpUserParam);
                return null;
            });
        }
        log.info("线程[{}] ==> 异步方式同步用户至企微执行完成", Thread.currentThread().getName());
        return new AsyncResult<>(YnEnum.YES.getValue());
    }

    /**
     * 保存或新增企微用户
     */
    private void saveOrUpdateCpUser(String corpId, CpUserOperate operate, CpUser user, UserSaveQo cpUserParam) {
        //不存在则新增
        if (user == null) {

            //如果主部门为空，则默认第一个部门为主部门
            if (cpUserParam.getMainDepartment() == null) {
                cpUserParam.setMainDepartment(cpUserParam.getDepartment().get(0));
            }

            //新增至企微
            Result<?> userCreateResult = userFeign.userCreate(corpId, cpUserParam.getToInvite(), cpUserParam);
            if (userCreateResult.success()) {
                operate.setOperateStatus(OperateStatusEnum.SUCCESS.getCode());
                log.info("新增用户[{}]成功", cpUserParam.getUserid());
            } else if (!userCreateResult.success()
                    && WxCpErrorMsgEnum.CODE_1.getCode() != userCreateResult.getCode()
                    && WxCpErrorMsgEnum.CODE_45033.getCode() != userCreateResult.getCode()) {
                //设为失败
                operate.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                operate.setErrorMsg(userCreateResult.getMsg());
                log.error("新增用户[{}]失败：{}", cpUserParam.getUserid(), userCreateResult.getMsg());
            }

            //存在则更新
        } else {
            Result<?> userUpdateResult = userFeign.userUpdate(corpId, cpUserParam);
            if (userUpdateResult.success()) {
                operate.setOperateStatus(OperateStatusEnum.SUCCESS.getCode());
                log.info("更新用户[{}]成功", cpUserParam.getUserid());
            } else if (!userUpdateResult.success()
                    && WxCpErrorMsgEnum.CODE_1.getCode() != userUpdateResult.getCode()
                    && WxCpErrorMsgEnum.CODE_45033.getCode() != userUpdateResult.getCode()) {
                //设为失败
                operate.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                operate.setErrorMsg(userUpdateResult.getMsg());
                log.error("更新用户[{}]失败：{}", cpUserParam.getUserid(), userUpdateResult.getMsg());
            }
        }
    }

    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncUserBakToCp(String corpId, List<CpUserBak> userOperateList) {
        for (CpUserBak userBak : userOperateList) {
            UserSaveQo cpUserParam = JacksonUtils.toObj(JacksonUtils.toJson(userBak), UserSaveQo.class);
            try {
                //更新至企微
                Result<?> userUpdateResult = userFeign.userUpdate(corpId, cpUserParam);
                if (userUpdateResult.success()) {
                    log.info("更新用户[{}]成功", userBak.getUserid());
                } else {
                    log.error("更新用户[{}]至企微异常：{}", userBak.getUserid(), userUpdateResult.getMsg());
                }
            } catch (ServiceException e) {
                log.error("feign调用异常", e);
            }
        }
        log.info("线程[{}] ==> 恢复-异步方式同步用户至企微执行完成", Thread.currentThread().getName());
        return new AsyncResult<>(YnEnum.YES.getValue());
    }

    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncUpdateUserDeptToCp(String corpId, List<CpUser> userList) {
        for (CpUser user : userList) {
            UserSaveQo cpUserParam = new UserSaveQo();
            cpUserParam.setUserid(user.getUserid());
            cpUserParam.setDepartment(user.getDepartment());

            //更新至企微
            try {
                Result<?> userUpdateResult = userFeign.userUpdate(corpId, cpUserParam);
                if (!userUpdateResult.success()) {
                    log.error("更新用户[{}]至企微异常：{}", user.getUserid(), userUpdateResult.getMsg());
                }
            } catch (Exception e) {
                log.error("feign调用异常：{}", e.getMessage(), e);
            }
        }
        log.info("线程[{}] ==> 异步方式同步用户至企微执行完成", Thread.currentThread().getName());
        return new AsyncResult<>(YnEnum.YES.getValue());
    }

    @Override
    public List<UserInfoVo> listUsersByUseridList(List<String> useridList) {
        LambdaQueryWrapper<CpUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CpUser::getUserid, useridList);
        List<CpUser> userList = super.list(queryWrapper);
        if (CollUtil.isEmpty(userList)) {
            return Collections.emptyList();
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
    }

    @Override
    public List<UserInfoVo> listUsersByUseridAndStatusList(List<String> useridLis) {
        // 过滤退出企微的用户
        LambdaQueryWrapper<CpUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(CpUser::getStatus, UserStatusEnum.QUIT.getCode());
        queryWrapper.in(CpUser::getUserid, useridLis);
        List<CpUser> userList = super.list(queryWrapper);
        if (CollUtil.isEmpty(userList)) {
            return Collections.emptyList();
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
    }

    @Override
    public List<UserInfoVo> listUsersByDepartmentid(Long departmentid) {
        //查询用户部门关联
        List<CpUser> userList = userMapper.listUsersByDepartmentid(departmentid);
        if (CollUtil.isEmpty(userList)) {
            return Collections.emptyList();
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
    }

    @Override
    public List<UserInfoVo> listAllUsersByDepartmentid(Long departmentid) {
        CpDepartment department = departmentService.getById(departmentid);
        //部门不存在
        if (department == null) {
            return Collections.emptyList();
        }

        String idPath = department.getIdPath();
        List<CpUser> userList = userMapper.listAllUsersByDepartmentid(idPath);
        if (CollUtil.isEmpty(userList)) {
            return Collections.emptyList();
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
    }

    @Override
    public List<UserInfoVo> listAllUsersByDepartmentids(List<Long> departmentids) {
        //部门为空，直接返回
        if (CollUtil.isEmpty(departmentids)) {
            return Collections.emptyList();
        }
        List<CpDepartment> departments = departmentService.listByIds(departmentids);
        //部门不存在
        if (CollUtil.isEmpty(departments)) {
            return Collections.emptyList();
        }
        List<String> idPathList = departments.stream().map(CpDepartment::getIdPath).collect(Collectors.toList());
        // 根据idPathList查询用户(该接口会过滤掉不在企微的用户)
        List<CpUser> userList = userMapper.listAllUsersByDepartmentids(idPathList);
        return JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
    }

    @Override
    public List<UserInfoVo> listAllUserByTagList(List<Long> tagIds) {
        //标签为空，直接返回
        if (CollUtil.isEmpty(tagIds)) {
            return Collections.emptyList();
        }
        // 根据标签ids获取对应的用户ids
        List<CpUserTagRelation> userTagRelations = userTagRelationService.list(new LambdaQueryWrapper<CpUserTagRelation>()
                .in(CpUserTagRelation::getTagid, tagIds));
        if (CollUtil.isEmpty(userTagRelations)) {
            return Collections.emptyList();
        }
        List<String> userIds = userTagRelations.stream().map(CpUserTagRelation::getUserid).collect(Collectors.toList());
        // 获取对应标签下的用户
        List<UserInfoVo> userList = listUsersByUseridAndStatusList(userIds);
        return JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
    }

    @Override
    public Set<UserInfoVo> getUsersScope(UserInfoQo userInfoQo) {
        List<String> useridList = userInfoQo.getUseridList();
        List<Long> departmentidList = userInfoQo.getDepartmentidList();
        List<Long> tagidList = userInfoQo.getTagidList();

        //最终的成员userid列表
        Set<UserInfoVo> finalUseridSet = new HashSet<>();

        // 根据用户ids获取对应的用户信息
        if (CollUtil.isNotEmpty(useridList)) {
            List<UserInfoVo> userInfos = listUsersByUseridAndStatusList(useridList);
            if (CollUtil.isNotEmpty(userInfos)) {
                finalUseridSet.addAll(userInfos);
            }
        }

        // 根据部门ids获取部门下对应的成员
        if (CollUtil.isNotEmpty(departmentidList)) {
            List<UserInfoVo> departmentUserInfos = listAllUsersByDepartmentids(departmentidList);
            if (CollUtil.isNotEmpty(departmentUserInfos)) {
                finalUseridSet.addAll(departmentUserInfos);
            }
        }

        // 根据标签ids获取对应的用户信息
        if (CollUtil.isNotEmpty(tagidList)) {
            List<UserInfoVo> tagUserInfos = listAllUserByTagList(tagidList);
            if (CollUtil.isNotEmpty(tagUserInfos)) {
                finalUseridSet.addAll(tagUserInfos);
            }
        }

        Collection<UserInfoVo> userInfoVos = finalUseridSet.stream().collect(Collectors.toMap(UserInfoVo::getUserid, userInfoVo -> userInfoVo, (oldValue, newValue) -> newValue)).values();
        finalUseridSet.clear();
        finalUseridSet.addAll(userInfoVos);
        return finalUseridSet;
    }

    @Override
    public Page<UserInfoVo> pageUserInfo(UserInfoPageQo pageQo) {
        Page<UserInfoVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //是否查询子部门部门
        if (pageQo.getDepartmentId() != null && (YnEnum.YES.value() == pageQo.getFetchChild())) {
            CpDepartment department = departmentService.getById(pageQo.getDepartmentId());
            //设置idPath查询
            pageQo.setDepartmentIdPath(department.getIdPath());
        }

        List<CpUser> userList = userMapper.pageUserInfoVo(page, pageQo);
        if (CollUtil.isEmpty(userList)) {
            return page;
        }

        //查询用户部门
        List<Long> deptList = new ArrayList<>();
        userList.stream().map(CpUser::getDepartment).forEach(deptList::addAll);
        deptList = deptList.stream().distinct().collect(Collectors.toList());
        List<CpDepartment> departmentList;
        //如果数量小于1000，则按条件查询
        if (deptList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            departmentList = departmentService.listByIds(deptList);
        } else {
            departmentList = departmentService.list();
        }
        Map<Long, CpDepartment> departmentMap = departmentList.stream().collect(Collectors.toMap(CpDepartment::getId, Function.identity()));

        //查询用户标签关联
        List<CpUserTagRelation> userTagRelationList;
        //如果数量小于1000，则按条件查询
        if (userList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            List<String> useridList = userList.stream().map(CpUser::getUserid).collect(Collectors.toList());
            userTagRelationList = userTagRelationService.listTagRelationByUseridList(useridList);
        } else {
            userTagRelationList = userTagRelationService.list();
        }
        Map<String, List<Long>> userTagMap = userTagRelationList.stream().collect(
                Collectors.groupingBy(CpUserTagRelation::getUserid, Collectors.mapping(CpUserTagRelation::getTagid, Collectors.toList())));

        //查询部门标签关联
        List<CpDepartmentTagRelation> departmentTagRelationList = departmentTagRelationService.list();
        Map<Long, List<Long>> departmentTagMap = departmentTagRelationList.stream().collect(
                Collectors.groupingBy(CpDepartmentTagRelation::getDepartmentid, Collectors.mapping(CpDepartmentTagRelation::getTagid, Collectors.toList())));

        //合并tagId
        List<Long> tagidList = Stream.concat(
                userTagRelationList.stream().map(CpUserTagRelation::getTagid).distinct(),
                departmentTagRelationList.stream().map(CpDepartmentTagRelation::getTagid).distinct()
        ).distinct().collect(Collectors.toList());

        //查询标签
        Map<Long, CpTag> tagMap = new HashMap<>();
        if (CollUtil.isNotEmpty(tagidList)) {
            List<CpTag> tagList;
            //如果数量小于1000，则按条件查询
            if (tagidList.size() < AddressbookConstant.IN_QUERY_SIZE) {
                tagList = tagService.listByIds(tagidList);
            } else {
                tagList = tagService.list();
            }
            tagMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));
        }


        //查询用户标签关联
        List<StdStudentInfo> stdStudentInfoList;
        //如果数量小于1000，则按条件查询
        if (userList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            List<String> useridList = userList.stream().map(CpUser::getUserid).collect(Collectors.toList());
            stdStudentInfoList = stdStudentInfoService.list(new LambdaQueryWrapper<StdStudentInfo>().in(StdStudentInfo::getUserId, useridList));
        } else {
            stdStudentInfoList = stdStudentInfoService.list();
        }
        Map<String, StdStudentInfo> stdStudentInfoMap = stdStudentInfoList.stream().collect(Collectors.toMap(StdStudentInfo::getUserId, Function.identity()));


        List<UserInfoVo> voList = JacksonUtils.toObj(JacksonUtils.toJson(userList), new TypeReference<>() {
        });
        //设置部门、标签、用户字段信息
        setField(voList, departmentMap, userTagMap, tagMap, departmentTagMap, stdStudentInfoMap, pageQo.getDesensitization());

        page.setRecords(voList);
        return page;
    }

    /**
     * 设置字段
     */
    private void setField(List<UserInfoVo> voList, Map<Long, CpDepartment> departmentMap, Map<String, List<Long>> userTagMap, Map<Long, CpTag> tagMap, Map<Long, List<Long>> departmentTagMap, Map<String, StdStudentInfo> stdStudentInfoMap, Boolean desensitization) {
        for (UserInfoVo userInfo : voList) {
            //设置主部门信息
            CpDepartment mainDept = departmentMap.get(userInfo.getMainDepartment());
            userInfo.setMainDepartmentName(mainDept.getName());
            userInfo.setMainPath(mainDept.getPath());

            //部门用[,]拼接 主部门放在第一位
            StringBuilder deptStr = new StringBuilder();
            deptStr.append(mainDept.getName());

            List<UserInfoVo.DepartmentVo> departmentVoList = new ArrayList<>();
            //用户关联部门（包含父部门）
            Set<Long> userRelateDeptSet = new HashSet<>();
            //设置部门信息
            for (Long deptId : userInfo.getDepartment()) {
                CpDepartment department = departmentMap.get(deptId);
                UserInfoVo.DepartmentVo departmentVo = UserInfoVo.DepartmentVo.builder()
                        .departmentid(deptId)
                        .departmentName(department.getName())
                        .departmentPath(department.getPath())
                        .build();
                departmentVoList.add(departmentVo);

                //设置部门str
                if (!department.getId().equals(mainDept.getId())) {
                    deptStr.append(StrPool.COMMA).append(department.getName());
                }

                //设置用户关联部门
                String idPath = department.getIdPath();
                Set<Long> ids = Arrays.stream(idPath.split(StrPool.SLASH)).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toSet());
                userRelateDeptSet.addAll(ids);
            }
            userInfo.setDepartmentVoList(departmentVoList);
            userInfo.setDeptStr(deptStr.toString());

            //设置用户标签信息
            List<Long> tagIds = userTagMap.get(userInfo.getUserid());
            if (CollUtil.isNotEmpty(tagIds)) {
                List<UserInfoVo.TagVo> tagVoList = tagIds.stream().map(tagId ->
                                UserInfoVo.TagVo.builder()
                                        .tagid(tagId)
                                        .tagname(Optional.ofNullable(tagMap.get(tagId)).map(CpTag::getTagname).orElse(null)).build())
                        .collect(Collectors.toList());
                userInfo.setTagVoList(tagVoList);
                //设置标签str
                userInfo.setTagStr(tagVoList.stream().map(UserInfoVo.TagVo::getTagname).collect(Collectors.joining(StrPool.COMMA)));
            }

            //设置部门标签信息
            List<Long> deptTagIds = userRelateDeptSet.stream()
                    .map(departmentTagMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deptTagIds)) {
                List<UserInfoVo.TagVo> tagVoList = deptTagIds.stream().map(tagId ->
                                UserInfoVo.TagVo.builder()
                                        .tagid(tagId)
                                        .tagname(Optional.ofNullable(tagMap.get(tagId)).map(CpTag::getTagname).orElse(null))
                                        .build())
                        .collect(Collectors.toList());
                userInfo.setDeptTagVoList(tagVoList);
            }
            //设置年级和院校
            StdStudentInfo studentInfo = stdStudentInfoMap.get(userInfo.getUserid());
            if(studentInfo!=null) {
                userInfo.setGrade(studentInfo.getGrade());
                userInfo.setCollegeName(studentInfo.getCollegeName());
            }

            //设置性别
            userInfo.setGenderDesc(EnumUtil.getFieldBy(GenderEnum::getDesc, GenderEnum::getCode, userInfo.getGender()));

            //设置状态
            userInfo.setStatusDesc(EnumUtil.getFieldBy(UserStatusEnum::getDesc, UserStatusEnum::getCode, userInfo.getStatus()));

            //手机号脱敏
            if (desensitization) {
                userInfo.setMobile(DesensitizedUtil.mobilePhone(userInfo.getMobile()));
            }
        }
    }

    @Override
    public Integer getUserInfoCount(UserInfoPageQo userInfoPageQo) {
        return userMapper.pageUserInfoVoCount(userInfoPageQo);
    }

    @Override
    public List<String> listNotExistUser(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        //不存在的用户列表
        List<String> notExistUserIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(userIds)) {
            List<CpUser> userList = super.list();
            if (CollUtil.isEmpty(userList)) {
                return Collections.emptyList();
            }
            //用户set
            Set<String> userIdSet = userList.stream().map(CpUser::getUserid).collect(Collectors.toSet());
            for (String userId : userIds) {
                if (!userIdSet.contains(userId)) {
                    notExistUserIds.add(userId);
                }
            }
        }
        return notExistUserIds;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeMobile(ChangeMobileQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();
        //获取通用应用id
        String agentId = agentProperties.getCommonAgentId();

        //查询企微用户
        Result<UserDto> userResult = userFeign.userGet(corpId, agentProperties.getCommonAgentId(), qo.getUserid());
        if (userResult.hasError()) {
            log.error("用户更换手机号-查询企微用户异常, userid:{}, code:{}, msg:{}", qo.getUserid(), userResult.getCode(), userResult.getMsg());
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.build("用户绑定手机号-查询企微用户失败").toServiceException();
        }
        UserDto user = userResult.getResult();

        //用户标准表
        LambdaUpdateWrapper<CpUser> userUpdateWrapper = Wrappers.lambdaUpdate(CpUser.class);
        userUpdateWrapper.set(CpUser::getMobile, qo.getMobile());
        userUpdateWrapper.eq(CpUser::getUserid, qo.getUserid());

        //如果用户未激活 可以直接修改手机号
        if (UserStatusEnum.INACTIVE.getCode() == user.getStatus()) {
            UserSaveQo userUpdateQo = UserSaveQo.builder().userid(qo.getUserid()).mobile(qo.getMobile()).build();
            Result<?> userUpdateResult = userFeign.userUpdate(corpId, userUpdateQo);
            if (!userUpdateResult.success()) {
                log.error("用户更换手机号-企微更新手机号异常, userid:{}, mobile:{}, code:{}, msg:{}", qo.getUserid(), qo.getMobile(), userUpdateResult.getCode(), userUpdateResult.getMsg());
                throw new ServiceException(userUpdateResult.getMsg(), userUpdateResult.getCode());
            }
            boolean isSuccess = true;
            //更新手机号不管成功失败都不会报错，再查一次
            UserGetuseridQo userGetuseridQo = UserGetuseridQo.builder().mobile(qo.getMobile()).build();
            Result<UserIdDTO> userIdDTOResult = userFeign.userGetuserid(corpId, agentId, userGetuseridQo);
            if (userIdDTOResult.hasError()) {
                log.error("用户更换手机号-企微通过手机号查询用户id异常, mobile:{}, code:{}, msg:{}", qo.getMobile(), userIdDTOResult.getCode(), userIdDTOResult.getMsg());
//                throw new ServiceException(userIdDTOResult.getMsg(), userIdDTOResult.getCode());
                // 未查询到说明直接更新手机号失败了。
                isSuccess = false;
            }
            if (isSuccess) {
                UserIdDTO userIdDTO = userIdDTOResult.getResult();
                //如果没有更新成功，则抛异常
                if (!qo.getUserid().equals(userIdDTO.getUserid())) {
                    log.error("用户更换手机号-更新手机号异常, userid:{}, mobile:{}", qo.getUserid(), qo.getMobile());
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("修改手机号失败，请重试").toServiceException();
                }

                log.info("用户更换手机号-企微更新手机号成功, userid:{}, mobile:{}", qo.getUserid(), qo.getMobile());

                //更新用户标准表
                super.update(userUpdateWrapper);
                return;
            }
        }

        //校验手机号
        verifyMobile(qo, corpId, agentId);

        //获取用户的Tag集合
        LambdaQueryWrapper<CpUserTagRelation> userTagRelaitonQueryWrapper = Wrappers.lambdaQuery(CpUserTagRelation.class);
        userTagRelaitonQueryWrapper.eq(CpUserTagRelation::getUserid, qo.getUserid());
        List<CpUserTagRelation> userTagRelations = userTagRelationService.list(userTagRelaitonQueryWrapper);
        List<Long> tagIds = userTagRelations.stream().map(CpUserTagRelation::getTagid).collect(Collectors.toList());

        //删除用户企微会回调删除标签成员，这里设置一个标识，回调时去除该userid
        redisUtils.leftPush(CommonServiceRedisKeys.COMMON_SERVICE_CALLBACK_USER_TAG_EXCLUDE_USERID, qo.getUserid(), RedisUtils.HOUR_ONE_EXPIRE);

        //先删除原有账号
        Result<?> userDeleteResult = userFeign.userDelete(corpId, qo.getUserid());
        if (!userDeleteResult.success()) {
            log.error("用户更换手机号-企微删除用户异常, userid:{}, code:{}, msg:{}", qo.getUserid(), userDeleteResult.getCode(), userDeleteResult.getMsg());
            throw new ServiceException(userDeleteResult.getMsg(), userDeleteResult.getCode());
        }

        //找到用户修改本地用户信息
        //新增账号(保留原始成员信息、部门关系)
        user.setMobile(qo.getMobile());
        //将之前企业邮箱的数据置为空，防止因为企业微信存在导致加入人员失败
        user.setBizMail(null);
        UserSaveQo userSaveQo = JacksonUtils.toObj(JacksonUtils.toJson(user), UserSaveQo.class);
        Result<?> userCreateResult = userFeign.userCreate(corpId, Boolean.TRUE, userSaveQo);
        if (!userCreateResult.success()) {
            throw new ServiceException(userCreateResult.getMsg(), userCreateResult.getCode());
        }

        //新增的用户状态为未激活
        user.setStatus(UserStatusEnum.INACTIVE.getCode());
        userUpdateWrapper.set(CpUser::getStatus, UserStatusEnum.INACTIVE.getCode());
        //更新用户标准表
        super.update(userUpdateWrapper);

        //保留原始成员-标签关系
        if (CollUtil.isNotEmpty(tagIds)) {
            //用户更换手机号保存标签
            changeMobileSaveUserTag(qo, tagIds, corpId);
        }

        //更新用户缓存
        CpUser cpUser = super.getById(user.getUserid());
        redisService.setCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, qo.getUserid(), JacksonUtils.toJson(cpUser));

        log.info("用户更换手机号-删除再新增用户成功, userid:{}, mobile:{}", qo.getUserid(), qo.getMobile());
    }

    /**
     * 用户更换手机号保存标签
     */
    private void changeMobileSaveUserTag(ChangeMobileQo qo, List<Long> tagIds, String corpId) {
        //查询标签
        List<CpTag> tags = tagService.listByIds(tagIds);
        Map<Long, Integer> tagMap = CollUtil.toMap(tags, null, CpTag::getTagid, CpTag::getAgentId);
        List<String> userList = Collections.singletonList(qo.getUserid());
        for (Long tagId : tagIds) {
            TagUsersQo tagUsersQo = TagUsersQo.builder().tagId(tagId).userList(userList).build();

            //设置agentId
            String agentIdStr = Optional.ofNullable(tagMap.get(tagId)).map(String::valueOf).orElse(null);

            Result<TagAddOrRemoveUsersDto> result = tagFeign.addTagUsers(corpId, agentIdStr, tagUsersQo);
            if (result.hasError()) {
                log.error("用户更换手机号-企微更新标签成员异常, tagid:{}, userList:{}, code:{}, msg:{}", tagId, userList, result.getCode(), result.getMsg());
            }
        }
    }

    /**
     * 校验手机号
     */
    private void verifyMobile(ChangeMobileQo qo, String corpId, String agentId) {

        //本地可以查询用户敏感信息，则查询标准表
        if (Boolean.TRUE.equals(agentProperties.getSensitiveInfoEnabled())) {
            CpUser mobileUser = super.getOne(new LambdaQueryWrapper<CpUser>().eq(CpUser::getMobile, qo.getMobile()));
            if (mobileUser != null) {
                if (qo.getUserid().equals(mobileUser.getUserid())) {
                    log.error("用户更换手机号-手机号未变更，无需提交修改手机号, userid:{}, mobile:{}", qo.getUserid(), qo.getMobile());
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("手机号未变更，无需提交修改手机号").toServiceException();
                } else {
                    log.error("用户更换手机号-手机号已被其他成员[{}]占用, userid:{}, mobile:{}", mobileUser.getUserid(), qo.getUserid(), qo.getMobile());
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("手机号已被其他成员[" + mobileUser.getUserid() + "]占用").toServiceException();
                }
            }


        } else {
            //应用不能查询敏感信息，则调用企微查询手机号
            verifyCpMobile(qo, corpId, agentId);
        }
    }

    /**
     * 校验企微手机号
     */
    private void verifyCpMobile(ChangeMobileQo qo, String corpId, String agentId) {
        UserGetuseridQo userGetuseridQo = UserGetuseridQo.builder().mobile(qo.getMobile()).build();
        Result<UserIdDTO> userIdDTOResult = userFeign.userGetuserid(corpId, agentId, userGetuseridQo);
        if (userIdDTOResult.success()) {
            UserIdDTO result = userIdDTOResult.getResult();
            String userIdQuery = result.getUserid();
            if (qo.getUserid().equals(userIdQuery)) {
                log.error("用户更换手机号-手机号未变更，无需提交修改手机号, userid:{}, mobile:{}", qo.getUserid(), qo.getMobile());
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("手机号未变更，无需提交修改手机号").toServiceException();
            } else {
                log.error("用户更换手机号-手机号已被其他成员[{}]占用, userid:{}, mobile:{}", userIdQuery, qo.getUserid(), qo.getMobile());
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("手机号已被其他成员[" + userIdQuery + "]占用").toServiceException();
            }

        } else {
            //如果不是【用户不存在】的异常，则抛异常
            if (WxCpErrorMsgEnum.CODE_46004.getCode() != userIdDTOResult.getCode()) {
                throw new ServiceException(userIdDTOResult.getMsg(), userIdDTOResult.getCode());
            }
        }
    }

    @Async("commonTaskExecutor")
    @Override
    public Future<Integer> asyncUserDeptRelationToCp(String corpId, List<UserSaveQo> userList, Map<String, List<CpUserDepartmentRelationOperate>> userDeptMap) {
        for (UserSaveQo qo : userList) {
            List<CpUserDepartmentRelationOperate> operateList = userDeptMap.get(qo.getUserid());

            //更新至企微
            Result<?> userUpdateResult = userFeign.userUpdate(corpId, qo);
            if (userUpdateResult.success()) {
                operateList.forEach(item -> item.setOperateStatus(OperateStatusEnum.SUCCESS.getCode()));

                log.info("更新用户部门关系[{}]成功", qo.getUserid());
            } else if (!userUpdateResult.success()
                    && WxCpErrorMsgEnum.CODE_1.getCode() != userUpdateResult.getCode()
                    && WxCpErrorMsgEnum.CODE_45033.getCode() != userUpdateResult.getCode()) {
                //设为失败
                operateList.forEach(item -> {
                    item.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                    item.setErrorMsg(userUpdateResult.getMsg());
                });
                log.error("更新用户部门关系[{}]失败：{}", qo.getUserid(), userUpdateResult.getMsg());
            }
        }

        log.info("线程[{}] ==> 异步方式同步用户部门关系至企微执行完成", Thread.currentThread().getName());
        return new AsyncResult<>(YnEnum.YES.getValue());
    }

    @Override
    public UserDeptIdsTagIdsDto getDeptIdsAndTagIdsByUserId(String userid) {
        //查询用户是否存在
        CpUser user = super.getById(userid);
        if (user == null) {
            throw ExceptionCodeEnum.USER_NOT_EXIST.toServiceException();
        }

        UserDeptIdsTagIdsDto dto = new UserDeptIdsTagIdsDto();
        dto.setUserid(userid);

        //查询用户部门关联
        LambdaQueryWrapper<CpUserDepartmentRelation> userDeptWrapper = Wrappers.lambdaQuery(CpUserDepartmentRelation.class);
        userDeptWrapper.eq(CpUserDepartmentRelation::getUserid, userid);
        List<CpUserDepartmentRelation> userDeptRelationList = userDepartmentRelationService.list(userDeptWrapper);

        List<Long> allDeptIds = null;
        if (CollUtil.isNotEmpty(userDeptRelationList)) {
            List<Long> deptIds = userDeptRelationList.stream().map(CpUserDepartmentRelation::getDepartmentid).collect(Collectors.toList());
            //查询部门idPath，获取父级部门id
            List<CpDepartment> departmentList = departmentService.listByIds(deptIds);
            Set<Long> deptIdSet = new HashSet<>();
            departmentList.stream().map(CpDepartment::getIdPath).map(item -> CharSequenceUtil.split(item, StrPool.SLASH))
                    .forEach(strings -> strings.forEach(item -> {
                        if (StringUtils.isNotEmpty(item)) deptIdSet.add(Long.valueOf(item));
                    }));
            allDeptIds = new ArrayList<>(deptIdSet);
            dto.setDepartmentIds(allDeptIds);
        }

        //查询用户标签关联
        LambdaQueryWrapper<CpUserTagRelation> userTagWrapper = Wrappers.lambdaQuery(CpUserTagRelation.class);
        userTagWrapper.eq(CpUserTagRelation::getUserid, userid);
        List<CpUserTagRelation> userTagRelationList = userTagRelationService.list(userTagWrapper);
        if (CollUtil.isNotEmpty(userTagRelationList)) {
            dto.setTagIds(userTagRelationList.stream().map(CpUserTagRelation::getTagid).collect(Collectors.toList()));
        }

        //查询用户部门及所有上级部门关联的标签
        if (CollUtil.isNotEmpty(allDeptIds)) {
            List<CpDepartmentTagRelation> deptTags = departmentTagRelationService.listByDeptIds(allDeptIds);
            if (CollUtil.isNotEmpty(deptTags)) {
                if (dto.getTagIds() == null) {
                    dto.setTagIds(new ArrayList<>());
                }
                dto.getTagIds().addAll(deptTags.stream().map(CpDepartmentTagRelation::getTagid).collect(Collectors.toList()));
            }
        }
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUserByMsg(WxCpXmlMessage wxMessage) {
        Result<UserDto> result = userFeign.userGet(agentProperties.getCorpId(), agentProperties.getCommonAgentId(), wxMessage.getUserId());
        if (result.hasError()) {
            log.error("通讯录回调-查询用户[{}]失败：{}", wxMessage.getUserId(), result.getMsg());
            return;
        }

        UserDto userDto = result.getResult();
        CpUser user = JacksonUtils.toObj(JacksonUtils.toJson(userDto), CpUser.class);

        //如果newUserid不为空，说明userid发生了变更，需要更新userid
        if (StringUtils.isNotBlank(wxMessage.getNewUserId())) {
            LambdaUpdateWrapper<CpUser> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(CpUser::getUserid, wxMessage.getNewUserId());
            updateWrapper.eq(CpUser::getUserid, wxMessage.getUserId());
            super.update(updateWrapper);
        } else {
            //新增或更新标准表
            super.saveOrUpdate(user);
        }

        log.info("通讯录回调-保存用户[{}] 成功", wxMessage.getUserId());
    }

    @Override
    public void delUserByMsg(WxCpXmlMessage wxMessage) {
        //删除标准表
        super.removeById(wxMessage.getUserId());
        log.info("通讯录回调-删除用户[{}]成功", wxMessage.getUserId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveUserWithTag(UserSaveWithTagQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        if (OperateTypeEnum.SAVE.getCode() == qo.getOperateType()) {
            //新增用户
            return saveUser(qo, corpId);

        } else if (OperateTypeEnum.UPDATE.getCode() == qo.getOperateType()) {
            //更新用户
            return updateUser(qo, corpId);

        } else {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("操作类型错误").toServiceException();
        }

    }

    /**
     * 更新用户
     */
    private String updateUser(UserSaveWithTagQo qo, String corpId) {
        //查询用户是否存在
        CpUser useridQuery = super.getById(qo.getUserid());
        if (useridQuery == null) {
            throw ExceptionCodeEnum.USER_NOT_EXIST.toServiceException();
        }

        //更新企微用户
        UserSaveQo userSaveQo = JacksonUtils.toObj(JacksonUtils.toJson(qo), UserSaveQo.class);
        Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
        if (result.hasError()) {
            throw new ServiceException(String.format("更新企微用户失败：%s", result.getMsg()), result.getCode());
        }

        //更新用户表
        CpUser user = JacksonUtils.toObj(JacksonUtils.toJson(qo), CpUser.class);
        super.updateById(user);

        //设置用户缓存
        CpUser cpUser = super.getById(qo.getUserid());
        redisService.setCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, qo.getUserid(), JacksonUtils.toJson(cpUser));

        //更新用户-处理标签
        String tagAddFailMsg = updateUserProcessTag(qo, corpId);

        //更新用户-处理部门
        updateUserProcessDept(qo);
        return tagAddFailMsg;
    }

    /**
     * 更新用户-处理部门
     */
    private void updateUserProcessDept(UserSaveWithTagQo qo) {
        //如果存在部门负责人，则校验是否需要更新部门表
        List<Long> leaderDepartmentList = qo.getLeaderDepartmentList();
        if (CollUtil.isNotEmpty(qo.getLeaderDepartmentList())) {
            //部门更新列表
            List<CpDepartment> updateDeptList = new ArrayList<>();
            List<CpDepartment> departmentList = departmentService.listByIds(leaderDepartmentList);
            for (CpDepartment dept : departmentList) {
                List<String> departmentLeader = dept.getDepartmentLeader();
                //如果不存在当前用户，则添加
                if (!departmentLeader.contains(qo.getUserid())) {
                    //部门负责人添加当前用户
                    departmentLeader.add(qo.getUserid());
                    updateDeptList.add(dept);
                }
            }
            //更新部门表
            if (CollUtil.isNotEmpty(updateDeptList)) {
                departmentService.updateBatchById(departmentList);
            }
        }

        //更新用户-处理部门关联
        updateUserProcessDeptRelation(qo);
    }

    /**
     * 更新用户-处理部门关联
     */
    private void updateUserProcessDeptRelation(UserSaveWithTagQo qo) {
        //查询用户部门关联
        List<CpUserDepartmentRelation> userDepartmentRelations = userDepartmentRelationService.listByUserId(qo.getUserid());
        List<Long> oldDeptIdList = userDepartmentRelations.stream().map(CpUserDepartmentRelation::getDepartmentid).collect(Collectors.toList());

        //需要新增的部门关联
        List<Long> addDeptIdList = CollUtil.subtractToList(qo.getDepartment(), oldDeptIdList);
        //需要被删除的部门关联
        List<Long> delDeptIdList = CollUtil.subtractToList(oldDeptIdList, qo.getDepartment());
        //需要更新的部门关联
        List<Long> updateDeptIdList = new ArrayList<>(CollUtil.intersectionDistinct(qo.getDepartment(), oldDeptIdList));

        //新增用户部门关联表
        if (CollUtil.isNotEmpty(addDeptIdList)) {
            //新增用户部门关联表
            List<CpUserDepartmentRelation> userDeptRelationList = new ArrayList<>();
            for (Long deptId : addDeptIdList) {
                CpUserDepartmentRelation userDeptRelation = CpUserDepartmentRelation.builder()
                        .userid(qo.getUserid())
                        .departmentid(deptId)
                        //是否是部门负责人 下标和部门一致
                        .isLeader(qo.getIsLeaderInDept().get(qo.getDepartment().indexOf(deptId)))
                        .build();
                userDeptRelationList.add(userDeptRelation);
            }
            userDepartmentRelationService.saveBatch(userDeptRelationList);
        }

        //删除用户部门关联表
        if (CollUtil.isNotEmpty(delDeptIdList)) {
            LambdaQueryWrapper<CpUserDepartmentRelation> queryWrapper = Wrappers.lambdaQuery(CpUserDepartmentRelation.class)
                    .eq(CpUserDepartmentRelation::getUserid, qo.getUserid())
                    .in(CpUserDepartmentRelation::getDepartmentid, delDeptIdList);
            userDepartmentRelationService.remove(queryWrapper);
        }

        //更新用户部门关联表
        if (CollUtil.isNotEmpty(updateDeptIdList)) {
            Map<String, CpUserDepartmentRelation> relationMap = userDepartmentRelations.stream().collect(
                    Collectors.toMap(item -> item.getUserid() + "-" + item.getDepartmentid(), Function.identity()));

            List<CpUserDepartmentRelation> userDeptRelationUpdateList = new ArrayList<>();
            for (Long deptId : updateDeptIdList) {
                //是否为部门负责人 下标和部门一致
                Integer isLeader = qo.getIsLeaderInDept().get(qo.getDepartment().indexOf(deptId));
                //如果是否为部门负责人发生变化，则更新
                CpUserDepartmentRelation oldRelation = relationMap.get(qo.getUserid() + "-" + deptId);
                if (!oldRelation.getIsLeader().equals(isLeader)) {
                    CpUserDepartmentRelation userDeptRelation = CpUserDepartmentRelation.builder()
                            .id(oldRelation.getId())
                            .isLeader(isLeader)
                            .build();
                    userDeptRelationUpdateList.add(userDeptRelation);
                }
            }
            userDepartmentRelationService.updateBatchById(userDeptRelationUpdateList);
        }
    }

    /**
     * 更新用户-处理标签
     */
    private String updateUserProcessTag(UserSaveWithTagQo qo, String corpId) {
        //查询用户标签关联
        List<CpUserTagRelation> userTagRelations = userTagRelationService.listTagRelationByUserid(qo.getUserid());
        //旧标签
        List<Long> oldTagidList = userTagRelations.stream().map(CpUserTagRelation::getTagid).collect(Collectors.toList());
        //入参标签
        List<Long> tagidList = qo.getTagidList();
        //去重
        tagidList = CollUtil.distinct(tagidList);

        //全部idList
        List<Long> allTagidList = new ArrayList<>(CollUtil.unionDistinct(oldTagidList, tagidList));
        //需要新增的标签关联
        List<Long> addTagidList = CollUtil.subtractToList(tagidList, oldTagidList);
        //需要被删除的标签关联
        List<Long> delTagidList = CollUtil.subtractToList(oldTagidList, tagidList);

        //查询标签列表
        List<CpTag> tags = tagService.listByIds(allTagidList);
        Map<Long, CpTag> tagMap = tags.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        //如果标签不是通讯录应用创建的且没有记录agentId则会报没有权限
        //因为此时用户已经更新成功，所以不报错，仅展示标签操作失败信息
        StringBuilder tagAddFailMsg = new StringBuilder();
        //标签新增用户
        tagAddUser(qo, corpId, addTagidList, tagMap, tagAddFailMsg);
        //标签删除用户
        tagDelUser(qo, corpId, delTagidList, tagMap, tagAddFailMsg);

        return tagAddFailMsg.toString();
    }

    /**
     * 标签删除用户
     */
    private void tagDelUser(UserSaveWithTagQo qo, String corpId, List<Long> delTagidList, Map<Long, CpTag> tagMap, StringBuilder tagAddFailMsg) {
        //需要被删除的标签关联不为空，则删除企微标签成员
        if (CollUtil.isNotEmpty(delTagidList)) {
            //操作成功列表
            List<Long> successTagIdList = new ArrayList<>();
            for (Long tagid : delTagidList) {
                TagUsersQo tagUsersQo = TagUsersQo.builder().tagId(tagid).userList(Collections.singletonList(qo.getUserid())).build();
                //设置agentId
                CpTag tag = tagMap.get(tagid);
                String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);
                Result<TagAddOrRemoveUsersDto> delTagUsersResult = tagFeign.delTagUsers(corpId, agentIdStr, tagUsersQo);
                if (delTagUsersResult.hasError()) {
                    //记录失败信息
                    tagAddFailMsg.append("[").append(tag.getTagname()).append("]删除标签成员失败:").append(delTagUsersResult.getMsg()).append(";");
                } else {
                    //删除成功列表
                    successTagIdList.add(tagid);
                }
            }

            //删除用户标签关联表
            if (CollUtil.isNotEmpty(successTagIdList)) {
                LambdaQueryWrapper<CpUserTagRelation> queryWrapper = Wrappers.lambdaQuery(CpUserTagRelation.class)
                        .eq(CpUserTagRelation::getUserid, qo.getUserid())
                        .in(CpUserTagRelation::getTagid, delTagidList);
                userTagRelationService.remove(queryWrapper);
            }
        }
    }

    /**
     * 标签新增用户
     */
    private void tagAddUser(UserSaveWithTagQo qo, String corpId, List<Long> addTagidList, Map<Long, CpTag> tagMap, StringBuilder tagAddFailMsg) {
        //需要新增的标签关联不为空，则新增企微标签成员
        if (CollUtil.isNotEmpty(addTagidList)) {
            //操作成功列表
            List<Long> successTagIdList = new ArrayList<>();
            for (Long tagid : addTagidList) {
                TagUsersQo tagUsersQo = TagUsersQo.builder().tagId(tagid).userList(Collections.singletonList(qo.getUserid())).build();
                //设置agentId
                CpTag tag = tagMap.get(tagid);
                String agentIdStr = Optional.ofNullable(tag.getAgentId()).map(String::valueOf).orElse(null);
                Result<TagAddOrRemoveUsersDto> addTagUsersResult = tagFeign.addTagUsers(corpId, agentIdStr, tagUsersQo);
                if (addTagUsersResult.hasError()) {
                    //记录失败信息
                    tagAddFailMsg.append("[").append(tag.getTagname()).append("]新增标签成员失败:").append(addTagUsersResult.getMsg()).append(";");
                } else {
                    //添加成功列表
                    successTagIdList.add(tagid);
                }
            }
            //新增用户标签关联表
            if (CollUtil.isNotEmpty(successTagIdList)) {
                List<CpUserTagRelation> userTagRelationList = successTagIdList.stream().map(item ->
                        CpUserTagRelation.builder().userid(qo.getUserid()).tagid(item).build()).collect(Collectors.toList());
                userTagRelationService.saveBatch(userTagRelationList);
            }
        }
    }

    private String saveUser(UserSaveWithTagQo qo, String corpId) {
        //查询用户是否存在
        CpUser useridQuery = super.getById(qo.getUserid());
        if (useridQuery != null) {
            throw ExceptionCodeEnum.USER_EXIST.toServiceException();
        }
        Boolean isActive = Boolean.parseBoolean(sysConfigService.selectConfigByKey("shenfen.isactive") == null ? "false" : sysConfigService.selectConfigByKey("shenfen.isactive"));
        if (isActive.booleanValue()) {
            StaffIdentifyRelation identify = staffIdentifyRelationMapper.getByNo(qo.getUserid());
            if (identify != null && StringUtils.isNotEmpty(identify.getStaffNo())) {
                if (cpUserMapper.selectByUserId(identify.getStaffNo()) != null) {
                    log.info("根据staffNo查询用户已存在，异常无需处理");
                    throw ExceptionCodeEnum.USER_EXIST.toServiceException();
                } else {
                    qo.setUserid(identify.getStaffId());
                }
            }
        }
        //新增企微用户
        UserSaveQo userSaveQo = JacksonUtils.toObj(JacksonUtils.toJson(qo), UserSaveQo.class);
        log.info("add qw user userSaveQo :{}", userSaveQo.getMobile());
        Result<Object> result = userFeign.userCreate(corpId, false, userSaveQo);
        if (result.hasError()) {
            throw new ServiceException(String.format("新增企微用户失败：%s", result.getMsg()), result.getCode());
        }

        //新增用户表
        CpUser user = JacksonUtils.toObj(JacksonUtils.toJson(qo), CpUser.class);
        //默认状态为未激活
        user.setStatus(UserStatusEnum.INACTIVE.getCode());
        super.save(user);

        //如果标签不是通讯录应用创建的且没有记录agentId则会报没有权限
        //因为此时用户已经创建成功，所以不报错，仅展示标签添加失败信息
        StringBuilder tagAddFailMsg = new StringBuilder();
        //需要新增的标签关联不为空，则新增企微列表
        if (CollUtil.isNotEmpty(qo.getTagidList())) {
            //查询标签列表
            List<CpTag> tags = tagService.listByIds(qo.getTagidList());
            Map<Long, CpTag> tagMap = tags.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));
            //如果标签列表不为空，则新增企微列表
            tagAddUser(qo, corpId, qo.getTagidList(), tagMap, tagAddFailMsg);
        }

        //处理部门
        saveUserProcessDept(qo, user);

        //设置用户缓存
        CpUser cpUser = super.getById(qo.getUserid());
        redisService.setCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, qo.getUserid(), JacksonUtils.toJson(cpUser));

        //返回失败信息
        return tagAddFailMsg.toString();
    }

    /**
     * 处理部门
     */
    private void saveUserProcessDept(UserSaveWithTagQo qo, CpUser user) {
        //如果存在部门负责人，则更新部门标准表
        List<Long> leaderDepartmentList = qo.getLeaderDepartmentList();
        if (CollUtil.isNotEmpty(qo.getLeaderDepartmentList())) {
            List<CpDepartment> departmentList = departmentService.listByIds(leaderDepartmentList);
            List<CpDepartment> depertmentUpdateList = new ArrayList<>();
            for (CpDepartment dept : departmentList) {
                List<String> departmentLeader = dept.getDepartmentLeader();
                //部门负责人为空 则新增
                if (CollUtil.isEmpty(departmentLeader)) {
                    departmentLeader = new ArrayList<>();
                    departmentLeader.add(qo.getUserid());
                    dept.setDepartmentLeader(departmentLeader);
                } else {
                    //部门负责人不包含当前用户 则添加
                    if (!dept.getDepartmentLeader().contains(qo.getUserid())) {
                        departmentLeader.add(qo.getUserid());
                    }
                }
                //部门负责人添加当前用户
                depertmentUpdateList.add(dept);
            }
            if (CollUtil.isNotEmpty(depertmentUpdateList)) {
                departmentService.updateBatchById(depertmentUpdateList);
            }
        }

        //新增用户部门关联表
        List<CpUserDepartmentRelation> userDeptRelationList = new ArrayList<>();
        //部门列表
        List<Long> deptIdList = qo.getDepartment();
        //是否是部门负责人
        List<Integer> isLeaderInDept = qo.getIsLeaderInDept();
        for (int i = 0; i < deptIdList.size(); i++) {
            Long deptId = deptIdList.get(i);
            Integer isLeader = isLeaderInDept.get(i);
            CpUserDepartmentRelation userDeptRelation = CpUserDepartmentRelation.builder()
                    .userid(user.getUserid())
                    .departmentid(deptId)
                    .isLeader(isLeader)
                    .build();
            userDeptRelationList.add(userDeptRelation);
        }
        userDepartmentRelationService.saveBatch(userDeptRelationList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUserSaveDept(BatchUserDeptQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //查询用户部门关联
        List<CpUserDepartmentRelation> userDeptList = userDepartmentRelationService
                .list(new LambdaQueryWrapper<CpUserDepartmentRelation>().in(CpUserDepartmentRelation::getUserid, qo.getUseridList()));
        //用户部门id列表
        Map<String, List<Long>> userDeptIdMap = userDeptList.stream().collect(Collectors.groupingBy(CpUserDepartmentRelation::getUserid, Collectors.mapping(CpUserDepartmentRelation::getDepartmentid, Collectors.toList())));
        //用户部门列表
        Map<String, List<CpUserDepartmentRelation>> userDeptMap = userDeptList.stream().collect(Collectors.groupingBy(CpUserDepartmentRelation::getUserid, Collectors.toList()));

        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        List<CpUser> userList = new ArrayList<>();
        List<CpUserDepartmentRelation> userDepartmentRelationList = new ArrayList<>();
        for (String userid : qo.getUseridList()) {
            List<Long> newDeptIdList = qo.getDepartmentidList();
            //旧用户部门
            List<Long> oldDeptidList = userDeptIdMap.get(userid);
            //需要新增的部门
            List<Long> newDeptList = newDeptIdList.stream().filter(item -> !oldDeptidList.contains(item)).collect(Collectors.toList());
            //如果为空则不处理
            if (CollUtil.isEmpty(newDeptList)) {
                continue;
            }

            //旧部门添加新部门
            List<Long> deptIdList = CollUtil.unionAll(oldDeptidList, newDeptList);
            UserSaveQo userSaveQo = UserSaveQo.builder().userid(userid).department(deptIdList).build();
            CpUser entity = CpUser.builder().userid(userid).department(deptIdList).build();

            //设置主部门
            if (qo.getMainDepartmentid() != null) {
                userSaveQo.setMainDepartment(qo.getMainDepartmentid());
                entity.setMainDepartment(qo.getMainDepartmentid());
            }

            List<CpUserDepartmentRelation> relationList = new ArrayList<>();

            List<CpUserDepartmentRelation> departmentRelationList = userDeptMap.get(userid);
            Map<Long, Integer> isLeaderInDeptMap = CollUtil.toMap(departmentRelationList, MapUtil.newHashMap(), CpUserDepartmentRelation::getDepartmentid, CpUserDepartmentRelation::getIsLeader);
            //设置部门负责人
            List<Integer> isLeaderInDept = new ArrayList<>();
            for (Long deptId : deptIdList) {
                Integer isLeader = isLeaderInDeptMap.get(deptId);
                //设置是否为部门负责人，新增默认为否
                if (isLeader == null) {
                    isLeader = YnEnum.NO.getValue();
                }
                isLeaderInDept.add(isLeader);

                //新增用户部门关联
                CpUserDepartmentRelation relation = CpUserDepartmentRelation.builder().userid(userid).departmentid(deptId).isLeader(isLeader).build();
                relationList.add(relation);
            }

            userSaveQo.setIsLeaderInDept(isLeaderInDept);
            entity.setIsLeaderInDept(isLeaderInDept);

            //更新企微用户部门
            Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
            if (result.hasError()) {
                //更新成功的用户和用户部门关联表
                proxy.updateUserAndUserDeptRelation(userList, userDepartmentRelationList);
                throw new ServiceException(String.format("更新用户部门失败：%s", result.getMsg()), result.getCode());
            }

            //用户标准表
            userList.add(entity);

            //过滤存量数据
            relationList.removeIf(item -> userDeptList.stream().anyMatch(item2 -> item2.getUserid().equals(item.getUserid()) && item2.getDepartmentid().equals(item.getDepartmentid())));
            //用户部门关联表
            if (CollUtil.isNotEmpty(relationList)) {
                userDepartmentRelationList.addAll(relationList);
            }
        }

        //更新用户和用户部门关联表
        proxy.updateUserAndUserDeptRelation(userList, userDepartmentRelationList);
    }

    /**
     * 更新用户和用户部门关联表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateUserAndUserDeptRelation(List<CpUser> userList, List<CpUserDepartmentRelation> userDepartmentRelationList) {
        //更新用户标准表
        if (CollUtil.isNotEmpty(userList)) {
            super.updateBatchById(userList);
        }
        //新增用户部门关联表
        if (CollUtil.isNotEmpty(userDepartmentRelationList)) {
            userDepartmentRelationService.saveBatch(userDepartmentRelationList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUserUpdateDept(BatchUserDeptQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        Long mainDepartmentid = qo.getMainDepartmentid();
        if (mainDepartmentid == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("主部门不能为空").toServiceException();
        }

        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        List<CpUser> userList = new ArrayList<>();
        List<CpUserDepartmentRelation> userDepartmentRelationList = new ArrayList<>();
        for (String userid : qo.getUseridList()) {
            List<Long> departmentidList = qo.getDepartmentidList();

            //设置是否为部门负责人，默认为否
            List<Integer> isLeaderInDept = new ArrayList<>();
            for (int i = 0; i < departmentidList.size(); i++) {
                isLeaderInDept.add(YnEnum.NO.getValue());
            }

            UserSaveQo userSaveQo = UserSaveQo.builder().userid(userid)
                    .department(departmentidList)
                    .mainDepartment(mainDepartmentid)
                    .isLeaderInDept(isLeaderInDept)
                    .build();
            //更新企微用户部门
            Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
            if (result.hasError()) {
                //更新成功的用户和用户部门关联表
                proxy.updateUserAndUserDeptRelation(qo, userList, userDepartmentRelationList);
                throw new ServiceException(String.format("更新用户部门失败：%s", result.getMsg()), result.getCode());
            }
            //用户标准表
            CpUser entity = CpUser.builder().userid(userid).department(departmentidList).mainDepartment(mainDepartmentid).build();
            userList.add(entity);

            //用户部门关联表
            List<CpUserDepartmentRelation> relationList = departmentidList.stream().map(item ->
                    CpUserDepartmentRelation.builder().userid(userid).departmentid(item).build()).collect(Collectors.toList());
            userDepartmentRelationList.addAll(relationList);
        }

        //更新用户和用户部门关联表
        proxy.updateUserAndUserDeptRelation(qo, userList, userDepartmentRelationList);
    }

    /**
     * 更新用户和用户部门关联表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateUserAndUserDeptRelation(BatchUserDeptQo qo, List<CpUser> userList, List<CpUserDepartmentRelation> userDepartmentRelationList) {
        //删除用户部门关联表
        userDepartmentRelationService.remove(new LambdaQueryWrapper<CpUserDepartmentRelation>().in(CpUserDepartmentRelation::getUserid, qo.getUseridList()));

        //更新用户标准表
        super.updateBatchById(userList);

        //新增用户部门关联表
        userDepartmentRelationService.saveBatch(userDepartmentRelationList);
    }

    @Override
    public void importUser(MultipartFile file, HttpServletResponse response) throws IOException {
        //读取Excel，头有4行
        List<UserImportDto> userImportDtoList = EasyExcelFactory.read(file.getInputStream())
                .head(UserImportDto.class)
                .sheet()
                .headRowNumber(4)
                .doReadSync();


        if (CollUtil.isEmpty(userImportDtoList)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("导入数据为空").toServiceException();
        }

        //去除学工号重复数据
        userImportDtoList = CollUtil.distinct(userImportDtoList, UserImportDto::getUserid, true);

        //查询部门
        List<CpDepartment> deptList = departmentService.list();
        //部门path -> id map
        Map<String, Long> deptMap = deptList.stream().collect(Collectors.toMap(CpDepartment::getPath, CpDepartment::getId));

        //查询存在的用户
        List<String> useridList = userImportDtoList.stream().map(UserImportDto::getUserid).collect(Collectors.toList());
        LambdaQueryWrapper<CpUser> userWrapper = Wrappers.lambdaQuery(CpUser.class);
        //如果导入数量少于1000，则条件查询
        if (useridList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            userWrapper.in(CpUser::getUserid, useridList);
        }
        List<CpUser> oldUserList = super.list(userWrapper);
        List<String> oldUseridList = oldUserList.stream().map(CpUser::getUserid).collect(Collectors.toList());

        //新增列表
        List<UserSaveQo> userSaveQoList = new ArrayList<>();
        //更新列表
        List<UserSaveQo> userUpdateQoList = new ArrayList<>();

        //错误信息
        List<UserImportErrDto> errList = BeanUtil.copyToList(userImportDtoList, UserImportErrDto.class);
        Map<String, UserImportErrDto> errMap = errList.stream().collect(Collectors.toMap(UserImportErrDto::getUserid, Function.identity()));

        //属性校验
        fieldCheck(userImportDtoList, deptMap, oldUseridList, userUpdateQoList, userSaveQoList, response, errMap);

        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();
        CommonUploadLog commonUploadLog = proxy.saveImportLogWithTransaction(file);

        //异步处理用户
        commonExecutor.execute(() -> asyncProcessUser(proxy, userSaveQoList, errMap, userUpdateQoList, oldUserList, commonUploadLog));
    }

    /**
     * 使用事务保存导入日志
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonUploadLog saveImportLogWithTransaction(MultipartFile file) {
        FileInfo uploadInfo = minIOService.upload(file);
        CommonUploadLog commonUploadLog = CommonUploadLog.builder()
                .type(CommonUploadTypeEnum.ADDRESS_BOOK_IMPORT.getCode())
                .status(CommonUploadStatusEnum.UPDATING.getCode())
                .originFileId(uploadInfo.getId())
                .build();
        commonUploadLogService.save(commonUploadLog);
        return commonUploadLog;
    }

    /**
     * 异步处理用户
     */
    private void asyncProcessUser(UserServiceImpl proxy, List<UserSaveQo> userSaveQoList, Map<String, UserImportErrDto> errMap, List<UserSaveQo> userUpdateQoList, List<CpUser> oldUserList, CommonUploadLog commonUploadLog) {
        //新增企微用户
        addCpUser(userSaveQoList, errMap);

        //更新企微用户
        updateCpUser(userUpdateQoList, errMap);

        //新增、更新成功的用户
        List<UserSaveQo> saveSuccessList = userSaveQoList.stream().filter(item -> YnEnum.YES.getValue() == item.getIsOperateSuccess()).collect(Collectors.toList());
        List<UserSaveQo> updateSuceessList = userUpdateQoList.stream().filter(item -> YnEnum.YES.getValue() == item.getIsOperateSuccess()).collect(Collectors.toList());
        //处理用户和用户部门关系
        processUserAndUserDept(proxy, saveSuccessList, updateSuceessList, oldUserList);

        //处理文件记录
        processFileLog(errMap, commonUploadLog);

        log.info("通讯录导入执行完成");
    }

    /**
     * 处理文件记录
     * 上传minio，更新文件记录的状态和对应的文件id
     */
    private void processFileLog(Map<String, UserImportErrDto> errMap, CommonUploadLog commonUploadLog) {
        //成功信息
        List<UserImportDto> successList = BeanUtil.copyToList(errMap.values().stream().filter(item -> CollUtil.isEmpty(item.getErrMsgList())).collect(Collectors.toList()), UserImportDto.class);
        if (CollUtil.isNotEmpty(successList)) {
            //生成成功文件
            MultipartFile multipartFile = ExcelUtils.genMultipartFile(successList, "通讯录导入成功列表");
            //上传成功文件到minio
            FileInfo upload = minIOService.upload(multipartFile);
            //设置成功文件id
            commonUploadLog.setSuccessFileId(upload.getId());
        }

        //错误信息
        List<UserImportErrDto> errList = errMap.values().stream().filter(item -> CollUtil.isNotEmpty(item.getErrMsgList())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errList)) {
            //拼接错误信息
            errList.forEach(item -> item.setErrMsg(String.join(StrPool.COMMA, item.getErrMsgList())));
            //生成失败文件
            MultipartFile multipartFile = ExcelUtils.genMultipartFile(errList, "通讯录导入失败列表");
            //上传失败文件到minio
            FileInfo upload = minIOService.upload(multipartFile);
            //设置失败文件id
            commonUploadLog.setFailFileId(upload.getId());
        }

        //更新上传记录 状态为已完成
        commonUploadLog.setStatus(CommonUploadStatusEnum.FINISHED.getCode());
        commonUploadLogService.updateById(commonUploadLog);
    }

    public void processUserAndUserDept(UserServiceImpl proxy, List<UserSaveQo> saveSuccessList, List<UserSaveQo> updateSuceessList, List<CpUser> oldUserList) {
        //处理用户标准表
        proxy.processStandardWithTransaction(saveSuccessList, updateSuceessList, oldUserList);

        //处理用户部门关系
        proxy.processDeptRelation(saveSuccessList, updateSuceessList);
    }

    /**
     * 处理用户标准表
     */
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public void processStandardWithTransaction(List<UserSaveQo> saveSuccessList, List<UserSaveQo> updateSuceessList, List<CpUser> oldUserList) {
        //新增用户标准表
//        saveUserStandard(saveSuccessList);
        int batchSize = 100;
        if (CollUtil.isNotEmpty(saveSuccessList)) {
            List<CpUser> userList = JacksonUtils.toObj(JacksonUtils.toJson(saveSuccessList), new TypeReference<>() {
            });
            for (CpUser user : userList) {
                //默认未激活
                user.setStatus(UserStatusEnum.INACTIVE.getCode());
                List<Integer> isLeaderInDept = new ArrayList<>();
                //默认非部门负责人
                for (int i = 0; i < user.getDepartment().size(); i++) {
                    isLeaderInDept.add(YnEnum.NO.getValue());
                }
                user.setIsLeaderInDept(isLeaderInDept);
            }
            for (List<CpUser> batch : CollUtil.split(userList, batchSize)) {
                super.saveBatch(batch);
            }
        }
        //更新用户标准表
//        updateUserStandard(updateSuceessList, oldUserList);
        if (CollUtil.isNotEmpty(updateSuceessList)) {
            Map<String, CpUser> oldUserMap = CollUtil.toMap(oldUserList, null, CpUser::getUserid, o -> o);
            List<CpUser> userList = JacksonUtils.toObj(JacksonUtils.toJson(updateSuceessList), new TypeReference<>() {
            });
            for (CpUser user : userList) {
                CpUser oldUser = oldUserMap.get(user.getUserid());
                //旧用户部门信息
                List<Long> oldDept = oldUser.getDepartment();
                List<Integer> oldIsLeaderInDept = oldUser.getIsLeaderInDept();
                List<Integer> isLeaderInDept = new ArrayList<>();
                //设置部门负责人
                for (Long deptId : user.getDepartment()) {
                    //如果存在则照旧
                    if (oldDept.contains(deptId)) {
                        isLeaderInDept.add(oldIsLeaderInDept.get(oldDept.indexOf(deptId)));
                    } else {
                        //如果不存在则默认非部门负责人
                        isLeaderInDept.add(YnEnum.NO.getValue());
                    }
                }
                user.setIsLeaderInDept(isLeaderInDept);
            }
            super.updateBatchById(userList);
        }
    }


    /**
     * 处理用户部门关系
     */
    @Transactional(rollbackFor = Exception.class, timeout = 60) // 减少超时时间
    public boolean processDeptRelation(List<UserSaveQo> saveSuccessList, List<UserSaveQo> updateSuceessList) {
        try {


            //删除旧用户部门关联表
            if (CollUtil.isNotEmpty(updateSuceessList)) {
                List<String> useridList = updateSuceessList.stream().map(UserSaveQo::getUserid).collect(Collectors.toList());
                userDepartmentRelationService.remove(new LambdaQueryWrapper<CpUserDepartmentRelation>().in(CpUserDepartmentRelation::getUserid, useridList));
            }

            List<UserSaveQo> all = new ArrayList<>();
            all.addAll(saveSuccessList);
            all.addAll(updateSuceessList);


            //新增用户部门关联表
            List<CpUserDepartmentRelation> relationList = new ArrayList<>();
            if (CollUtil.isNotEmpty(all)) {
                for (UserSaveQo userSaveQo : all) {
                    List<Long> department = userSaveQo.getDepartment();
                    for (Long deptId : department) {
                        CpUserDepartmentRelation userDepartmentRelation = CpUserDepartmentRelation.builder()
                                .userid(userSaveQo.getUserid())
                                .departmentid(deptId)
                                //默认不是部门领导
                                .isLeader(YnEnum.NO.getValue())
                                .build();
                        relationList.add(userDepartmentRelation);
                    }
                }
            }
            // 分批处理部门关系
            int batchSize = 100;
            //批量新增用户部门关联表
            if (CollUtil.isNotEmpty(relationList)) {
                // 分批保存新关系
                for (List<CpUserDepartmentRelation> batch : CollUtil.split(relationList, batchSize)) {
                    userDepartmentRelationService.saveBatch(batch);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("处理用户部门关系失败: {}", e.getMessage(), e);
            return false;
        }

    }

    /**
     * 属性校验
     */
    private void fieldCheck(List<UserImportDto> userImportDtoList, Map<String, Long> deptMap, List<String> oldUseridList, List<UserSaveQo> userUpdateQoList, List<UserSaveQo> userSaveQoList, HttpServletResponse response, Map<String, UserImportErrDto> errMap) throws IOException {
        for (UserImportDto userImportDto : userImportDtoList) {
            UserSaveQo userSaveQo = JacksonUtils.toObj(JacksonUtils.toJson(userImportDto), UserSaveQo.class);

            UserImportErrDto errDto = errMap.get(userImportDto.getUserid());
            //校验部门
            deptCheck(deptMap, userImportDto, userSaveQo, errDto);

            //校验手机号、邮箱
            mobileEmailCheck(userImportDto, errDto);

            //校验性别
            if (CharSequenceUtil.isNotBlank(userImportDto.getGender())) {
                GenderEnum gender = EnumUtil.getBy(GenderEnum.class, genderEnum -> genderEnum.getDesc().equals(userImportDto.getGender()));
                if (gender == null) {
                    errDto.getErrMsgList().add(String.format("性别[%s]不存在", userImportDto.getGender()));
                } else {
                    userSaveQo.setGender(gender.getCode());
                }
            }

            //不存在错误
            if (CollUtil.isEmpty(errDto.getErrMsgList())) {
                //如果用户存在，则更新
                if (oldUseridList.contains(userImportDto.getUserid())) {
                    userUpdateQoList.add(userSaveQo);
                    //如果用户不存在，则新增
                } else {
                    userSaveQoList.add(userSaveQo);
                }
            }
        }

        //校验是否存在错误，并导出错误数据
        checkAndExportErr(errMap, response);
    }

    /**
     * 校验手机号、邮箱
     */
    private void mobileEmailCheck(UserImportDto userImportDto, UserImportErrDto errDto) {
        String mobile = userImportDto.getMobile();
        String email = userImportDto.getEmail();
        if (CharSequenceUtil.isBlank(mobile) && CharSequenceUtil.isBlank(email)) {
            errDto.getErrMsgList().add("手机号与个人邮箱不能同时为空");
        }
        //校验手机号格式
        if (CharSequenceUtil.isNotBlank(mobile) && (!Validator.isMobile(mobile))) {
            errDto.getErrMsgList().add(String.format("手机号[%s]格式不正确", mobile));
        }
        //校验邮箱格式
        if (CharSequenceUtil.isNotBlank(email) && (!Validator.isEmail(email))) {
            errDto.getErrMsgList().add(String.format("邮箱[%s]格式不正确", email));
        }
    }

    /**
     * 校验是否存在错误，并导出错误数据
     */
    private void checkAndExportErr(Map<String, UserImportErrDto> errMap, HttpServletResponse response) throws IOException {
        //获取错误信息
        List<UserImportErrDto> errList = errMap.values().stream().filter(item -> CollUtil.isNotEmpty(item.getErrMsgList())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errList)) {
            //拼接错误信息
            errList.forEach(item -> item.setErrMsg(String.join(StrPool.COMMA, item.getErrMsgList())));
            ExcelUtils.exportExcel(response, errList, "导入失败数据");
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("导入数据存在错误").toServiceException();
        }
    }

    /**
     * 校验部门
     */
    private void deptCheck(Map<String, Long> deptMap, UserImportDto userImportDto, UserSaveQo userSaveQo, UserImportErrDto errDto) {
        String deptStr = userImportDto.getDepartmentStr();
        List<String> pathList = CharSequenceUtil.split(deptStr, ";");
        if (CollUtil.isEmpty(pathList)) {
            errDto.getErrMsgList().add("部门不能为空");
            return;
        }
        List<Long> deptIdList = new ArrayList<>();
        //根据path获取部门
        for (String path : pathList) {
            Long deptId = deptMap.get(path + StrPool.SLASH);
            if (deptId == null) {
                errDto.getErrMsgList().add(String.format("部门[%s]不存在", path));
                return;
            }
            deptIdList.add(deptId);
        }
        userSaveQo.setDepartment(deptIdList);
        //主部门为第一个
        userSaveQo.setMainDepartment(deptIdList.get(0));
    }

    @Override
    public void exportUser(UserInfoPageQo userInfoPageQo, HttpServletResponse response) throws IOException {
        //不分页查询
        userInfoPageQo.setPage(1L);
        userInfoPageQo.setSize(Long.MAX_VALUE);
        Page<UserInfoVo> page = pageUserInfo(userInfoPageQo);
        List<UserInfoVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        //导出通讯录(含敏感信息）
        if (Boolean.TRUE.equals(agentProperties.getSensitiveInfoEnabled())) {
            ExcelUtils.exportExcel(response, BeanUtil.copyToList(records, UserExportDto.class), "通讯录");
        } else {
            //导出通讯录(不含敏感信息）
            ExcelUtils.exportExcel(response, BeanUtil.copyToList(records, UserExportWithoutSensitiveInfoDto.class), "通讯录");
        }
    }

    @Override
    public void downloadImportUserTemplate(HttpServletResponse response) {
        //下载导入通讯录模板
        minIOService.download(userImportTemplateMinioId, response, false);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchEnableOrForbidUser(BatchEnableOrdForbidUserQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        List<CpUser> successList = new ArrayList<>();
        for (String userid : qo.getUseridList()) {
            //禁用/启用企微用户
            UserSaveQo userSaveQo = UserSaveQo.builder().userid(userid).enable(qo.getEnable()).build();
            Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
            if (result.hasError()) {
                //禁用/启用用户标准表 处理已经成功的用户
                proxy.batchEnableOrForbidUser(successList);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("禁用/启用用户失败").toServiceException();
            } else {
                UserStatusEnum userStatusEnum;
                //启用 用户状态为已激活
                if (YnEnum.YES.getValue() == (qo.getEnable())) {
                    userStatusEnum = UserStatusEnum.ACTIVATED;

                } else {
                    //禁用 用户状态为已禁用
                    userStatusEnum = UserStatusEnum.FORBIDDEN;
                }
                successList.add(CpUser.builder().userid(userid).status(userStatusEnum.getCode()).build());
            }
        }

        //批量启用或禁用用户
        proxy.batchEnableOrForbidUser(successList);
    }

    /**
     * 批量启用或禁用用户
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void batchEnableOrForbidUser(List<CpUser> successList) {
        if (CollUtil.isNotEmpty(successList)) {
            //禁用/启用用户标准表
            super.updateBatchById(successList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelUser(List<String> useridList) {
        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        //删除成功列表
        List<String> delSuccessList = new ArrayList<>();
        for (String userid : useridList) {
            //删除企微用户
            Result<Object> result = userFeign.userDelete(agentProperties.getCorpId(), userid);
            if (result.hasError()) {
                //删除用户标准表 处理已经成功的用户
                proxy.delUserStandard(delSuccessList);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("删除用户失败").toServiceException();
            }

            delSuccessList.add(userid);
        }

        //删除用户标准表
        proxy.delUserStandard(delSuccessList);

        //handler处理删除用户
        commonExecutor.execute(() -> handleDelUser(delSuccessList));
    }


    /**
     * handler处理删除用户
     */
    private void handleDelUser(List<String> delSuccessList) {
        AddressbookHandler handler = addressbookHandlerFactory.getHandler(addressbookProperties.getHandler());
        for (String userid : delSuccessList) {
            handler.delUser(userid);
        }
    }

    @Override
    public List<CpUser> listByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return super.listByIds(ids);
    }

    @Override
    public void batchInvite(BatchInviteQo qo) {
        Result<BatchInviteDto> result = userFeign.batchInvite(agentProperties.getCorpId(), null, qo);
        if (result.hasError()) {
            throw new ServiceException(String.format("邀请成员失败：%s", result.getMsg()), result.getCode());
        }
    }

    @Override
    public UserInfoVo getUserByNameAndMobile(String name, String mobile) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(mobile)) {
            return null;
        }

        //根据姓名与手机号查询用户
        LambdaQueryWrapper<CpUser> queryWrapper = Wrappers.lambdaQuery(CpUser.class);
        queryWrapper.eq(CpUser::getName, name);
        queryWrapper.eq(CpUser::getMobile, mobile);
        CpUser user = super.getOne(queryWrapper);

        if (user == null) {
            //如果本地未找到，则查询企微
            UserGetuseridQo userGetuseridQo = UserGetuseridQo.builder().mobile(mobile).build();
            Result<UserIdDTO> userIdDTOResult = userFeign.userGetuserid(agentProperties.getCorpId(), agentProperties.getCommonAgentId(), userGetuseridQo);
            if (userIdDTOResult.hasError()) {
                log.error("根据手机号查询用户-查询企微用户异常, mobile:{}, code:{}, msg:{}", mobile, userIdDTOResult.getCode(), userIdDTOResult.getMsg());
                return null;
            }
            String userid = userIdDTOResult.getResult().getUserid();
            Result<UserDto> userDtoResult = userFeign.userGet(agentProperties.getCorpId(), agentProperties.getCommonAgentId(), userid);
            if (userDtoResult.hasError()) {
                log.error("根据 userid 查询用户-查询企微用户异常, userid：{}, code:{}, msg:{}", userid, userDtoResult.getCode(), userDtoResult.getMsg());
                return null;
            }
            UserDto userDto = userDtoResult.getData();
            return JacksonUtils.toObj(JacksonUtils.toJson(userDto), new TypeReference<UserInfoVo>() {
            });
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(user), new TypeReference<>() {
        });
    }

    @Override
    public UserInfoVo getUserByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        //根据姓名与手机号查询用户
        LambdaQueryWrapper<CpUser> queryWrapper = Wrappers.lambdaQuery(CpUser.class);
        queryWrapper.eq(CpUser::getName, name);
        CpUser user = super.getOne(queryWrapper);

        if (user == null) {
            return null;
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(user), new TypeReference<>() {
        });
    }

    @Override
    public CpUser getCacheUserByUserid(String userid) {
        String userJson;
        if (Boolean.TRUE.equals(redisService.hasKey(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP))) {
            userJson = redisService.getCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, userid);
            //如果缓存中没有，则从数据库查询并放入缓存中
            if (StringUtils.isBlank(userJson)) {
                CpUser cpuser = this.getOne(Wrappers.lambdaQuery(CpUser.class).eq(CpUser::getUserid, userid));
                if (Objects.nonNull(cpuser)) {
                    redisService.setCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, userid, JacksonUtils.toJson(cpuser));
                }
                return cpuser;
            }
        } else {
            List<CpUser> list = super.list();
            Map<String, String> userMap = CollUtil.toMap(list, MapUtil.newHashMap(), CpUser::getUserid, JacksonUtils::toJson);
            redisService.setCacheMap(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, userMap);
            redisService.expire(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, (long) 60 * 60);
            userJson = userMap.get(userid);
        }

        if (StringUtils.isBlank(userJson)) {
            return null;
        } else {
            return JacksonUtils.toObj(userJson, CpUser.class);
        }
    }

    @Override
    public List<CpUser> listCacheUserByUserids(Collection<String> userids) {
        if (CollUtil.isEmpty(userids)) {
            return Collections.emptyList();
        }
        List<CpUser> cpUserList = new ArrayList<>();
        for (String userid : userids) {
            cpUserList.add(getCacheUserByUserid(userid));
        }
        return cpUserList;
    }

    @Override
    public UserInfoVo getUserByUserid(String userid) {
        //查询用户
        CpUser user = getCacheUserByUserid(userid);
        if (user == null) {
            return null;
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(user), new TypeReference<>() {
        });
    }

    @Override
    public void authSucc(String userid) {
        userFeign.userAuthsucc(agentProperties.getCorpId(), userid);
        //二次验证通过后，过去用户信息，并将用户信息更新到本地
        Result<UserDto> userDtoResult = userFeign.userGet(agentProperties.getCorpId(), agentProperties.getCommonAgentId(), userid);
        if (userDtoResult.hasError()) {
            log.error("请求企微异常，异常信息：{}", userDtoResult.getMsg());
            return;
        }
        CpUser user = JacksonUtils.toObj(JacksonUtils.toJson(userDtoResult.getData()), CpUser.class);
        user.setStatus(UserStatusEnum.ACTIVATED.getCode());
        super.saveOrUpdate(user);
        List<Long> departmentList = user.getDepartment();
        List<Integer> isLeaderInDeptList = user.getIsLeaderInDept();
        List<CpUserDepartmentRelation> userDepartmentRelationList = new ArrayList<>();
        for (int i = 0; i < departmentList.size(); i++) {
            CpUserDepartmentRelation userDepartmentRelation = new CpUserDepartmentRelation();
            userDepartmentRelation.setUserid(userid);
            userDepartmentRelation.setDepartmentid(departmentList.get(i));
            userDepartmentRelation.setIsLeader(isLeaderInDeptList.get(i));
            userDepartmentRelationList.add(userDepartmentRelation);
        }
        userDepartmentRelationService.saveOrUpdateBatch(userDepartmentRelationList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelCpUser(List<String> useridList) {
        //分成5个线程并发处理
        List<List<String>> split = ListUtil.splitAvg(useridList, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        for (List<String> subList : split) {
            if (CollUtil.isEmpty(subList)) {
                continue;
            }

            //删除企微用户
            commonExecutor.execute(() -> {
                //批量删除最多支持200个用户，分批处理
                List<List<String>> subSplit = ListUtil.split(subList, 200);
                for (List<String> list : subSplit) {
                    Result<Object> result = userFeign.userBatchdelete(agentProperties.getCorpId(), list.toArray(new String[0]));
                    if (result.success()) {
                        log.info("批量删除企微用户【{}】成功", list);
                        //删除用户标准表
                        delUserStandard(list);
                    } else {
                        log.error("批量删除企微用户【{}】失败：{}", list, result.getMsg());
                    }
                }
            });
        }
    }

    @Override
    public Set<String> selectAllUserid(UserInfoPageQo pageQo) {
        //是否查询子部门部门
        if (pageQo.getDepartmentId() != null && (YnEnum.YES.value() == pageQo.getFetchChild())) {
            CpDepartment department = departmentService.getById(pageQo.getDepartmentId());
            //设置idPath查询
            pageQo.setDepartmentIdPath(department.getIdPath());
        }
        //size 传 -1 默认查所有
        List<CpUser> userList = userMapper.pageUserInfoVo(Page.of(1, -1), pageQo);
        Set<String> userSet = userList.stream().map(CpUser::getUserid).collect(Collectors.toSet());
        if (CollUtil.isEmpty(userSet)) {
            return Collections.emptySet();
        }
        userSet.removeAll(pageQo.getExcludeUserids());
        return userSet;
    }

    @Override
    public void batchUserUpdateDeptV2(BatchUserDeptQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        Long mainDepartmentid = qo.getMainDepartmentid();
        if (mainDepartmentid == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("主部门不能为空").toServiceException();
        }

        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        List<CpUser> userList = new CopyOnWriteArrayList<>();
        List<CpUserDepartmentRelation> userDepartmentRelationList = new CopyOnWriteArrayList<>();
        List<Result<Object>> errorResultList = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (String userid : qo.getUseridList()) {
            futureList.add(CompletableFuture.runAsync(() -> {
                List<Long> departmentidList = qo.getDepartmentidList();

                //设置是否为部门负责人，默认为否
                List<Integer> isLeaderInDept = new ArrayList<>();
                for (int i = 0; i < departmentidList.size(); i++) {
                    isLeaderInDept.add(YnEnum.NO.getValue());
                }

                UserSaveQo userSaveQo = UserSaveQo.builder().userid(userid)
                        .department(departmentidList)
                        .mainDepartment(mainDepartmentid)
                        .isLeaderInDept(isLeaderInDept)
                        .build();
                //更新企微用户部门
                Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
                if (result.hasError()) {
                    log.error("更新用户部门失败：{}", result.getMsg());
                    errorResultList.add(result);
                    return;
                }
                //用户标准表
                CpUser entity = CpUser.builder().userid(userid).department(departmentidList).mainDepartment(mainDepartmentid).build();
                userList.add(entity);

                //用户部门关联表
                List<CpUserDepartmentRelation> relationList = departmentidList.stream().map(item ->
                        CpUserDepartmentRelation.builder().userid(userid).departmentid(item).build()).collect(Collectors.toList());
                userDepartmentRelationList.addAll(relationList);
            }, commonExecutor));
        }

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        //更新用户和用户部门关联表
        proxy.updateUserAndUserDeptRelation(qo, userList, userDepartmentRelationList);

        if (CollUtil.isNotEmpty(errorResultList)) {
            throw new ServiceException(String.format("更新部门失败：%s", errorResultList.get(0).getMsg()), errorResultList.get(0).getCode());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchEnableOrForbidUserV2(BatchEnableOrdForbidUserQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        List<Result<Object>> errorResultList = new CopyOnWriteArrayList<>();
        List<CpUser> successList = new CopyOnWriteArrayList<>();
        for (String userid : qo.getUseridList()) {
            futureList.add(CompletableFuture.runAsync(() -> {
                //禁用/启用企微用户
                UserSaveQo userSaveQo = UserSaveQo.builder().userid(userid).enable(qo.getEnable()).build();
                Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
                if (result.hasError()) {
                    log.error("禁用/启用用户失败：{}", result.getMsg());
                    errorResultList.add(result);
                    return;
                }
                UserStatusEnum userStatusEnum;
                //启用 用户状态为已激活
                if (YnEnum.YES.getValue() == (qo.getEnable())) {
                    userStatusEnum = UserStatusEnum.ACTIVATED;
                } else {
                    //禁用 用户状态为已禁用
                    userStatusEnum = UserStatusEnum.FORBIDDEN;
                }
                successList.add(CpUser.builder().userid(userid).status(userStatusEnum.getCode()).build());
            }, commonExecutor));
        }

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        //批量启用或禁用用户
        proxy.batchEnableOrForbidUser(successList);

        if (CollUtil.isNotEmpty(errorResultList)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("禁用/启用用户失败").toServiceException();
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUserSaveDeptV2(BatchUserDeptQo qo) {
        //获取企业id
        String corpId = agentProperties.getCorpId();

        //查询用户部门关联
        List<CpUserDepartmentRelation> userDeptList = userDepartmentRelationService
                .list(new LambdaQueryWrapper<CpUserDepartmentRelation>().in(CpUserDepartmentRelation::getUserid, qo.getUseridList()));
        //用户部门id列表
        Map<String, List<Long>> userDeptIdMap = userDeptList.stream().collect(Collectors.groupingBy(CpUserDepartmentRelation::getUserid, Collectors.mapping(CpUserDepartmentRelation::getDepartmentid, Collectors.toList())));
        //用户部门列表
        Map<String, List<CpUserDepartmentRelation>> userDeptMap = userDeptList.stream().collect(Collectors.groupingBy(CpUserDepartmentRelation::getUserid, Collectors.toList()));

        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();

        List<CpUser> userList = new CopyOnWriteArrayList<>();
        List<CpUserDepartmentRelation> userDepartmentRelationList = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        List<Result<Object>> errorResultList = new CopyOnWriteArrayList<>();
        for (String userid : qo.getUseridList()) {
            futureList.add(CompletableFuture.runAsync(() -> doUserSaveDeptV2(qo, userid, userDeptIdMap, userDeptMap, corpId, proxy, userList,
                    userDepartmentRelationList, errorResultList, userDeptList), commonExecutor));
        }

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        //更新用户和用户部门关联表
        proxy.updateUserAndUserDeptRelation(userList, userDepartmentRelationList);

        if (CollUtil.isNotEmpty(errorResultList)) {
            throw new ServiceException(String.format("新增用户部门失败：%s", errorResultList.get(0).getMsg()), errorResultList.get(0).getCode());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelUserV2(List<String> useridList) {
        //获取当前类的代理类
        UserServiceImpl proxy = (UserServiceImpl) AopContext.currentProxy();
        Map<String, UserRemoveLogVo> users = getUserQueue(useridList);
        //删除成功列表
        List<String> delSuccessList = new ArrayList<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        List<Result<Object>> errorResultList = new CopyOnWriteArrayList<>();
        for (String userid : useridList) {
            futureList.add(CompletableFuture.runAsync(() -> {
                //删除企微用户
                Result<Object> result = userFeign.userDelete(agentProperties.getCorpId(), userid);
                if (result.hasError() && !result.getMsg().contains("UserID不存在")) {
                    //删除用户标准表 处理已经成功的用户
                    errorResultList.add(result);
                    return;
                }
                //记录日志
                UserRemoveLogVo vo = users.get(userid);
                if (vo != null) {
                    userInfoOperationLogService.save(new UserInfoOperationLog(userid, vo.getName(), "3", "删除账号", vo.getSfzjh(), "删除成功", new Date()));
                    ;
                }
                delSuccessList.add(userid);
            }, commonExecutor));
        }

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        //删除用户标准表
        proxy.delUserStandard(delSuccessList);

        if (CollUtil.isNotEmpty(errorResultList)) {
            throw new ServiceException(String.format("新增用户部门失败：%s", errorResultList.get(0).getMsg()), errorResultList.get(0).getCode());
        }
    }

    private Map<String, UserRemoveLogVo> getUserQueue(List<String> useridList) {
        Map<String, UserRemoveLogVo> result = new HashMap<>();
        List<UserRemoveLogVo> users = userRemoveQueueMapper.getDeleteUserByIds(useridList);
        for (UserRemoveLogVo user : users) {
            result.put(user.getUserId(), user);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDelUserSync(List<String> useridList) {
        if (CollectionUtils.isEmpty(useridList)) {
            return;
        }
        String corpId = sysConfigService.selectConfigByKey(BatchUserConstant.CORP_ID);
        String agentId = sysConfigService.selectConfigByKey(BatchUserConstant.AGENT_ID);
        if (StringUtils.isBlank(corpId)) {
            throw ExceptionCodeEnum.CORP_ID_EXIST.toServiceException();
        }
        if (StringUtils.isBlank(agentId)) {
            throw ExceptionCodeEnum.AGENTT_ID_EXIST.toServiceException();
        }

        // 添加配置， 每次批量删除的间隔时间(分钟)，默认间隔时间60分钟
        String interval = sysConfigService.selectConfigByKey(BatchUserConstant.BATCH_DEL_CP_USER_INTERVAL);
        if (StringUtils.isBlank(interval)) {
            interval = "60";
        }
        Integer batchSize = BatchUserConstant.BATCH_DEL_CP_USER_SIZE;
        String sSize = sysConfigService.selectConfigByKey(BatchUserConstant.BATCH_DEL_USER_SIZE);
        if (StringUtils.isNotBlank(sSize) && StringUtils.isNumeric(sSize)) {
            batchSize = Integer.valueOf(sSize);
        }
        //1、每200进行拆分,
        List<List<String>> splitUser = ListUtil.split(useridList, batchSize);
        // 对数据进行校验，是否已经存在批量操作中
        checkUserIsBatch(splitUser);
        //2、更新数据状态，并插入异步执行数据表，传入jobid
        BatchJob batchJob = insertJob(BatchTypeEnum.QW_BATCH_REMOVE_USER);
        //查询删除排队最大的预计执行时间进行叠加，没有返回当前时间
        Date nowDate = getExecPlanTime(interval);
        for (List<String> batchIds : splitUser) {
            updateUserBatchState(batchIds, BatchUserConstant.BATCH_STATE);
            insertJobExec(getJobExecParam(corpId, agentId, batchIds), batchJob.getId(), nowDate, BatchTypeEnum.QW_BATCH_REMOVE_USER);
            //计算下一次需要执行的时间
            nowDate = execPlanTime(nowDate, interval);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUser(JobExecVo item) {
        JSONObject execParam = JSONObject.parseObject(item.getExecParam());
        if (null != execParam) {
            String userId = execParam.getString(BatchUserConstant.BATCH_USER_ID);
            Integer enable = execParam.getInteger(BatchUserConstant.BATCH_ENABLE);
            if (StringUtils.isNotBlank(userId) && null != enable) {
                CpUser cpUser = new CpUser();
                cpUser.setUserid(userId);
                if (YnEnum.YES.getValue() == (enable)) {
                    //启用 用户状态为已激活
                    cpUser.setStatus(UserStatusEnum.ACTIVATED.getCode());
                } else {
                    //禁用 用户状态为已禁用
                    cpUser.setStatus(UserStatusEnum.FORBIDDEN.getCode());
                }
                this.updateById(cpUser);
            }
        }
        //更新job表 业务数据状态
        batchJobExecService.updateBussState(item.getId(), BatchUserConstant.BATCH_ONE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(JobExecVo item) {
        //对执行完成的任务进行数据同步
        List<String> list = JSONObject.parseObject(item.getExecParam()).getList(BatchUserConstant.USER_ID_LIST, String.class);
        //删除原始数据
        this.delUserStandard(list);
        //更新job表 业务数据状态
        batchJobExecService.updateBussState(item.getId(), BatchUserConstant.BATCH_ONE);
    }

    private Date getExecPlanTime(String interval) {
        Date nowDate = new Date();
        JobExecCompDto jobExecCompDto = new JobExecCompDto();
        jobExecCompDto.setExecBeanName(BatchTypeEnum.QW_BATCH_REMOVE_USER.getBatchExecutorName());
        jobExecCompDto.setExecStatus(BatchUserConstant.BATCH_ZERO);
        Date maxExecPlanTime = batchJobExecService.getMaxExecPlanTime(jobExecCompDto);
        if (null != maxExecPlanTime) {
            nowDate = execPlanTime(maxExecPlanTime, interval);
        }
        return nowDate;
    }


    //插入job表
    private BatchJob insertJob(BatchTypeEnum qwBatch) {
        BatchJob batchJob = new BatchJob();
        batchJob.setBatchType(qwBatch.getBatchType());
        batchJob.setBatchName(qwBatch.getBatchName());
        batchJob.setBatchStatus(BatchUserConstant.BATCH_ZERO);
        batchJob.setCreateTime(new Date());
        batchJobService.save(batchJob);
        return batchJob;
    }

    //批量删除用户组装入参
    private String getJobExecParam(String corpId, String agentId, List<String> batchUserIds) {
        JSONObject jo = new JSONObject();
        jo.put(BatchUserConstant.CORP_ID, corpId);
        jo.put(BatchUserConstant.AGENT_ID, agentId);
        jo.put(BatchUserConstant.USER_ID_LIST, batchUserIds);
        return jo.toJSONString();
    }

    //插入job_exec执行表
    private void insertJobExec(String execParam, Long jobId, Date nowDate, BatchTypeEnum batchTypeEnum) {
        //corpId  agentId   userIdList
        BatchJobExec jobExec = new BatchJobExec();
        jobExec.setBatchId(jobId);
        jobExec.setExecBeanName(batchTypeEnum.getBatchExecutorName());
        jobExec.setExecParam(execParam);
        jobExec.setExecPlanTime(nowDate);
        jobExec.setExecStatus(BatchUserConstant.BATCH_ZERO);
        jobExec.setBusinessStatus(BatchUserConstant.BATCH_ZERO);
        batchJobExecService.save(jobExec);
    }

    //更新用户的状态
    private void updateUserBatchState(List<String> userIds, Integer batchState) {
        LambdaUpdateWrapper<CpUser> luw = new LambdaUpdateWrapper<>();
        luw.set(CpUser::getStatus, batchState)
                .in(CpUser::getUserid, userIds);
        this.update(luw);
    }

    //校验用户是否已经在批量处理中
    private void checkUserIsBatch(List<List<String>> splitUser) {
        LambdaQueryWrapper<CpUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CpUser::getStatus, BatchUserConstant.BATCH_STATE);
        for (List<String> userIds : splitUser) {
            lqw.in(CpUser::getUserid, userIds);
            List<CpUser> list = this.list(lqw);
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> throwUserIds = list.stream().map(CpUser::getUserid).collect(Collectors.toList());
                throw new ServiceException(String.format("批量删除用户失败，用户【%s】已经在处理中！", throwUserIds.toString()), ExceptionCodeEnum.BATCH_USER_EXEC.getCode());
            }
        }
    }

    //计算下一次执行的时间
    private Date execPlanTime(Date nowDate, String interval) {
        // 创建一个Calendar对象
        Calendar calendar = Calendar.getInstance();
        // 获取当前时间
        calendar.setTime(nowDate);
        // 按分钟添加时间
        calendar.add(Calendar.MINUTE, Integer.valueOf(interval));
        return calendar.getTime();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelDeptUser(Long deptId) {
        List<String> userids = userDepartmentRelationService.listUserIdsByDepartmentIdList(Collections.singletonList(deptId));
        if (CollUtil.isEmpty(userids)) {
            return;
        }
        batchDelCpUser(userids);
    }

    private void doUserSaveDeptV2(BatchUserDeptQo qo, String userid, Map<String, List<Long>> userDeptIdMap, Map<String, List<CpUserDepartmentRelation>> userDeptMap, String corpId, UserServiceImpl proxy, List<CpUser> userList, List<CpUserDepartmentRelation> userDepartmentRelationList, List<Result<Object>> errorResultList, List<CpUserDepartmentRelation> userDeptList) {
        List<Long> newDeptIdList = qo.getDepartmentidList();
        //旧用户部门
        List<Long> oldDeptidList = userDeptIdMap.get(userid);
        //需要新增的部门
        List<Long> newDeptList = newDeptIdList.stream().filter(item -> !oldDeptidList.contains(item)).collect(Collectors.toList());
        //如果为空则不处理
        if (CollUtil.isEmpty(newDeptList)) {
            return;
        }

        //旧部门添加新部门
        List<Long> deptIdList = CollUtil.unionAll(oldDeptidList, newDeptList);
        UserSaveQo userSaveQo = UserSaveQo.builder().userid(userid).department(deptIdList).build();
        CpUser entity = CpUser.builder().userid(userid).department(deptIdList).build();

        //设置主部门
        if (qo.getMainDepartmentid() != null) {
            userSaveQo.setMainDepartment(qo.getMainDepartmentid());
            entity.setMainDepartment(qo.getMainDepartmentid());
        }

        List<CpUserDepartmentRelation> relationList = new ArrayList<>();

        List<CpUserDepartmentRelation> departmentRelationList = userDeptMap.get(userid);
        Map<Long, Integer> isLeaderInDeptMap = CollUtil.toMap(departmentRelationList, MapUtil.newHashMap(), CpUserDepartmentRelation::getDepartmentid, CpUserDepartmentRelation::getIsLeader);
        //设置部门负责人
        List<Integer> isLeaderInDept = new ArrayList<>();
        for (Long deptId : deptIdList) {
            Integer isLeader = isLeaderInDeptMap.get(deptId);
            //设置是否为部门负责人，新增默认为否
            if (isLeader == null) {
                isLeader = YnEnum.NO.getValue();
            }
            isLeaderInDept.add(isLeader);

            //新增用户部门关联
            CpUserDepartmentRelation relation = CpUserDepartmentRelation.builder().userid(userid).departmentid(deptId).isLeader(isLeader).build();
            relationList.add(relation);
        }

        userSaveQo.setIsLeaderInDept(isLeaderInDept);
        entity.setIsLeaderInDept(isLeaderInDept);

        //更新企微用户部门
        Result<Object> result = userFeign.userUpdate(corpId, userSaveQo);
        if (result.hasError()) {
            //更新成功的用户和用户部门关联表
            proxy.updateUserAndUserDeptRelation(userList, userDepartmentRelationList);
            log.error("更新用户部门失败：{}", result.getMsg());
            errorResultList.add(result);
            return;
        }

        //用户标准表
        userList.add(entity);

        //过滤存量数据
        relationList.removeIf(item -> userDeptList.stream().anyMatch(item2 -> item2.getUserid().equals(item.getUserid()) && item2.getDepartmentid().equals(item.getDepartmentid())));
        //用户部门关联表
        if (CollUtil.isNotEmpty(relationList)) {
            userDepartmentRelationList.addAll(relationList);
        }
    }

    /**
     * 删除用户标准表
     */
    @Transactional(rollbackFor = Exception.class)
    public void delUserStandard(List<String> delSuccessList) {
        if (CollUtil.isNotEmpty(delSuccessList)) {
            //删除用户标准表
            super.removeByIds(delSuccessList);

            //删除用户部门关系表
            userDepartmentRelationService.remove(Wrappers.<CpUserDepartmentRelation>lambdaQuery()
                    .in(CpUserDepartmentRelation::getUserid, delSuccessList));

            //删除用户标签关系表
            userTagRelationService.remove(Wrappers.<CpUserTagRelation>lambdaQuery()
                    .in(CpUserTagRelation::getUserid, delSuccessList));

            //删除用户手机号绑定
            cpUserPhoneService.update(Wrappers.<CpUserPhone>lambdaUpdate()
                    .set(CpUserPhone::getIsDeleted, Boolean.TRUE)
                    .set(CpUserPhone::getUpdateTime, new Date())
                    .in(CpUserPhone::getUserId, delSuccessList));

            //删除用户缓存
            for (String userid : delSuccessList) {
                redisService.deleteCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, userid);
            }
        }
    }


    /**
     * 更新用户标准表
     */
    private void updateUserStandard(List<UserSaveQo> updateSuceessList, List<CpUser> oldUserList) {
        if (CollUtil.isNotEmpty(updateSuceessList)) {
            Map<String, CpUser> oldUserMap = CollUtil.toMap(oldUserList, null, CpUser::getUserid, o -> o);
            List<CpUser> userList = JacksonUtils.toObj(JacksonUtils.toJson(updateSuceessList), new TypeReference<>() {
            });
            for (CpUser user : userList) {
                CpUser oldUser = oldUserMap.get(user.getUserid());
                //旧用户部门信息
                List<Long> oldDept = oldUser.getDepartment();
                List<Integer> oldIsLeaderInDept = oldUser.getIsLeaderInDept();
                List<Integer> isLeaderInDept = new ArrayList<>();
                //设置部门负责人
                for (Long deptId : user.getDepartment()) {
                    //如果存在则照旧
                    if (oldDept.contains(deptId)) {
                        isLeaderInDept.add(oldIsLeaderInDept.get(oldDept.indexOf(deptId)));
                    } else {
                        //如果不存在则默认非部门负责人
                        isLeaderInDept.add(YnEnum.NO.getValue());
                    }
                }
                user.setIsLeaderInDept(isLeaderInDept);
            }
            super.updateBatchById(userList);
        }
    }

    /**
     * 新增用户标准表
     */
    private void saveUserStandard(List<UserSaveQo> saveSuccessList) {
        if (CollUtil.isNotEmpty(saveSuccessList)) {
            List<CpUser> userList = JacksonUtils.toObj(JacksonUtils.toJson(saveSuccessList), new TypeReference<>() {
            });
            for (CpUser user : userList) {
                //默认未激活
                user.setStatus(UserStatusEnum.INACTIVE.getCode());
                List<Integer> isLeaderInDept = new ArrayList<>();
                //默认非部门负责人
                for (int i = 0; i < user.getDepartment().size(); i++) {
                    isLeaderInDept.add(YnEnum.NO.getValue());
                }
                user.setIsLeaderInDept(isLeaderInDept);
            }

            super.saveBatch(userList);
        }
    }


    /**
     * 更新企微用户
     */
    private void updateCpUser(List<UserSaveQo> userUpdateQoList, Map<String, UserImportErrDto> errMap) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        //拆分成16组
        List<List<UserSaveQo>> split = ListUtil.splitAvg(userUpdateQoList, AddressbookConstant.PROCESS_THREAD_COUNT_16);
        for (List<UserSaveQo> list : split) {
            if (CollUtil.isEmpty(list)) {
                continue;
            }

            //异步调用企微
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                for (UserSaveQo userSaveQo : list) {
                    UserImportErrDto errDto = errMap.get(userSaveQo.getUserid());
                    try {
                        Result<Object> result = userFeign.userUpdate(agentProperties.getCorpId(), userSaveQo);
                        if (result.success()) {
                            userSaveQo.setIsOperateSuccess(YnEnum.YES.getValue());
                        } else {
                            userSaveQo.setIsOperateSuccess(YnEnum.NO.getValue());
                            //设置错误信息
                            errDto.getErrMsgList().add(result.getMsg());
                            log.error("更新企微用户失败：{}", result.getMsg());
                        }
                    } catch (Exception e) {
                        userSaveQo.setIsOperateSuccess(YnEnum.NO.getValue());
                        errDto.getErrMsgList().add("系统异常，请重试");
                        log.error("更新企微用户_feign调用异常：{}", e.getMessage(), e);
                    }
                }
            }, commonExecutor).whenComplete((unused, throwable) ->
                    log.info("线程[{}] ==> 异步方式更新用户至企微执行完成", Thread.currentThread().getName()));
            futureList.add(voidCompletableFuture);
        }

        //等待全部任务执行完
        CompletableFuture.allOf(futureList.toArray(CompletableFuture[]::new)).join();
    }

    /**
     * 新增企微用户
     */
    private void addCpUser(List<UserSaveQo> userSaveQoList, Map<String, UserImportErrDto> errMap) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        //拆分多线程执行
        List<List<UserSaveQo>> split = ListUtil.splitAvg(userSaveQoList, AddressbookConstant.PROCESS_THREAD_COUNT_16);
        for (List<UserSaveQo> list : split) {
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            //异步调用企微
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (UserSaveQo userSaveQo : list) {
                    UserImportErrDto errDto = errMap.get(userSaveQo.getUserid());
                    try {
                        Result<Object> result = userFeign.userCreate(agentProperties.getCorpId(), false, userSaveQo);
                        if (result.success()) {
                            userSaveQo.setIsOperateSuccess(YnEnum.YES.getValue());
                        } else {
                            userSaveQo.setIsOperateSuccess(YnEnum.NO.getValue());
                            //设置错误信息
                            errDto.getErrMsgList().add(result.getMsg());
                            log.error("新增企微用户失败：{}", result.getMsg());
                        }
                    } catch (Exception e) {
                        userSaveQo.setIsOperateSuccess(YnEnum.NO.getValue());
                        errDto.getErrMsgList().add("系统异常，请重试");
                        log.error("新增企微用户_feign调用异常：{}", e.getMessage(), e);
                    }
                }
            }, commonExecutor).whenComplete((unused, throwable) ->
                    log.info("线程[{}] ==> 异步方式新增用户至企微执行完成", Thread.currentThread().getName()));
            futureList.add(future);
        }

        //等待全部任务执行完
        CompletableFuture.allOf(futureList.toArray(CompletableFuture[]::new)).join();
    }

    /**
     * 提交用户信息并开通企微
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submitUserInfo(SubmitUserInfoQo qo) {
        //检验验证码
        cpUserPhoneService.checkPhoneCode(qo.getMobile(), qo.getVerifyCode());
        UserSaveWithTagQo data = new UserSaveWithTagQo();
        data.setMobile(qo.getMobile());
        data.setUserid(qo.getUserid());
        data.setName(qo.getName());
        data.setOperateType(OperateTypeEnum.SAVE.getCode()); // 默认新增
        SysConfigVo config = sysConfigService.getByConfigKey(UserBindHandlerConstant.SUBMIT_USER_INFO);
        Long maidId = Long.parseLong(config.getConfigValue());
        data.setMainDepartment(maidId);
        data.setDepartment(Collections.singletonList(maidId));
        data.setIsLeaderInDept(Collections.singletonList(0));
        return this.saveUserWithTag(data);
    }

    @Override
    public Collection<UserInfoPartVO> getActiveUsers() {
        return userMapper.getActiveUsers();
    }

    @Override
    public List<UserInfoPartVO> listUsersByDeptIds(List<Long> deptIds) {
        return userMapper.listUsersByDeptIds(deptIds);
    }

    @Override
    public List<UserInfoPartVO> getUserByIds(Collection<String> userIds) {
        return userMapper.getUserByIds(userIds);
    }

    @Override
    public void refreshCache() {
        redisService.deleteObject(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP);
    }

    @Override
    public Object updateUserTags(UpdateUserTagsDto dto) {
        List<TagInfoVo> allTags = tagService.listTagInfo();
        List<Long> allTagIds = allTags.stream().map(TagInfoVo::getTagid).collect(Collectors.toList());
        //查询用户关联的标签列表
        if (StringUtils.isEmpty(dto.getUserId())) {
            dto.setUserId(SecurityContextHolder.getUserId());
        }
        log.info("学工号是：{}", dto.getUserId());
//        List<CpUserTagRelation> userTagRelationList = userTagRelationService.listTagRelationByUserid(dto.getUserId());
//        List<Long> userTagIds = userTagRelationList.stream().map(CpUserTagRelation::getTagid).collect(Collectors.toList());
//        log.info("userTagIds列表是：{}",userTagIds);
        List<CpTag> delTagidList = cpTagMapper.selectTagsByIdentify(dto.getOldFidentify(), dto.getOldSidentify());
        List<Long> delTagid = delTagidList.stream().map(CpTag::getTagid).collect(Collectors.toList());
        List<CpTag> addTagidList = cpTagMapper.selectTagsByIdentify(dto.getNewFidentify(), dto.getNewSidentify());
        List<Long> addTagid = addTagidList.stream().map(CpTag::getTagid).collect(Collectors.toList());
        //全部idList
        //查询标签列表
        List<CpTag> tags = tagService.listByIds(allTagIds);
        Map<Long, CpTag> tagMap = tags.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));
        StringBuilder tagAddFailMsg = new StringBuilder();
        UserSaveWithTagQo qo = new UserSaveWithTagQo();
        qo.setUserid(dto.getUserId());
        //标签新增用户
        tagAddUser(qo, dto.getCorpId(), addTagid, tagMap, tagAddFailMsg);
        //标签删除用户
        tagDelUser(qo, dto.getCorpId(), delTagid, tagMap, tagAddFailMsg);
        return null;
    }
}
