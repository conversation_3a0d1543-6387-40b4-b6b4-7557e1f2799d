package com.lanshan.base.commonservice.addressbook.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.addressbook.dao.UserInfoStatusDao;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoStatus;
import com.lanshan.base.commonservice.addressbook.service.UserInfoStatusService;
import org.springframework.stereotype.Service;

/**
 * 用户信息状态(UserInfoStatus)表服务实现类
 *
 * <AUTHOR>
 */
@Service("userInfoStatusService")
public class UserInfoStatusServiceImpl extends ServiceImpl<UserInfoStatusDao, UserInfoStatus> implements UserInfoStatusService {

}

