package com.lanshan.base.commonservice.schooldata.hue.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;


/**
 * 图书欠费信息(TQywxtxlzlTsqfxx)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class TQywxtxlzlTsqfxx extends Model<TQywxtxlzlTsqfxx> {
    /**
     * 记录经手人编码
     */
    private String jljsrbmdmmc;
    /**
     * 处理状态码
     */
    private String clztmdmmc;
    /**
     * 证件号
     */
    private String zjh;
    /**
     * 财产号
     */
    private String cch;
    /**
     * 书名
     */
    private String sm;
    /**
     * 记录经手人编码
     */
    private String jljsrbm;
    /**
     * 记录日期
     */
    private String jilrq;
    /**
     * 应罚金额
     */
    private String yfje;
    /**
     * 实罚金额
     */
    private String sfje;
    /**
     * 处理日期
     */
    private String clrq;
    /**
     * 处理状态码
     */
    private String clztm;
    private String tstamp;

}

