package com.lanshan.base.commonservice.schooldata.lzjtu.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.lanshan.base.commonservice.schooldata.lzjtu.entity.Yjsjbxx;
import org.apache.ibatis.annotations.Update;

/**
 * 研究生信息(Yjsjbxx)表数据库访问层
 */
public interface YjsjbxxDao extends BaseMapper<Yjsjbxx> {

    //清空表
    @Update("TRUNCATE TABLE school_data.yjsjbxx")
    void truncate();
}

