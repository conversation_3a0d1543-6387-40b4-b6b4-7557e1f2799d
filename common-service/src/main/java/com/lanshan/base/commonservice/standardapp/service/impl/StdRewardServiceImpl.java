package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.converter.StdRewardConverter;
import com.lanshan.base.commonservice.standardapp.dao.StdRewardDao;
import com.lanshan.base.commonservice.standardapp.entity.StdReward;
import com.lanshan.base.commonservice.standardapp.qo.StdRewardQO;
import com.lanshan.base.commonservice.standardapp.service.StdRewardService;
import com.lanshan.base.commonservice.standardapp.vo.StdRewardVO;
import com.lanshan.base.commonservice.standardapp.vo.StdRewardYearMonthVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 酬金查询表(StdReward)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdRewardService")
public class StdRewardServiceImpl extends ServiceImpl<StdRewardDao, StdReward> implements StdRewardService {

    @Override
    public IPage<StdRewardVO> pageByParam(StdRewardQO rewardQO) {
        String userId = SecurityContextHolder.getUserId();
        return this.page(Page.of(rewardQO.getPage(), rewardQO.getSize()), Wrappers.lambdaQuery(StdReward.class)
                .eq(CharSequenceUtil.isNotBlank(userId), StdReward::getUserId, userId)
                .eq(CharSequenceUtil.isNotBlank(rewardQO.getYear()), StdReward::getYear, rewardQO.getYear())
                .eq(CharSequenceUtil.isNotBlank(rewardQO.getMonth()), StdReward::getMonth, rewardQO.getMonth())
                .orderByDesc(StdReward::getCreateTime)
        ).convert(StdRewardConverter.INSTANCE::toVO);
    }

    @Override
    public List<StdRewardYearMonthVO> getYearMonthList() {
        return this.baseMapper.getYearMonthList(SecurityContextHolder.getUserId());
    }
}

