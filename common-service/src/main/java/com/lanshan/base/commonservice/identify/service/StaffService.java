package com.lanshan.base.commonservice.identify.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.identify.dto.AddStaffDto;
import com.lanshan.base.commonservice.identify.entity.StaffEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * (Staff)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-10 14:06:11
 */
public interface StaffService extends IService<StaffEntity> {

    Map<String,Object> importUserData(MultipartFile file) throws Exception;

    void add(AddStaffDto addStaffDto);
}

