package com.lanshan.base.commonservice.excel.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.Row;

import java.util.List;

/**
 * EasyExcel自适应行高处理器
 * 
 * 功能：
 * 1. 根据内容自动调整行高
 * 2. 支持多行文本自动换行
 * 3. 支持最大最小行高限制
 * 4. 支持标题行特殊处理
 * 
 * 使用方式：
 * EasyExcel.write(outputStream, ExportDTO.class)
 *     .registerWriteHandler(new AutoRowHeightHandler())
 *     .sheet("数据")
 *     .doWrite(data);
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class AutoRowHeightHandler implements RowWriteHandler {

    /**
     * 默认行高（磅）
     */
    private static final float DEFAULT_ROW_HEIGHT = 15f;
    
    /**
     * 最大行高（磅）
     */
    private static final float MAX_ROW_HEIGHT = 100f;
    
    /**
     * 最小行高（磅）
     */
    private static final float MIN_ROW_HEIGHT = 12f;
    
    /**
     * 标题行高（磅）
     */
    private static final float HEADER_ROW_HEIGHT = 20f;
    
    /**
     * 每行字符数对应的行高增量
     */
    private static final float HEIGHT_PER_LINE = 15f;
    
    /**
     * 自定义最大行高
     */
    private final float maxRowHeight;
    
    /**
     * 自定义最小行高
     */
    private final float minRowHeight;
    
    /**
     * 自定义标题行高
     */
    private final float headerRowHeight;
    
    /**
     * 是否启用自动换行
     */
    private final boolean enableAutoWrap;

    /**
     * 默认构造函数
     */
    public AutoRowHeightHandler() {
        this(MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, HEADER_ROW_HEIGHT, true);
    }

    /**
     * 自定义构造函数
     * 
     * @param maxRowHeight 最大行高
     * @param minRowHeight 最小行高
     * @param headerRowHeight 标题行高
     * @param enableAutoWrap 是否启用自动换行
     */
    public AutoRowHeightHandler(float maxRowHeight, float minRowHeight, float headerRowHeight, boolean enableAutoWrap) {
        this.maxRowHeight = maxRowHeight;
        this.minRowHeight = minRowHeight;
        this.headerRowHeight = headerRowHeight;
        this.enableAutoWrap = enableAutoWrap;
    }

    @Override
    public void beforeRowCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                               Integer rowIndex, Integer relativeRowIndex, Boolean isHead) {
        // 行创建前不需要特殊处理
    }

    @Override
    public void afterRowCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                              Row row, Integer relativeRowIndex, Boolean isHead) {
        // 设置基础行高
        if (isHead) {
            // 标题行使用固定高度
            row.setHeightInPoints(headerRowHeight);
        } else {
            // 数据行使用默认高度，后续根据内容调整
            row.setHeightInPoints(DEFAULT_ROW_HEIGHT);
        }
    }

    @Override
    public void afterRowDispose(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                               Row row, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            // 标题行不需要动态调整
            return;
        }

        // 计算当前行所需的最大高度
        float maxHeight = calculateRowHeight(row);
        
        // 确保高度在合理范围内
        float finalHeight = Math.max(minRowHeight, Math.min(maxHeight, maxRowHeight));
        
        // 设置行高
        row.setHeightInPoints(finalHeight);
    }

    /**
     * 计算行高
     * 
     * @param row 行对象
     * @return 计算出的行高
     */
    private float calculateRowHeight(Row row) {
        float maxHeight = DEFAULT_ROW_HEIGHT;
        
        // 遍历行中的所有单元格
        for (int i = 0; i < row.getLastCellNum(); i++) {
            if (row.getCell(i) != null) {
                String cellValue = getCellValueAsString(row.getCell(i));
                if (cellValue != null && !cellValue.isEmpty()) {
                    float cellHeight = calculateCellHeight(cellValue);
                    maxHeight = Math.max(maxHeight, cellHeight);
                }
            }
        }
        
        return maxHeight;
    }

    /**
     * 获取单元格值的字符串表示
     * 
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 计算单元格内容所需的高度
     * 
     * @param content 单元格内容
     * @return 所需高度
     */
    private float calculateCellHeight(String content) {
        if (content == null || content.isEmpty()) {
            return DEFAULT_ROW_HEIGHT;
        }
        
        // 计算换行符数量
        int lineCount = 1;
        if (enableAutoWrap) {
            lineCount = content.split("\n").length;
            
            // 如果内容很长，估算可能的换行数
            if (lineCount == 1 && content.length() > 20) {
                // 假设每20个字符换一行（这个可以根据实际列宽调整）
                lineCount = (content.length() / 20) + 1;
            }
        }
        
        // 根据行数计算高度
        return Math.max(DEFAULT_ROW_HEIGHT, lineCount * HEIGHT_PER_LINE);
    }

    /**
     * 创建默认的自适应行高处理器
     * 
     * @return AutoRowHeightHandler实例
     */
    public static AutoRowHeightHandler create() {
        return new AutoRowHeightHandler();
    }

    /**
     * 创建自定义配置的自适应行高处理器
     * 
     * @param maxHeight 最大行高
     * @param minHeight 最小行高
     * @return AutoRowHeightHandler实例
     */
    public static AutoRowHeightHandler create(float maxHeight, float minHeight) {
        return new AutoRowHeightHandler(maxHeight, minHeight, HEADER_ROW_HEIGHT, true);
    }

    /**
     * 创建自定义标题行高的处理器
     * 
     * @param headerHeight 标题行高
     * @return AutoRowHeightHandler实例
     */
    public static AutoRowHeightHandler createWithHeaderHeight(float headerHeight) {
        return new AutoRowHeightHandler(MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, headerHeight, true);
    }

    /**
     * 创建禁用自动换行的处理器
     * 
     * @return AutoRowHeightHandler实例
     */
    public static AutoRowHeightHandler createWithoutAutoWrap() {
        return new AutoRowHeightHandler(MAX_ROW_HEIGHT, MIN_ROW_HEIGHT, HEADER_ROW_HEIGHT, false);
    }
}
