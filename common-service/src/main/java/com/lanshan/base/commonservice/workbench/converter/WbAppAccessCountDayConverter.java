package com.lanshan.base.commonservice.workbench.converter;


import java.util.List;

import com.lanshan.base.commonservice.workbench.entity.WbAppAccessCountDay;
import com.lanshan.base.commonservice.workbench.vo.WbAppAccessCountDayVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * (WbAppAccessCountDay)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppAccessCountDayConverter {

    WbAppAccessCountDayConverter INSTANCE = Mappers.getMapper(WbAppAccessCountDayConverter.class);

    WbAppAccessCountDayVO toVO(WbAppAccessCountDay entity);

    WbAppAccessCountDay toEntity(WbAppAccessCountDayVO vo);

    List<WbAppAccessCountDayVO> toVO(List<WbAppAccessCountDay> entityList);

    List<WbAppAccessCountDay> toEntity(List<WbAppAccessCountDayVO> voList);
}


