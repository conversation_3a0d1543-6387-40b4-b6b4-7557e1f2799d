package com.lanshan.base.commonservice.schooldata.whut.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 本科生学籍基本信息（本科生院）(WhutUndergraduateStatus)表实体类
 */
@Data
public class WhutUndergraduateStatus implements Serializable{

    @ApiModelProperty(value = "学号")
    private String xh;

    @ApiModelProperty(value = "姓名")
    private String xm;

    @ApiModelProperty(value = "校区编码")
    private String xqbm;

    @ApiModelProperty(value = "院系编码")
    private String yxbm;

    @ApiModelProperty(value = "专业编码")
    private String zybm;

    @ApiModelProperty(value = "班级编码")
    private String bjbm;

    @ApiModelProperty(value = "培养层次")
    private String pycc;

    @ApiModelProperty(value = "年级")
    private String nj;

    @ApiModelProperty(value = "学习年限")
    private String xxnx;

    @ApiModelProperty(value = "学制")
    private String xz;

    @ApiModelProperty(value = "学科门类")
    private String xkml;

    @ApiModelProperty(value = "入学年月")
    private String rxny;

    @ApiModelProperty(value = "入学总分")
    private String rxzf;

    @ApiModelProperty(value = "入学方式")
    private String rxfs;

    @ApiModelProperty(value = "学生类别")
    private String xslb;

    @ApiModelProperty(value = "学生类别代码名称")
    private String xslbdmmc;

    @ApiModelProperty(value = "来源地区")
    private String lydq;

    @ApiModelProperty(value = "毕业中学")
    private String byzx;

    @ApiModelProperty(value = "委托培养单位")
    private String wtpydw;

    @ApiModelProperty(value = "预计毕业日期")
    private String yjbyrq;

    @ApiModelProperty(value = "是否在校")
    private String sfzx;

    @ApiModelProperty(value = "是否在籍")
    private String sfzj;

    @ApiModelProperty(value = "时间戳")
    private String tstamp;

}

