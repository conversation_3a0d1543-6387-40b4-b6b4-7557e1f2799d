package com.lanshan.base.commonservice.monitor.task;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.config.properties.MonitorProperties;
import com.lanshan.base.commonservice.enums.ServiceStatusEnum;
import com.lanshan.base.commonservice.monitor.converter.AcServiceConverter;
import com.lanshan.base.commonservice.monitor.entity.AcService;
import com.lanshan.base.commonservice.monitor.model.vo.AcServiceVO;
import com.lanshan.base.commonservice.monitor.service.AcServiceService;
import com.lanshan.base.starter.redis.constant.RedisKeys;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 应用服务健康检查任务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ServiceHealthCheckTask {


    private final RedisService redisService;

    private final ThreadPoolTaskExecutor commonTaskExecutor;

    private final AcServiceService acServiceService;

    private final MonitorProperties monitorProperties;

    @XxlJob("healthCheck")
    public void healthCheck() {
        List<AcServiceVO> serviceList = acServiceService.listAll();
        // 查询所有应用服务的健康状态
        CountDownLatch countDownLatch = new CountDownLatch(serviceList.size());
        ArrayBlockingQueue<AcServiceVO> healthCheckQueue = new ArrayBlockingQueue<>(serviceList.size());
        for (AcServiceVO acServiceVO : serviceList) {
            //提交线程池，多线程执行
            commonTaskExecutor.submit(() -> doHealthCheck(acServiceVO, countDownLatch, healthCheckQueue));
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("等待线程执行异常：", e);
            Thread.currentThread().interrupt();
        }
        List<AcService> acServiceList = healthCheckQueue.stream().map(AcServiceConverter.INSTANCE::toEntity).collect(Collectors.toList());
        redisService.expire(RedisKeys.getServiceMaintainMap(), monitorProperties.getRedisExpireTime());
        //保存数据到数据库
        acServiceService.updateBatchById(acServiceList);
    }


    private void doHealthCheck(AcServiceVO acServiceVO, CountDownLatch countDownLatch, ArrayBlockingQueue<AcServiceVO> healthCheckQueue) {
        String healthCheckUrl = acServiceVO.getHealthCheckUrl();
        if (CharSequenceUtil.isBlank(healthCheckUrl)) {
            return;
        }
        try {
            log.info("开始健康检测，检测地址：{}", healthCheckUrl);
            HttpResponse response = HttpUtil.createGet(healthCheckUrl).timeout(monitorProperties.getTimeout() * 1000).execute();
            log.info("获取健康状态成功，返回状态码：{}", response.getStatus());
            if (response.isOk()) {
                acServiceVO.setStatus(ServiceStatusEnum.RUNNING.getCode());
            } else {
                acServiceVO.setStatus(ServiceStatusEnum.ERROR.getCode());
            }
            redisService.setCacheMapValue(RedisKeys.getServiceMaintainMap(), acServiceVO.getId().toString(), acServiceVO);
        } catch (Exception e) {
            log.error("获取健康状态发生异常：", e);
            acServiceVO.setStatus(ServiceStatusEnum.ERROR.getCode());
            redisService.setCacheMapValue(RedisKeys.getServiceMaintainMap(), acServiceVO.getId().toString(), acServiceVO);
        } finally {
            countDownLatch.countDown();
            try {
                healthCheckQueue.put(acServiceVO);
            } catch (InterruptedException e) {
                log.error("插入队列失败：", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}
