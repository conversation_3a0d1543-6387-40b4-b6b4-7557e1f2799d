package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * JSONB到List<Integer>的类型处理器
 * 
 * 专门用于处理数据库JSONB字段到Java List<Integer>类型的转换
 * 
 * 使用场景：
 * 1. 数据库中存储整数数组，如：[1,2,3]
 * 2. 需要将JSON整数数组转换为Java List<Integer>对象
 * 3. 适用于ID列表、数值序列等
 * 
 * 使用方式：
 * @TableField(typeHandler = JsonbIntegerListTypeHandler.class)
 * private List<Integer> userIds;
 * 
 * 或在XML中：
 * #{userIds,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbIntegerListTypeHandler}
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({List.class})
public class JsonbIntegerListTypeHandler extends UniversalJsonbTypeHandler<List<Integer>> {
    
    /**
     * 默认构造函数，处理List<Integer>类型
     */
    public JsonbIntegerListTypeHandler() {
        super(new TypeReference<List<Integer>>() {});
    }
}
