package com.lanshan.base.commonservice.task;

import com.lanshan.base.commonservice.schooldata.hue.service.StdBookBorrowRecordService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
public class BookBorrowRecordTask {

    @Resource
    private StdBookBorrowRecordService stdBookBorrowRecordService;

    @XxlJob("hueBookBorrowRecord")
    public void hueBookBorrowRecord() {
        log.info("开始执行任务");
        stdBookBorrowRecordService.syncBookBorrowRecord();
        log.info("任务执行完成");
    }

    @XxlJob("bookBorrowRecordNotice")
    public void notice() {
        log.info("开始执行通知任务");
        stdBookBorrowRecordService.notice();
        log.info("通知任务执行完成");
    }
}
