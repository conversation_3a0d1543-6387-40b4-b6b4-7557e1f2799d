package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel(value = "CourseClassVO", description = "行政班级")
public class CourseClassVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政班级ID")
    private String classNumber;

    @ApiModelProperty(value = "行政班级名称")
    private String className;
}
