package com.lanshan.base.commonservice.group.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "检查是否有创建权限VO")
public class CheckGroupCreateVO {

    @ApiModelProperty(value = "是否有创建权限")
    private Boolean canCreate;

    @ApiModelProperty(value = "是否有配置权限")
    private Boolean hasSetting;
}
