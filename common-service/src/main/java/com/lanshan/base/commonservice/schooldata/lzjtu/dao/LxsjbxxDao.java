package com.lanshan.base.commonservice.schooldata.lzjtu.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.lanshan.base.commonservice.schooldata.lzjtu.entity.Lxsjbxx;
import org.apache.ibatis.annotations.Update;

/**
 * 留学生信息(Lxsjbxx)表数据库访问层
 */
public interface LxsjbxxDao extends BaseMapper<Lxsjbxx> {

    //清空表
    @Update("TRUNCATE TABLE school_data.lxsjbxx")
    void truncate();
}

