package com.lanshan.base.commonservice.addressbook.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import cn.hutool.core.date.DateTime;

/**
 * 企业微信用户导入标签日志查询对象
 */
@Data
@ApiModel("企业微信用户导入标签日志查询对象")
public class CpUserImportTagLogQO extends PageQo {

    @ApiModelProperty("ID")
    private Long id;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String code;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String operator;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private DateTime operateTime;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间开始")
    private DateTime operateTimeStart;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间结束")
    private DateTime operateTimeEnd;
    /**
     * 失败
     */
    @ApiModelProperty("是否失败")
    private Boolean hasError;

    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;

}