package com.lanshan.base.commonservice.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.CommonUploadStatusEnum;
import com.lanshan.base.api.qo.common.CommonUploadLogQo;
import com.lanshan.base.api.vo.common.CommonUploadLogVo;
import com.lanshan.base.commonservice.common.entity.CommonUploadLog;
import com.lanshan.base.commonservice.common.mapper.CommonUploadLogMapper;
import com.lanshan.base.commonservice.common.service.CommonUploadLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 文件上传记录(CommonUploadLog)服务实现类
 */
@Slf4j
@Service
public class CommonUploadLogServiceImpl extends ServiceImpl<CommonUploadLogMapper, CommonUploadLog> implements CommonUploadLogService {

    @Override
    public List<CommonUploadLogVo> listUploadLog(CommonUploadLogQo qo) {
        LambdaQueryWrapper<CommonUploadLog> queryWrapper = Wrappers.lambdaQuery(CommonUploadLog.class);
        // 上传类型
        queryWrapper.eq(CommonUploadLog::getType, qo.getType());
        // 开始时间、结束时间
        queryWrapper.ge(CommonUploadLog::getCreateDate, qo.getStartDate());
        queryWrapper.lt(CommonUploadLog::getCreateDate, DateUtil.offsetDay(qo.getEndDate(), 1));
        //按id（创建时间）倒序排列
        queryWrapper.orderByDesc(CommonUploadLog::getId);
        List<CommonUploadLog> list = super.list(queryWrapper);

        if(CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<CommonUploadLogVo> voList = BeanUtil.copyToList(list, CommonUploadLogVo.class);
        //填充Vo对象
        for(CommonUploadLogVo vo : voList) {
            //设置状态
            vo.setStatusDesc(EnumUtil.getFieldBy(CommonUploadStatusEnum::getDesc, CommonUploadStatusEnum::getCode, vo.getStatus()));
        }

        return voList;
    }
}
