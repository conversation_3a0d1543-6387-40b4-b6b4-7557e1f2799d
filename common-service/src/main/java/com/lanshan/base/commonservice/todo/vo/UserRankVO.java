package com.lanshan.base.commonservice.todo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 用户排名VO
 */
@Data
@ApiModel(description = "用户排名")
public class UserRankVO {

    @ApiModelProperty(value = "用户ID")
    @JsonIgnore
    private String userId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "总待办数")
    private String totalTodo;
}
