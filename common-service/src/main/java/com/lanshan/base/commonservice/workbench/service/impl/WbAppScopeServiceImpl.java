package com.lanshan.base.commonservice.workbench.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.user.UserDeptIdsTagIdsDto;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.workbench.dao.WbAppScopeDao;
import com.lanshan.base.commonservice.workbench.entity.WbAppScope;
import com.lanshan.base.commonservice.workbench.enums.AppScopeTypeEnum;
import com.lanshan.base.commonservice.workbench.service.WbAppScopeService;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作台应用可见范围信息表(WbAppScope)表服务实现类
 *
 * <AUTHOR>
 */
@Service("wbAppScopeService")
public class WbAppScopeServiceImpl extends ServiceImpl<WbAppScopeDao, WbAppScope> implements WbAppScopeService {


    @Resource
    private UserService userService;

    /**
     * 同步应用可见范围
     *
     * @param appId 应用id
     * @param agent 应用
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncAppScope(Long appId, WxCpAgent agent) {

        List<WbAppScope> batchList = new ArrayList<>();

        if (agent.getAllowUserInfos() != null && agent.getAllowUserInfos().getUsers() != null) {
            List<WxCpAgent.User> users = agent.getAllowUserInfos().getUsers();
            for (WxCpAgent.User user : users) {
                batchList.add(WbAppScope.builder()
                        .appId(appId)
                        .scopeType(AppScopeTypeEnum.USER.getCode())
                        .scopeTargetId(user.getUserId())
                        .build());
            }
        }

        if (agent.getAllowParties() != null && CollectionUtils.isNotEmpty(agent.getAllowParties().getPartyIds())) {
            List<Long> parties = agent.getAllowParties().getPartyIds();
            for (Long party : parties) {
                batchList.add(WbAppScope.builder()
                        .appId(appId)
                        .scopeType(AppScopeTypeEnum.PARTY.getCode())
                        .scopeTargetId(party.toString())
                        .build());
            }
        }


        if (agent.getAllowTags() != null && CollectionUtils.isNotEmpty(agent.getAllowTags().getTagIds())) {
            List<Integer> tags = agent.getAllowTags().getTagIds();
            for (Integer tag : tags) {
                batchList.add(WbAppScope.builder()
                        .appId(appId)
                        .scopeType(AppScopeTypeEnum.TAG.getCode())
                        .scopeTargetId(tag.toString())
                        .build());
            }
        }

        super.remove(new LambdaQueryWrapper<WbAppScope>().eq(WbAppScope::getAppId, appId));
        super.saveOrUpdateBatch(batchList);
    }

    /**
     * 获取用户可见的应用id
     * 根据用户ID, 用户所在部门，用户标签的应用可见范围进行合并
     *
     * @param userId 用户id
     * @return 应用id集合
     */
    @Override
    public Set<Long> getUserAppScope(String userId) {
        return getUserAppScope(userId, Collections.emptyList());
    }

    /**
     * 获取用户可见的应用id
     * 根据用户ID, 用户所在部门，用户标签的应用可见范围进行合并
     *
     * @param userId 用户id
     * @param appIds 应用id集合
     * @return 应用id集合
     */
    @Override
    public Set<Long> getUserAppScope(String userId, List<Long> appIds) {
        UserDeptIdsTagIdsDto deptIdsAndTagIds =
                userService.getDeptIdsAndTagIdsByUserId(String.valueOf(userId));

        List<Long> partyIds = deptIdsAndTagIds.getDepartmentIds();
        List<Long> userTagIds = deptIdsAndTagIds.getTagIds();

        LambdaQueryWrapper<WbAppScope> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(WbAppScope::getAppId);

        if (CollectionUtils.isNotEmpty(appIds)) {
            queryWrapper.in(WbAppScope::getAppId, appIds);
        }
        queryWrapper.eq(WbAppScope::getScopeType, AppScopeTypeEnum.USER.getCode())
                .eq(WbAppScope::getScopeTargetId, userId);

        Set<Long> appIs = getUserScopeAppIds(List.of(userId), AppScopeTypeEnum.USER);

        if (CollectionUtils.isNotEmpty(partyIds)) {
            appIs.addAll(getUserScopeAppIds(partyIds.stream().map(String::valueOf).collect(Collectors.toList()), AppScopeTypeEnum.PARTY));
        }

        if (CollectionUtils.isNotEmpty(userTagIds)) {
            appIs.addAll(getUserScopeAppIds(userTagIds.stream().map(String::valueOf).collect(Collectors.toList()), AppScopeTypeEnum.TAG));
        }
        return appIs;
    }

    /**
     * 根据应用可见范围类型，查询应用id集合
     *
     * @param targetIds        目标id集合
     * @param appScopeTypeEnum 应用可见范围类型
     * @return 应用id集合
     */
    private Set<Long> getUserScopeAppIds(List<String> targetIds, AppScopeTypeEnum appScopeTypeEnum) {

        LambdaQueryWrapper<WbAppScope> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(WbAppScope::getAppId);
        queryWrapper.eq(WbAppScope::getScopeType, appScopeTypeEnum.getCode())
                .in(WbAppScope::getScopeTargetId, targetIds);
        return Optional.ofNullable(this.list(queryWrapper))
                .map(list -> list.stream().map(WbAppScope::getAppId).collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
    }
}

