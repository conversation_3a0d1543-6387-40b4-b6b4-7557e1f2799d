package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * JSONB到List<Long>的类型处理器
 * 
 * 专门用于处理数据库JSONB字段到Java List<Long>类型的转换
 * 
 * 使用场景：
 * 1. 数据库中存储长整数数组，如：[1,2,3]
 * 2. 需要将JSON长整数数组转换为Java List<Long>对象
 * 3. 适用于大数值ID列表、时间戳序列等
 * 
 * 使用方式：
 * @TableField(typeHandler = JsonbLongListTypeHandler.class)
 * private List<Long> joinInUser;
 * 
 * 或在XML中：
 * #{joinInUser,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbLongListTypeHandler}
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({List.class})
public class JsonbLongListTypeHandler extends UniversalJsonbTypeHandler<List<Long>> {
    
    /**
     * 默认构造函数，处理List<Long>类型
     */
    public JsonbLongListTypeHandler() {
        super(new TypeReference<List<Long>>() {});
    }
}
