package com.lanshan.base.commonservice.excel.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.lanshan.base.commonservice.excel.handler.AutoColumnWidthHandler;
import com.lanshan.base.commonservice.excel.handler.AutoRowHeightHandler;
import com.lanshan.base.commonservice.excel.handler.AutoSizeHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * EasyExcel自适应大小工具类
 * 
 * 提供便捷的方法来创建具有自适应列宽和行高功能的Excel文件
 * 
 * 使用示例：
 * 1. 基础用法：
 *    EasyExcelAutoSizeUtil.writeWithAutoSize(response, "文件名", "工作表名", ExportDTO.class, dataList);
 * 
 * 2. 自定义配置：
 *    EasyExcelAutoSizeUtil.builder()
 *        .maxColumnWidth(60)
 *        .minColumnWidth(10)
 *        .maxRowHeight(120f)
 *        .writeToResponse(response, "文件名", "工作表名", ExportDTO.class, dataList);
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class EasyExcelAutoSizeUtil {

    /**
     * 写入Excel到HttpServletResponse（使用默认自适应配置）
     * 
     * @param response HTTP响应对象
     * @param fileName 文件名（不包含扩展名）
     * @param sheetName 工作表名
     * @param clazz 数据类型
     * @param data 数据列表
     * @param <T> 数据类型泛型
     */
    public static <T> void writeWithAutoSize(HttpServletResponse response, String fileName, String sheetName, 
                                           Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);
            
            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoSizeHandler())
                    .sheet(sheetName)
                    .doWrite(data);
                    
        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 写入Excel到OutputStream（使用默认自适应配置）
     * 
     * @param outputStream 输出流
     * @param sheetName 工作表名
     * @param clazz 数据类型
     * @param data 数据列表
     * @param <T> 数据类型泛型
     */
    public static <T> void writeWithAutoSize(OutputStream outputStream, String sheetName, 
                                           Class<T> clazz, List<T> data) {
        EasyExcel.write(outputStream, clazz)
                .registerWriteHandler(new AutoSizeHandler())
                .sheet(sheetName)
                .doWrite(data);
    }

    /**
     * 只使用自适应列宽
     * 
     * @param response HTTP响应对象
     * @param fileName 文件名
     * @param sheetName 工作表名
     * @param clazz 数据类型
     * @param data 数据列表
     * @param <T> 数据类型泛型
     */
    public static <T> void writeWithAutoColumnWidth(HttpServletResponse response, String fileName, String sheetName,
                                                  Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);
            
            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoColumnWidthHandler())
                    .sheet(sheetName)
                    .doWrite(data);
                    
        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 只使用自适应行高
     * 
     * @param response HTTP响应对象
     * @param fileName 文件名
     * @param sheetName 工作表名
     * @param clazz 数据类型
     * @param data 数据列表
     * @param <T> 数据类型泛型
     */
    public static <T> void writeWithAutoRowHeight(HttpServletResponse response, String fileName, String sheetName,
                                                Class<T> clazz, List<T> data) {
        try {
            setupResponse(response, fileName);
            
            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(new AutoRowHeightHandler())
                    .sheet(sheetName)
                    .doWrite(data);
                    
        } catch (IOException e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }

    /**
     * 创建构建器
     * 
     * @return AutoSizeBuilder实例
     */
    public static AutoSizeBuilder builder() {
        return new AutoSizeBuilder();
    }

    /**
     * 设置HTTP响应头
     * 
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    private static void setupResponse(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            
        } catch (Exception e) {
            throw new RuntimeException("设置响应头失败", e);
        }
    }

    /**
     * 自适应大小构建器
     */
    public static class AutoSizeBuilder {
        private AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig();
        private AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig();

        /**
         * 设置最大列宽
         */
        public AutoSizeBuilder maxColumnWidth(int maxColumnWidth) {
            this.columnConfig.setMaxColumnWidth(maxColumnWidth);
            return this;
        }

        /**
         * 设置最小列宽
         */
        public AutoSizeBuilder minColumnWidth(int minColumnWidth) {
            this.columnConfig.setMinColumnWidth(minColumnWidth);
            return this;
        }

        /**
         * 设置是否启用中文字符优化
         */
        public AutoSizeBuilder enableChineseOptimization(boolean enable) {
            this.columnConfig.setEnableChineseOptimization(enable);
            return this;
        }

        /**
         * 设置最大行高
         */
        public AutoSizeBuilder maxRowHeight(float maxRowHeight) {
            this.rowConfig.setMaxRowHeight(maxRowHeight);
            return this;
        }

        /**
         * 设置最小行高
         */
        public AutoSizeBuilder minRowHeight(float minRowHeight) {
            this.rowConfig.setMinRowHeight(minRowHeight);
            return this;
        }

        /**
         * 设置默认行高
         */
        public AutoSizeBuilder defaultRowHeight(float defaultRowHeight) {
            this.rowConfig.setDefaultRowHeight(defaultRowHeight);
            return this;
        }

        /**
         * 设置标题行高
         */
        public AutoSizeBuilder headerRowHeight(float headerRowHeight) {
            this.rowConfig.setHeaderRowHeight(headerRowHeight);
            return this;
        }

        /**
         * 设置是否启用自动换行
         */
        public AutoSizeBuilder enableAutoWrap(boolean enable) {
            this.rowConfig.setEnableAutoWrap(enable);
            return this;
        }

        /**
         * 写入到HttpServletResponse
         */
        public <T> void writeToResponse(HttpServletResponse response, String fileName, String sheetName,
                                      Class<T> clazz, List<T> data) {
            try {
                setupResponse(response, fileName);
                
                EasyExcel.write(response.getOutputStream(), clazz)
                        .registerWriteHandler(new AutoSizeHandler(columnConfig, rowConfig))
                        .sheet(sheetName)
                        .doWrite(data);
                        
            } catch (IOException e) {
                throw new RuntimeException("导出Excel文件失败", e);
            }
        }

        /**
         * 写入到OutputStream
         */
        public <T> void writeToStream(OutputStream outputStream, String sheetName, Class<T> clazz, List<T> data) {
            EasyExcel.write(outputStream, clazz)
                    .registerWriteHandler(new AutoSizeHandler(columnConfig, rowConfig))
                    .sheet(sheetName)
                    .doWrite(data);
        }

        /**
         * 创建ExcelWriter（用于更复杂的场景）
         */
        public ExcelWriter createWriter(OutputStream outputStream) {
            return EasyExcel.write(outputStream)
                    .registerWriteHandler(new AutoSizeHandler(columnConfig, rowConfig))
                    .build();
        }

        /**
         * 获取配置好的AutoSizeHandler
         */
        public AutoSizeHandler build() {
            return new AutoSizeHandler(columnConfig, rowConfig);
        }
    }
}
