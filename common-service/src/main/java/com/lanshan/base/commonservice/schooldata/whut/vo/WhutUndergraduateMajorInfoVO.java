package com.lanshan.base.commonservice.schooldata.whut.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 本科生专业基本信息表(WhutUndergraduateMajorInfo)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "本科生专业基本信息表VO")
@Data
@ToString
public class WhutUndergraduateMajorInfoVO implements Serializable {

    @ApiModelProperty(value = "自增主键ID")
    private Long id;

    @ApiModelProperty(value = "专业编码")
    private String zybm;

    @ApiModelProperty(value = "专业名称")
    private String zymc;

    @ApiModelProperty(value = "专业全称")
    private String zyqc;

    @ApiModelProperty(value = "专业英文名称")
    private String zyywmc;

    @ApiModelProperty(value = "专业培养目标")
    private String zypymb;

    @ApiModelProperty(value = "专业培养要求")
    private String zypyyq;

    @ApiModelProperty(value = "专业类别")
    private String zylb;

    @ApiModelProperty(value = "归属大类名称")
    private String gsdlmc;

    @ApiModelProperty(value = "招生类别")
    private String zslb;

    @ApiModelProperty(value = "院系编码")
    private String yxbm;

    @ApiModelProperty(value = "校区编码")
    private String xqbm;

    @ApiModelProperty(value = "学制")
    private String xz;

    @ApiModelProperty(value = "培养层次")
    private String pycc;

    @ApiModelProperty(value = "本专科专业编码")
    private String bzkzybm;

    @ApiModelProperty(value = "学位")
    private String xw;

    @ApiModelProperty(value = "是否辅修专业")
    private String sffxzy;

    @ApiModelProperty(value = "启用标志码")
    private String qybz;

    @ApiModelProperty(value = "时间戳")
    private Date tstamp;
}

