package com.lanshan.base.commonservice.group.vo;

import com.lanshan.base.commonservice.group.entity.GroupChat;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 建群入参
 * <AUTHOR>
 */
@Data
@ApiModel(value = "群聊详情")
public class ChatInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "群聊信息")
    @NotNull(message = "")
    private GroupChat chat;
    @ApiModelProperty(value = "群聊接收范围-部门")
    private List<GroupChatScope> departmentList;
    @ApiModelProperty(value = "群聊接收范围-标签")
    private List<GroupChatScope> tagList;
    @ApiModelProperty(value = "群聊接收范围-用户")
    private List<GroupChatScope> userList;
    @ApiModelProperty(value = "应用agentId")
    private String agentId;
}
