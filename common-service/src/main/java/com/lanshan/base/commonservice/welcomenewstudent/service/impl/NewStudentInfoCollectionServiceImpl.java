package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentInfoCollectionConverter;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentInfoCollectionDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentInfoCollection;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStuInfoCollectionExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentDataQO;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentInfoCollectionQO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentInfoCollectionService;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentDataVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentInfoCollectionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 新生基本信息采集表(NewStudentInfoCollection)服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NewStudentInfoCollectionServiceImpl extends ServiceImpl<NewStudentInfoCollectionDao, NewStudentInfoCollection> implements NewStudentInfoCollectionService {

    @Resource
    private NewStudentDataService newStudentDataService;

    @Override
    public IPage<NewStudentInfoCollectionVO> page(NewStudentInfoCollectionQO qo) {
        // 根据QO创建分页参数
        Page<NewStudentInfoCollection> page = new Page<>(qo.getPage(), qo.getSize());

        // 构建查询条件
        LambdaQueryWrapper<NewStudentInfoCollection> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(qo.getName()), NewStudentInfoCollection::getName, qo.getName())
                .eq(qo.getNewStudentDataId() != null, NewStudentInfoCollection::getNewStudentDataId, qo.getNewStudentDataId())
                .eq(StringUtils.hasText(qo.getGender()), NewStudentInfoCollection::getGender, qo.getGender())
                .eq(StringUtils.hasText(qo.getIdCardNumber()), NewStudentInfoCollection::getIdCardNumber, qo.getIdCardNumber())
                .eq(StringUtils.hasText(qo.getPhone()), NewStudentInfoCollection::getPhone, qo.getPhone())
                .orderByDesc(NewStudentInfoCollection::getCreateDate);

        // 执行分页查询
        IPage<NewStudentInfoCollection> pageResult = page(page, wrapper);

        // 转换为VO
        Page<NewStudentInfoCollectionVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(NewStudentInfoCollectionConverter.INSTANCE.toVO(pageResult.getRecords()));

        return voPage;
    }

    @Override
    public boolean add(NewStudentInfoCollectionVO vo) {
        // 数据验证
        if (vo == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("新增参数不能为空");
        }

        // 转换为实体
        NewStudentInfoCollection entity = NewStudentInfoCollectionConverter.INSTANCE.toEntity(vo);

        // 保存
        return save(entity);
    }

    @Override
    public boolean update(NewStudentInfoCollectionVO qo) {
        // 数据验证
        if (qo == null || qo.getId() == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("修改参数不能为空");
        }

        // 检查记录是否存在
        NewStudentInfoCollection existing = getById(qo.getId());
        if (existing == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("记录不存在");
        }

        // 转换为实体
        NewStudentInfoCollection entity = NewStudentInfoCollectionConverter.INSTANCE.toEntity(qo);

        // 更新
        return updateById(entity);
    }

    @Override
    public NewStudentInfoCollectionVO getDetail(Long id) {
        // 参数验证
        if (id == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("ID不能为空");
        }

        // 查询实体
        NewStudentInfoCollection entity = getById(id);
        if (entity == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("记录不存在");
        }

        // 转换为VO
        return NewStudentInfoCollectionConverter.INSTANCE.toVO(entity);
    }

    @Override
    public boolean batchDel(List<Long> ids) {
        // 参数验证
        if (CollectionUtils.isEmpty(ids)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("删除ID列表不能为空");
        }

        // 批量删除
        return removeByIds(ids);
    }

    @Override
    public boolean removeById(Long id) {
        // 参数验证
        if (id == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("ID不能为空");
        }

        // 删除记录
        return super.removeById(id);
    }

    @Override
    public List<NewStuInfoCollectionExportDTO> exportData(NewStudentDataQO qo) {
        try {
            // 首先根据NewStudentDataQO查询新生数据
            List<NewStudentDataVO> studentDataVOList = newStudentDataService.exportStudentData(qo);

            if (studentDataVOList.isEmpty()) {
                return new ArrayList<>();
            }

            // 获取新生数据ID列表
            List<Long> studentDataIds = studentDataVOList.stream()
                    .map(NewStudentDataVO::getId)
                    .collect(Collectors.toList());

            // 根据新生数据ID查询信息采集数据
            LambdaQueryWrapper<NewStudentInfoCollection> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(NewStudentInfoCollection::getNewStudentDataId, studentDataIds);

            // 查询新生信息采集数据
            List<NewStudentInfoCollection> collections = list(queryWrapper);

            // 将学生数据转换为Map以提高查询性能
            Map<Long, NewStudentDataVO> studentDataMap = studentDataVOList.stream()
                    .collect(Collectors.toMap(NewStudentDataVO::getId, vo -> vo));

            // 转换为导出DTO
            List<NewStuInfoCollectionExportDTO> exportList = new ArrayList<>();

            for (NewStudentInfoCollection collection : collections) {
                NewStuInfoCollectionExportDTO exportDTO = new NewStuInfoCollectionExportDTO();

                // 设置基本信息（从Map中获取，避免重复查询数据库）
                if (collection.getNewStudentDataId() != null) {
                    NewStudentDataVO studentData = studentDataMap.get(collection.getNewStudentDataId());
                    if (studentData != null) {
                        exportDTO.setXh(studentData.getXh());
                        exportDTO.setCollegeName(studentData.getCollegeName());
                        exportDTO.setMajorName(studentData.getMajorName());
                        exportDTO.setClassName(studentData.getClassName());
                    }
                }

                // 设置采集信息
                exportDTO.setName(collection.getName());
                exportDTO.setGender(collection.getGender());
                exportDTO.setIdCardNumber(collection.getIdCardNumber());
                exportDTO.setPhone(collection.getPhone());
                exportDTO.setPoliticalStatus(collection.getPoliticalStatus());
                exportDTO.setNationality(collection.getNationality());

                // 格式化出生日期
                if (collection.getBirthDate() != null) {
                    exportDTO.setBirthDate(DateUtil.formatDate(collection.getBirthDate()));
                }

                exportDTO.setPermanentAddress(collection.getPermanentAddress());
                exportDTO.setNativePlace(collection.getNativePlace());
                exportDTO.setHouseholdAddress(collection.getHouseholdAddress());
                exportDTO.setStudentCategory(collection.getStudentCategory());

                // 格式化意外伤害保险标志
                if (collection.getAccidentInsuranceFlag() != null) {
                    exportDTO.setAccidentInsuranceFlag(collection.getAccidentInsuranceFlag() ? "是" : "否");
                }

                exportDTO.setHeight(collection.getHeight());
                exportDTO.setWeight(collection.getWeight());
                exportDTO.setShoeSize(collection.getShoeSize());
                exportDTO.setDailyNecessities(collection.getDailyNecessities());

                // 处理家长联系方式 - 格式化为指定格式
                exportDTO.setParentContactInfo(formatParentContactInfo(JSON.toJSONString(collection.getParentContactInfo())));

                exportList.add(exportDTO);
            }

            return exportList;
        } catch (Exception e) {
            log.error("导出新生基本信息采集数据失败", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导出数据失败");
        }
    }

    @Override
    public NewStudentInfoCollectionVO getByNewStudentDataId(Long newStudentDataId) {
        if (newStudentDataId == null) {
            return null;
        }
        NewStudentInfoCollection entity = baseMapper.selectOne(new LambdaQueryWrapper<NewStudentInfoCollection>()
                .eq(NewStudentInfoCollection::getNewStudentDataId, newStudentDataId));
        return NewStudentInfoCollectionConverter.INSTANCE.toVO(entity);
    }

    /**
     * 格式化家长联系方式
     * 将JSON格式转换为：姓名：xxx|关系：xxx|手机号:xxx;
     */
    private String formatParentContactInfo(String parentContactInfo) {
        if (parentContactInfo == null || parentContactInfo.trim().isEmpty()) {
            return "";
        }

        try {
            JSONArray contactArray = JSONArray.parseArray(parentContactInfo);
            StringBuilder formatted = new StringBuilder();

            for (int i = 0; i < contactArray.size(); i++) {
                JSONObject contact = contactArray.getJSONObject(i);
                String name = contact.getString("name");
                String relationship = contact.getString("relationship");
                String phone = contact.getString("phone");

                if (name != null || relationship != null || phone != null) {
                    formatted.append("姓名：").append(name != null ? "name" : "")
                            .append("|关系：").append(relationship != null ? relationship : "")
                            .append("|手机号：").append(phone != null ? phone : "");

                    if (i < contactArray.size() - 1) {
                        formatted.append(";\n");
                    }
                }
            }

            return formatted.toString();
        } catch (Exception e) {
            log.error("格式化家长联系方式失败，原始数据：{}", parentContactInfo, e);
            return parentContactInfo;
        }
    }
} 