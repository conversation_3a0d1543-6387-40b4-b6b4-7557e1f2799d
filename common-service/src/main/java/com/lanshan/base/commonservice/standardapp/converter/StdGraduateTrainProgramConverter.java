package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdGraduateTrainProgram;
import com.lanshan.base.commonservice.standardapp.vo.StdGraduateTrainProgramVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 研究生培养方案表(StdGraduateTrainProgram)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdGraduateTrainProgramConverter {

    StdGraduateTrainProgramConverter INSTANCE = Mappers.getMapper(StdGraduateTrainProgramConverter.class);

    StdGraduateTrainProgramVO toVO(StdGraduateTrainProgram entity);

    StdGraduateTrainProgram toEntity(StdGraduateTrainProgramVO vo);

    List<StdGraduateTrainProgramVO> toVO(List<StdGraduateTrainProgram> entityList);

    List<StdGraduateTrainProgram> toEntity(List<StdGraduateTrainProgramVO> voList);
}


