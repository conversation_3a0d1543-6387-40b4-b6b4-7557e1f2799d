package com.lanshan.base.commonservice.group.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * Description: new java files header
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 17:38
 */
@Data
@ApiModel("搜索群聊操作参数QO")
public class GetGroupChatQo extends PageQo{

    /**
     * 群聊主键ID
     */
    private Long id;

    /**
     * 群聊ID
     */
    private String chatid;

    /**
     * 群主ID
     */
    private String owner;

    /**
     * 群聊成员名称
     */
    private String name;

    /**
     * 标签ID
     */
    private Long tagid;

    /**
     * 组织路径
     */
    private Long departmentId;

    /**
     * 应用ID
     */
    private String agentId;
}
