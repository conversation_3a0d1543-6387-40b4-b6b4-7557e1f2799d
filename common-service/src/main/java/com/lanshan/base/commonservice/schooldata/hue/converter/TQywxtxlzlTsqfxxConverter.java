package com.lanshan.base.commonservice.schooldata.hue.converter;


import com.lanshan.base.commonservice.schooldata.hue.entity.TQywxtxlzlTsqfxx;
import com.lanshan.base.commonservice.schooldata.hue.vo.TQywxtxlzlTsqfxxVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 图书欠费信息(TQywxtxlzlTsqfxx)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface TQywxtxlzlTsqfxxConverter {

    TQywxtxlzlTsqfxxConverter INSTANCE = Mappers.getMapper(TQywxtxlzlTsqfxxConverter.class);

    TQywxtxlzlTsqfxxVO toVO(TQywxtxlzlTsqfxx entity);

    TQywxtxlzlTsqfxx toEntity(TQywxtxlzlTsqfxxVO vo);

    List<TQywxtxlzlTsqfxxVO> toVO(List<TQywxtxlzlTsqfxx> entityList);

    List<TQywxtxlzlTsqfxx> toEntity(List<TQywxtxlzlTsqfxxVO> voList);
}


