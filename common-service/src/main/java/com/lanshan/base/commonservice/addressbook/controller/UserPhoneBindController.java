package com.lanshan.base.commonservice.addressbook.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.annotation.OperationCallLog;
import com.lanshan.base.commonservice.addressbook.converter.CpUserPhoneConverter;
import com.lanshan.base.commonservice.addressbook.dto.UserPhoneBindDTO;
import com.lanshan.base.commonservice.addressbook.dto.UserPhoneSearchDTO;
import com.lanshan.base.commonservice.addressbook.entity.CpUserPhone;
import com.lanshan.base.commonservice.addressbook.qo.BindingUserQO;
import com.lanshan.base.commonservice.addressbook.qo.PerBindingVerifyQO;
import com.lanshan.base.commonservice.addressbook.qo.VerificationOfDocumentQO;
import com.lanshan.base.commonservice.addressbook.service.CpUserPhoneService;
import com.lanshan.base.commonservice.addressbook.vo.CpUserPhoneVO;
import com.lanshan.base.commonservice.addressbook.vo.PreBindingResultVO;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;
import com.lanshan.base.commonservice.addressbook.vo.UserSimpleInfo;
import com.lanshan.base.commonservice.enums.UserInfoOperationType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 用户手机号绑定表(CpUserPhone)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/phone")
@Api(tags = "用户手机号绑定表(CpUserPhone)控制层")
public class UserPhoneBindController {
    /**
     * 服务对象
     */
    @Resource
    private CpUserPhoneService cpUserPhoneService;

    /**
     * 分页查询所有数据
     *
     * @param dto 分页查询对象
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<CpUserPhoneVO>> selectAll(UserPhoneSearchDTO dto) {
        Page<CpUserPhone> page = new Page<>(dto.getPage(), dto.getSize());
        QueryWrapper<CpUserPhone> queryWrapper = new QueryWrapper<>(CpUserPhoneConverter.INSTANCE.toEntity(dto));
        IPage<CpUserPhone> pageData = this.cpUserPhoneService.page(page, queryWrapper);
        return Result.build(pageData.convert(CpUserPhoneConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<CpUserPhoneVO> selectOne(@PathVariable Serializable id) {
        return Result.build(CpUserPhoneConverter.INSTANCE.toVO(this.cpUserPhoneService.getById(id)));
    }

    @ApiOperation("获取当前信息")
    @GetMapping("/user/info")
    public Result<UserSimpleInfo> getUserInfo() {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(cpUserPhoneService.getUserSimpleInfo(userId));
    }

    @ApiOperation("获取用户已绑定的手机号")
    @GetMapping("/get/bind")
    public Result<CpUserPhoneVO> getPhoneBind() {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(this.cpUserPhoneService.getPhoneBind(userId));
    }

    @ApiOperation("检查用户数据是否可以绑定")
    @GetMapping("/check/can/bind")
    public Result<Boolean> checkCanBind() {
        String userId = SecurityContextHolder.getUserId();
        return Result.build(this.cpUserPhoneService.checkCanBind(userId));
    }

    @ApiOperation("获取用户绑定手机验证码")
    @GetMapping("/get/code")
    public Result<Object> getPhoneCode(@RequestParam String phoneNo) {
        return Result.build(this.cpUserPhoneService.getPhoneCode(phoneNo));
    }

    @ApiOperation("用户绑定手机号")
    @PostMapping("/bind")
    public Result<Boolean> bind(@RequestBody UserPhoneBindDTO dto) {
        return Result.build(this.cpUserPhoneService.bind(dto));
    }

    @ApiOperation("用户确认绑定手机号")
    @PostMapping("/bind/confirm")
    public Result<Boolean> bindConfirm() {
        return Result.build(this.cpUserPhoneService.bindConfirm());
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> insert(@RequestBody CpUserPhoneVO vo) {
        return Result.build(this.cpUserPhoneService.save(CpUserPhoneConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody CpUserPhoneVO vo) {
        return Result.build(this.cpUserPhoneService.updateById(CpUserPhoneConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.cpUserPhoneService.removeByIds(idList));
    }

    @OperationCallLog(operationType = UserInfoOperationType.USER_AUTH, operationTitle = "证件核验认证（港澳台）")
    @ApiOperation(value = "证件核验-港澳台")
    @PostMapping(value = "verificationOfDocument")
    public Result<Boolean> verificationOfDocument(@RequestBody VerificationOfDocumentQO qo) {
        return Result.build(cpUserPhoneService.verificationOfDocument(qo));
    }

    @OperationCallLog(operationType = UserInfoOperationType.USER_AUTH, operationTitle = "证件核验认证（留学生、外籍）")
    @ApiOperation(value = "证件核验-留学生，外籍")
    @PostMapping(value = "verificationOfDocumentWj")
    public Result<Boolean> verificationOfDocumentWj(@RequestBody VerificationOfDocumentQO qo) {
        return Result.build(cpUserPhoneService.verificationOfDocument(qo));
    }

    @ApiOperation(value = "获取身份列表")
    @GetMapping(value = "listIdentities")
    public Result<List<UserIdentityInfoVO>> listIdentities() {
        //微信 openId
        String openId = SecurityContextHolder.getUserId();
        return Result.build(cpUserPhoneService.listIdentities(openId));
    }

    @ApiOperation(value = "更新默认身份")
    @PostMapping(value = "updateDefaultIdentity")
    public Result<Boolean> updateDefaultIdentity(Long userInfoBindId) {
        return Result.build(cpUserPhoneService.updateDefaultIdentity(userInfoBindId));
    }

    @ApiOperation(value = "绑定前校验")
    @PostMapping(value = "perBindingVerify")
    public Result<PreBindingResultVO> perBindingVerify(@Validated @RequestBody PerBindingVerifyQO qo) {
        return Result.build(cpUserPhoneService.perBindingVerify(qo));
    }

    @OperationCallLog(operationType = UserInfoOperationType.USER_BINDING, operationTitle = "企微账号开通")
    @ApiOperation(value = "绑定")
    @PostMapping(value = "binding")
    public Result<Boolean> binding(@RequestBody BindingUserQO qo) {
        return Result.build(cpUserPhoneService.binding(qo));
    }

    @OperationCallLog(operationType = UserInfoOperationType.USER_BINDING, operationTitle = "企微账号换绑")
    @ApiOperation(value = "换绑")
    @PostMapping(value = "changeBinding")
    public Result<Boolean> changeBinding(@RequestBody BindingUserQO qo) {
        return Result.build(cpUserPhoneService.binding(qo));
    }

    @OperationCallLog(operationType = UserInfoOperationType.USER_BINDING, operationTitle = "注销原账号重新绑定")
    @ApiOperation(value = "注销绑定")
    @PostMapping(value = "deregisterBinding")
    public Result<Boolean> deregisterBinding(@RequestBody BindingUserQO qo) {
        return Result.build(cpUserPhoneService.binding(qo));
    }

    @ApiOperation(value = "获取客服二维码")
    @GetMapping("/getHelpUrl")
    public Result<String> getHelpUrl(@RequestParam String userType) {
        return Result.build(cpUserPhoneService.getHelpUrl(userType));
    }

    @ApiOperation(value = "更新身份列表")
    @PostMapping(value = "updateIdentities")
    public Result<List<UserIdentityInfoVO>> updateIdentities() {
        //微信 openId
        String openId = SecurityContextHolder.getUserId();
        return Result.build(cpUserPhoneService.updateIdentities(openId));
    }

    @ApiOperation(value = "获取新生身份信息")
    @GetMapping(value = "/getNewStuIdentityInfo")
    public Result<UserIdentityInfoVO> getNewStuIdentityInfo() {
        return Result.build(cpUserPhoneService.getNewStuIdentityInfo());
    }
}

