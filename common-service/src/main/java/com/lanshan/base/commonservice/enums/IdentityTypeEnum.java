package com.lanshan.base.commonservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2025/6/6 16:04
 */
@AllArgsConstructor
@Getter
public enum IdentityTypeEnum {

    ID_CARD("身份证", "0"),
    PASSPORT("护照", "1"),
    //港澳来往内地通行证
    HK_MACAO_PASSPORT("港澳来往内地通行证", "4"),
    //台湾居民来往大陆通行证
    TAIWAN_PASSPORT("台湾居民来往大陆通行证", "7"),
    ;

    private final String remark;

    private final String value;

    public static String getValueByRemark(String remark) {
        for (IdentityTypeEnum value : values()) {
            if (value.getRemark().equals(remark)) {
                return value.getValue();
            }
        }
        return null;
    }

    public static String getRemarkByValue(String value) {
        for (IdentityTypeEnum value1 : values()) {
            if (value1.getValue().equals(value)) {
                return value1.getRemark();
            }
        }
        return null;
    }
}
