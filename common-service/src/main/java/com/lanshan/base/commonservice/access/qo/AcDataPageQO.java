package com.lanshan.base.commonservice.access.qo;


import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "权限数据集PageQO")
@Data
@ToString
public class AcDataPageQO extends PageQo implements Serializable {

    private static final long serialVersionUID = -6383729814459099265L;

    @ApiModelProperty(value = "数据权限唯一标识")
    private String dataKey;

    @ApiModelProperty(value = "数据权限名称")
    private String dataName;
}

