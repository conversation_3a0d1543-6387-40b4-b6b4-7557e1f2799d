package com.lanshan.base.commonservice.identify.service;

import com.lanshan.base.commonservice.identify.dto.ChangeCurrentIdentifyDto;
import com.lanshan.base.commonservice.identify.dto.CurrentIdentifyDto;
import com.lanshan.base.commonservice.identify.dto.IDentifyNumDto;
import com.lanshan.base.commonservice.identify.dto.IdentifyListDto;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;

import java.util.List;

/**
 * @program: capability-platform-base
 * @Description:
 * @author: JZ
 * @createTime: 2025-04-14 10:49
 */
public interface IdentifyApiService {
    String getLsUserId(IDentifyNumDto iDentifyNumDto);

    List<StaffIdentifyRelation> getIdentifyList(IdentifyListDto identifyListDto);

    StaffIdentifyRelation getCurrentIdentify(CurrentIdentifyDto currentIdentifyDto);

    void changeCurrentIdentify(ChangeCurrentIdentifyDto changeCurrentIdentifyDto);
}
