# EasyExcel自适应列宽和行高功能

## 概述

本模块提供了EasyExcel的自适应列宽和行高功能，能够根据单元格内容自动调整Excel表格的列宽和行高，提升导出文件的可读性和美观度。

## 功能特性

### 自适应列宽
- ✅ 根据内容自动调整列宽
- ✅ 支持中文字符宽度优化（中文字符按2个字符宽度计算）
- ✅ 支持最大最小宽度限制
- ✅ 性能优化，避免频繁计算

### 自适应行高
- ✅ 根据内容自动调整行高
- ✅ 支持多行文本自动换行
- ✅ 支持最大最小行高限制
- ✅ 支持标题行特殊处理

### 组合功能
- ✅ 同时支持列宽和行高自适应
- ✅ 灵活的配置选项
- ✅ 简化的API调用

## 核心类说明

### 1. AutoColumnWidthHandler
自适应列宽处理器，实现`SheetWriteHandler`接口。

**主要功能：**
- 计算每列的最大宽度
- 支持中文字符宽度优化
- 设置列宽限制

### 2. AutoRowHeightHandler
自适应行高处理器，实现`RowWriteHandler`接口。

**主要功能：**
- 计算每行所需的高度
- 支持多行文本处理
- 设置行高限制

### 3. AutoSizeHandler
组合处理器，同时实现`SheetWriteHandler`和`RowWriteHandler`接口。

**主要功能：**
- 同时处理列宽和行高
- 统一的配置管理
- 更好的性能表现

### 4. EasyExcelAutoSizeUtil
工具类，提供便捷的API调用方式。

## 使用方式

### 1. 基础用法

```java
// 最简单的用法 - 使用默认配置
EasyExcelAutoSizeUtil.writeWithAutoSize(response, "文件名", "工作表名", ExportDTO.class, dataList);

// 只使用自适应列宽
EasyExcelAutoSizeUtil.writeWithAutoColumnWidth(response, "文件名", "工作表名", ExportDTO.class, dataList);

// 只使用自适应行高
EasyExcelAutoSizeUtil.writeWithAutoRowHeight(response, "文件名", "工作表名", ExportDTO.class, dataList);
```

### 2. 自定义配置

```java
// 使用构建器模式进行自定义配置
EasyExcelAutoSizeUtil.builder()
    .maxColumnWidth(60)              // 最大列宽60字符
    .minColumnWidth(10)              // 最小列宽10字符
    .maxRowHeight(100f)              // 最大行高100磅
    .minRowHeight(15f)               // 最小行高15磅
    .headerRowHeight(25f)            // 标题行高25磅
    .enableChineseOptimization(true) // 启用中文字符优化
    .enableAutoWrap(true)            // 启用自动换行
    .writeToResponse(response, "文件名", "工作表名", ExportDTO.class, dataList);
```

### 3. 直接使用处理器

```java
// 直接使用AutoSizeHandler
EasyExcel.write(response.getOutputStream(), ExportDTO.class)
    .registerWriteHandler(new AutoSizeHandler())
    .sheet("工作表名")
    .doWrite(dataList);

// 使用自定义配置的处理器
AutoSizeHandler.ColumnWidthConfig columnConfig = new AutoSizeHandler.ColumnWidthConfig(50, 8, true);
AutoSizeHandler.RowHeightConfig rowConfig = new AutoSizeHandler.RowHeightConfig(80f, 12f, 15f, 20f, 15f, true);
AutoSizeHandler handler = new AutoSizeHandler(columnConfig, rowConfig);

EasyExcel.write(response.getOutputStream(), ExportDTO.class)
    .registerWriteHandler(handler)
    .sheet("工作表名")
    .doWrite(dataList);
```

### 4. 复杂场景使用

```java
// 创建ExcelWriter用于多个工作表
ExcelWriter excelWriter = EasyExcelAutoSizeUtil.builder()
    .maxColumnWidth(50)
    .maxRowHeight(80f)
    .createWriter(response.getOutputStream());

// 写入第一个工作表
WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "工作表1").head(DTO1.class).build();
excelWriter.write(dataList1, writeSheet1);

// 写入第二个工作表
WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "工作表2").head(DTO2.class).build();
excelWriter.write(dataList2, writeSheet2);

// 关闭writer
excelWriter.finish();
```

## 配置参数说明

### 列宽配置 (ColumnWidthConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| maxColumnWidth | int | 50 | 最大列宽（字符数） |
| minColumnWidth | int | 8 | 最小列宽（字符数） |
| enableChineseOptimization | boolean | true | 是否启用中文字符优化 |

### 行高配置 (RowHeightConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| maxRowHeight | float | 100f | 最大行高（磅） |
| minRowHeight | float | 12f | 最小行高（磅） |
| defaultRowHeight | float | 15f | 默认行高（磅） |
| headerRowHeight | float | 20f | 标题行高（磅） |
| heightPerLine | float | 15f | 每行字符对应的行高增量（磅） |
| enableAutoWrap | boolean | true | 是否启用自动换行 |

## 性能优化

1. **缓存机制**：列宽计算结果会被缓存，避免重复计算
2. **中文字符优化**：可选择是否启用中文字符宽度优化
3. **合理的默认值**：提供了经过测试的默认配置参数
4. **批量处理**：支持大数据量的批量导出

## 注意事项

1. **内存使用**：大数据量导出时注意内存使用情况
2. **字符编码**：确保正确设置字符编码，避免中文乱码
3. **浏览器兼容性**：文件下载功能在不同浏览器中的表现可能有差异
4. **Excel版本**：生成的文件格式为xlsx，确保目标系统支持

## 实际应用示例

在NewStudentDataController中的应用：

```java
@ApiOperation("导出新生数据")
@PostMapping("/exportStudentData")
public void exportStudentData(@RequestBody NewStudentDataQO qo, HttpServletResponse response) {
    List<NewStudentDataVO> dataList = newStudentDataService.exportStudentData(qo);
    List<NewStudentExportDTO> exportList = convertToExportDTO(dataList);

    // 使用自适应功能
    EasyExcelAutoSizeUtil.builder()
        .maxColumnWidth(60)
        .minColumnWidth(10)
        .maxRowHeight(100f)
        .headerRowHeight(25f)
        .enableChineseOptimization(true)
        .enableAutoWrap(true)
        .writeToResponse(response, "新生信息表", "新生信息表", NewStudentExportDTO.class, exportList);
}
```

## 扩展开发

如需自定义更复杂的逻辑，可以：

1. 继承现有的处理器类
2. 实现EasyExcel的相关接口
3. 添加自定义的计算逻辑
4. 集成到现有的工具类中

## 版本历史

- v1.0.0 (2025/7/6)
  - 初始版本
  - 支持自适应列宽和行高
  - 提供便捷的工具类API
