package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "周课程表信息VO")
public class CourseDayVO implements Serializable {

    private static final long serialVersionUID = 5873642878117570776L;

    @ApiModelProperty(value = "星期几或周几")
    private String dayOfWeek;

    @ApiModelProperty(value = "课程安排信息列表")
    private List<StdCourseScheduleVO> courseScheduleList;
}
