package com.lanshan.base.commonservice.todo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.lanshan.base.commonservice.todo.entity.TodoWhitelistConfig;

/**
 * 白名单配置表(TodoWhitelistConfig)表数据库访问层
 *
 * <AUTHOR>
 */
public interface TodoWhitelistConfigMapper extends BaseMapper<TodoWhitelistConfig> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TodoWhitelistConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TodoWhitelistConfig> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TodoWhitelistConfig> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TodoWhitelistConfig> entities);

    TodoWhitelistConfig selectByAppId(@Param("appId") String appId);
}

