package com.lanshan.base.commonservice.addressbook.event;

import org.springframework.context.ApplicationEvent;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class NewStudentChangeEvent extends ApplicationEvent {

    /**
     * 导入的新生数据
     */

    private final List<String> userId;

    /**
     * 变更类型
     */
    private final NewStudentChangeEvent.ChangeType changeType;

    public NewStudentChangeEvent(Object source, List<String> userIds, ChangeType changeType) {
        super(source);
        this.changeType = changeType;
        this.userId = userIds;
    }

    public NewStudentChangeEvent(Object source, String userId, ChangeType changeType) {
        super(source);
        this.userId = Collections.singletonList(userId);
        this.changeType = changeType;
    }


    public enum ChangeType {
        //导入
        IMPORT,
        //认证成功
        AUTH_SUCCESS,
        //开通成功
        OPEN_SUCCESS
    }
}
