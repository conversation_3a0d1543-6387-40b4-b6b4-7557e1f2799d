package com.lanshan.base.commonservice.todo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.todo.dto.TodoNodeUserExistsByFlowInfoDto;
import com.lanshan.base.commonservice.todo.entity.TodoFlowCopyUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 待办抄送用户表(TodoFlowCopyUser)数据库访问层
 */

@Mapper
public interface TodoFlowCopyUserMapper extends BaseMapper<TodoFlowCopyUser> {

    /**
     * 根据流程信息查询代办节点抄送人
     * @param param 查询条件
     * @return 用户列表
     */
    List<String> listTodoCopyUserByFlowNode(@Param("param") TodoNodeUserExistsByFlowInfoDto param);

    /**
     * 根据用户id查询抄送应用id列表
     *
     * @param userid 用户id
     * @return 应用id列表
     */
    List<Long> listCopyAppId(@Param("userid") String userid,@Param("blackList") List<Long> blackList);
}

