package com.lanshan.base.commonservice.welcomenewstudent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentTaskQO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentTaskVO;

import java.util.List;

/**
 * 新生任务表(NewStudentTask)表服务接口
 *
 * <AUTHOR>
 */
public interface NewStudentTaskService extends IService<NewStudentTask> {

    /**
     * 分页查询所有数据
     *
     * @param qo 查询条件
     * @return 查询结果
     */
    IPage<NewStudentTaskVO> pageByParam(NewStudentTaskQO qo);

    /**
     * 完成新生任务
     *
     * @param newStudentTaskId 任务主键
     * @return
     */
    Boolean completeNewStudentTask(Long newStudentTaskId);

    /**
     * 获取所有已完成的新生任务
     *
     * @return
     */
    List<Long> getAllCompletedNewStudentTask();

    /**
     * 获取最大排序
     *
     * @return
     */
    Integer getMaxOrder();

    /**
     * 签到
     *
     * @return
     */
    Boolean clockIn();
}

