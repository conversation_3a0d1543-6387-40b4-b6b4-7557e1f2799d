package com.lanshan.base.commonservice.schooldata.hue.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.schooldata.hue.entity.TQywxtxlzlTsqfxx;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图书欠费信息(TQywxtxlzlTsqfxx)表数据库访问层
 *
 * <AUTHOR>
 */
public interface TQywxtxlzlTsqfxxDao extends BaseMapper<TQywxtxlzlTsqfxx> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TQywxtxlzlTsqfxx> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TQywxtxlzlTsqfxx> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TQywxtxlzlTsqfxx> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TQywxtxlzlTsqfxx> entities);

}

