package com.lanshan.base.commonservice.standardapp.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "CourseGroupChatQo", description = "课程群查询条件对象")
public class CourseGroupChatQo extends PageQo implements Serializable {

    @ApiModelProperty(value = "学年")
    private String teachYear;

    @ApiModelProperty(value = "学期")
    private String term;

    @ApiModelProperty(value = "教学班名称")
    private String teachClassName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "教工工号")
    private String teacherId;

    @ApiModelProperty(value = "教工姓名")
    private String teacherName;

    @ApiModelProperty(value = "群状态 0未建群 1已建群")
    private String groupStatus;

    @ApiModelProperty(value = "班级类型  2：本科生 3：研究生")
    private String courseType;

    @ApiModelProperty(value = "存在建群失败原因")
    private Boolean hasError;
}
