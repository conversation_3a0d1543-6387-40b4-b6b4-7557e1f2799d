package com.lanshan.base.commonservice.todo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("待办参与人Vo")
public class TodoUserVo implements Serializable {
    private static final long serialVersionUID = 500436445835705313L;

    @ApiModelProperty(value = "userid")
    private String userid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "阅读情况")
    private Integer isRead;

    @ApiModelProperty(value = "标签列表")
    private List<TagVo> tagVoList;


    @ApiModelProperty(value = "部门列表")
    private List<DepartmentVo> departmentVoList;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class TagVo implements Serializable {
        private static final long serialVersionUID = 1;

        @ApiModelProperty(value = "标签id")
        private Long tagid;

        @ApiModelProperty(value = "标签名称")
        private String tagname;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class DepartmentVo implements Serializable {
        private static final long serialVersionUID = 1;

        @ApiModelProperty(value = "标签id")
        private Long departmentid;

        @ApiModelProperty(value = "标签名称")
        private String departmentName;
    }
}

