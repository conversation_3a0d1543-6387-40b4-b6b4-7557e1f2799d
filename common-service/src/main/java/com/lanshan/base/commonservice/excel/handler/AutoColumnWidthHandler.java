package com.lanshan.base.commonservice.excel.handler;

import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

/**
 * EasyExcel自适应列宽处理器
 * 
 * 功能：
 * 1. 根据内容自动调整列宽
 * 2. 支持中文字符宽度计算
 * 3. 支持最大最小宽度限制
 * 4. 优化性能，避免频繁计算
 * 
 * 使用方式：
 * EasyExcel.write(outputStream, ExportDTO.class)
 *     .registerWriteHandler(new AutoColumnWidthHandler())
 *     .sheet("数据")
 *     .doWrite(data);
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class AutoColumnWidthHandler implements WorkbookWriteHandler {

    /**
     * 最大列宽（字符数）
     */
    private static final int MAX_COLUMN_WIDTH = 50;
    
    /**
     * 最小列宽（字符数）
     */
    private static final int MIN_COLUMN_WIDTH = 8;
    
    /**
     * 是否启用中文字符宽度优化
     */
    private final boolean enableChineseOptimization;
    
    /**
     * 自定义最大列宽
     */
    private final int maxColumnWidth;
    
    /**
     * 自定义最小列宽
     */
    private final int minColumnWidth;

    /**
     * 默认构造函数
     */
    public AutoColumnWidthHandler() {
        this(true, MAX_COLUMN_WIDTH, MIN_COLUMN_WIDTH);
    }

    /**
     * 自定义构造函数
     * 
     * @param enableChineseOptimization 是否启用中文字符宽度优化
     * @param maxColumnWidth 最大列宽
     * @param minColumnWidth 最小列宽
     */
    public AutoColumnWidthHandler(boolean enableChineseOptimization, int maxColumnWidth, int minColumnWidth) {
        this.enableChineseOptimization = enableChineseOptimization;
        this.maxColumnWidth = maxColumnWidth;
        this.minColumnWidth = minColumnWidth;
    }

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 在工作簿处理完成后调整所有工作表的列宽
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            autoSizeAllColumns(sheet);
        }
    }
    
    /**
     * 自动调整所有列的宽度
     */
    private void autoSizeAllColumns(Sheet sheet) {
        if (sheet.getPhysicalNumberOfRows() == 0) {
            return;
        }
        
        // 获取第一行来确定列数
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            return;
        }
        
        int lastCellNum = firstRow.getLastCellNum();
        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            autoSizeColumn(sheet, columnIndex);
        }
    }
    
    /**
     * 自动调整指定列的宽度
     */
    private void autoSizeColumn(Sheet sheet, int columnIndex) {
        int maxWidth = minColumnWidth;
        
        // 遍历该列的所有行来计算最大宽度
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                Cell cell = row.getCell(columnIndex);
                if (cell != null) {
                    String cellValue = getCellValueAsString(cell);
                    if (cellValue != null) {
                        int width = calculateStringWidth(cellValue);
                        maxWidth = Math.max(maxWidth, width);
                    }
                }
            }
        }
        
        // 确保宽度在合理范围内
        int finalWidth = Math.max(minColumnWidth, Math.min(maxWidth, maxColumnWidth));
        
        // 设置列宽（POI中的宽度单位是1/256字符宽度）
        sheet.setColumnWidth(columnIndex, finalWidth * 256);
    }
    
    /**
     * 获取单元格值的字符串表示
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 计算字符串显示宽度
     * 
     * @param str 字符串
     * @return 显示宽度
     */
    private int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }
        
        if (!enableChineseOptimization) {
            return str.length();
        }
        
        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                // 中文字符占2个字符宽度
                width += 2;
            } else {
                // 英文字符占1个字符宽度
                width += 1;
            }
        }
        
        return width;
    }

    /**
     * 判断是否为中文字符
     * 
     * @param c 字符
     * @return 是否为中文字符
     */
    private boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // 基本汉字
               (c >= 0x3400 && c <= 0x4DBF) ||  // 扩展A
               (c >= 0x20000 && c <= 0x2A6DF) || // 扩展B
               (c >= 0x2A700 && c <= 0x2B73F) || // 扩展C
               (c >= 0x2B740 && c <= 0x2B81F) || // 扩展D
               (c >= 0x2B820 && c <= 0x2CEAF) || // 扩展E
               (c >= 0x2CEB0 && c <= 0x2EBEF) || // 扩展F
               (c >= 0x30000 && c <= 0x3134F) || // 扩展G
               (c >= 0x3400 && c <= 0x4DB5) ||   // 扩展A
               (c >= 0xFF00 && c <= 0xFFEF);     // 全角字符
    }

    /**
     * 创建默认的自适应列宽处理器
     * 
     * @return AutoColumnWidthHandler实例
     */
    public static AutoColumnWidthHandler create() {
        return new AutoColumnWidthHandler();
    }

    /**
     * 创建自定义配置的自适应列宽处理器
     * 
     * @param maxWidth 最大列宽
     * @param minWidth 最小列宽
     * @return AutoColumnWidthHandler实例
     */
    public static AutoColumnWidthHandler create(int maxWidth, int minWidth) {
        return new AutoColumnWidthHandler(true, maxWidth, minWidth);
    }

    /**
     * 创建禁用中文优化的自适应列宽处理器
     * 
     * @return AutoColumnWidthHandler实例
     */
    public static AutoColumnWidthHandler createWithoutChineseOptimization() {
        return new AutoColumnWidthHandler(false, MAX_COLUMN_WIDTH, MIN_COLUMN_WIDTH);
    }
}
