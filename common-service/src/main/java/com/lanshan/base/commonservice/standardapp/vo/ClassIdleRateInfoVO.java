package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(value = "ClassIdleRateInfoVO", description = "班级空闲率信息")
public class ClassIdleRateInfoVO {

    @ApiModelProperty(value = "班级号")
    private String classNumber;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "总学生数")
    private Integer totalStudents;

    @ApiModelProperty(value = "总有课学生数")
    private Integer totalHasCourseStudents;

    @ApiModelProperty(value = "总空闲学生数")
    private Integer totalIdleStudents;
}
