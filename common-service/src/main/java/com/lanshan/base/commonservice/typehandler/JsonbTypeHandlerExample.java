package com.lanshan.base.commonservice.typehandler;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * JSONB类型处理器使用示例
 * 
 * 本示例展示了如何在实体类中使用各种JSONB类型处理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class JsonbTypeHandlerExample {

    /**
     * 示例实体类 - 用户信息
     * 展示如何直接在注解中使用各种JSONB类型处理器
     */
    @Data
    @TableName("user_info")
    public static class UserInfo {

        private Long id;

        private String username;

        /**
         * 使用JSONB字符串处理器
         * 数据库中的JSONB内容会直接作为字符串返回
         * 数据库示例：{"name":"<PERSON>","age":30}
         */
        @TableField(typeHandler = JsonbStringTypeHandler.class)
        private String profileJson;

        /**
         * 使用JSONB字符串列表处理器
         * 适用于存储标签、权限等字符串数组
         * 数据库示例：["tag1", "tag2", "tag3"]
         */
        @TableField(typeHandler = JsonbStringListTypeHandler.class)
        private List<String> tags;

        /**
         * 使用JSONB整数列表处理器
         * 适用于存储用户ID、分类ID等整数数组
         * 数据库示例：[1, 2, 3]
         */
        @TableField(typeHandler = JsonbIntegerListTypeHandler.class)
        private List<Integer> categoryIds;

        /**
         * 使用JSONB长整数列表处理器
         * 适用于存储大数值ID、时间戳等
         * 数据库示例：[1001, 1002, 1003] 或 ["1001", "1002", "1003"]
         */
        @TableField(typeHandler = JsonbLongListTypeHandler.class)
        private List<Long> joinInUser;

        /**
         * 使用JSONB对象Map处理器
         * 适用于存储配置信息、扩展属性等混合类型
         * 数据库示例：{"theme":"dark","language":"zh-CN","notifications":true}
         */
        @TableField(typeHandler = JsonbMapTypeHandler.class)
        private Map<String, Object> settings;

        /**
         * 使用JSONB字符串Map处理器
         * 适用于存储纯字符串的键值对
         * 数据库示例：{"label1":"value1","label2":"value2"}
         */
        @TableField(typeHandler = JsonbStringMapTypeHandler.class)
        private Map<String, String> labels;

        /**
         * 使用自定义对象类型处理器
         * 需要创建专门的类型处理器
         */
        @TableField(typeHandler = UserProfileTypeHandler.class)
        private UserProfile profile;

        /**
         * 使用自定义对象列表类型处理器
         * 处理复杂的泛型类型
         */
        @TableField(typeHandler = UserProfileListTypeHandler.class)
        private List<UserProfile> profileHistory;
    }

    /**
     * 用户档案信息
     */
    @Data
    public static class UserProfile {
        private String nickname;
        private String avatar;
        private String email;
        private Integer age;
        private Map<String, String> socialLinks;
    }

    /**
     * 自定义UserProfile类型处理器
     */
    public static class UserProfileTypeHandler extends UniversalJsonbTypeHandler<UserProfile> {
        public UserProfileTypeHandler() {
            super(UserProfile.class);
        }
    }

    /**
     * 自定义List<UserProfile>类型处理器
     */
    public static class UserProfileListTypeHandler extends UniversalJsonbTypeHandler<List<UserProfile>> {
        public UserProfileListTypeHandler() {
            super(new TypeReference<List<UserProfile>>() {});
        }
    }

    /**
     * MyBatis XML配置示例
     * 
     * 在MyBatis的XML映射文件中，可以这样使用：
     * 
     * <!-- 查询时指定类型处理器 -->
     * <select id="selectUserById" resultType="UserInfo">
     *     SELECT id, username, 
     *            profile_json,
     *            tags,
     *            settings,
     *            profile,
     *            profile_history
     *     FROM user_info 
     *     WHERE id = #{id}
     * </select>
     * 
     * <!-- 插入时指定类型处理器 -->
     * <insert id="insertUser">
     *     INSERT INTO user_info (username, profile_json, tags, settings, profile, profile_history)
     *     VALUES (
     *         #{username},
     *         #{profileJson,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringTypeHandler},
     *         #{tags,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbListTypeHandler},
     *         #{settings,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbMapTypeHandler},
     *         #{profile,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbTypeHandlerExample$UserProfileTypeHandler},
     *         #{profileHistory,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbTypeHandlerExample$UserProfileListTypeHandler}
     *     )
     * </insert>
     */

    /**
     * 使用建议和最佳实践：
     * 
     * 1. 选择合适的类型处理器：
     *    - 简单字符串：使用 JsonbStringTypeHandler
     *    - 字符串数组：使用 JsonbListTypeHandler 或自定义 List<String> 处理器
     *    - 键值对：使用 JsonbMapTypeHandler
     *    - 自定义对象：继承 UniversalJsonbTypeHandler 并指定具体类型
     * 
     * 2. 性能考虑：
     *    - FastJSON2 通常比 Jackson 更快，但 Jackson 更稳定
     *    - 可以通过构造函数参数选择优先使用的JSON库
     *    - 对于简单类型，建议使用 FastJSON2
     *    - 对于复杂泛型类型，建议使用 Jackson
     * 
     * 3. 错误处理：
     *    - 类型处理器会自动处理JSON解析异常
     *    - 当一种JSON库失败时，会自动尝试另一种
     *    - 所有异常都会被记录到日志中
     * 
     * 4. 数据库兼容性：
     *    - 主要针对PostgreSQL的JSONB类型设计
     *    - 也兼容PostgreSQL的JSON类型
     *    - 对于其他数据库，可能需要调整PGobject的处理逻辑
     * 
     * 5. 调试和监控：
     *    - 启用DEBUG日志级别可以看到详细的转换过程
     *    - 所有转换操作都有完整的日志记录
     *    - 异常信息包含具体的位置和原因
     */
}
