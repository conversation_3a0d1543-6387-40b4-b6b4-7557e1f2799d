package com.lanshan.base.commonservice.addressbook.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.entity.CpUserImportTagLogDetail;
import com.lanshan.base.commonservice.addressbook.qo.CpUserImportTagLogDetailQO;
import com.lanshan.base.commonservice.addressbook.vo.CpUserImportTagLogDetailVO;
import com.lanshan.base.commonservice.addressbook.service.CpUserImportTagLogDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 企业微信用户导入标签日志详情控制器
 */
@RestController
@RequestMapping("/addressbook/cpUserImportTagLogDetail")
@Api(tags = "企业微信用户导入标签日志详情")
public class CpUserImportTagLogDetailController {

    @Resource
    private CpUserImportTagLogDetailService cpUserImportTagLogDetailService;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    @ApiOperation("分页查询")
    public Result<IPage<CpUserImportTagLogDetailVO>> page(@RequestBody CpUserImportTagLogDetailQO qo) {
        return Result.build(cpUserImportTagLogDetailService.page(qo));
    }

    /**
     * 新增
     */
    @PostMapping
    @ApiOperation("新增")
    public Result<Boolean> save(@RequestBody CpUserImportTagLogDetailQO qo) {
        return Result.build(cpUserImportTagLogDetailService.add(qo));
    }

    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.build(cpUserImportTagLogDetailService.removeById(id));
    }

    /**
     * 修改
     */
    @PutMapping
    @ApiOperation("修改")
    public Result<Boolean> update(@RequestBody CpUserImportTagLogDetailQO qo) {
        return Result.build(cpUserImportTagLogDetailService.update(qo));
    }

    /**
     * 查询详情
     */
    @GetMapping("/{id}")
    @ApiOperation("查询详情")
    public Result<CpUserImportTagLogDetailVO> getDetail(@PathVariable Long id) {
        return Result.build(cpUserImportTagLogDetailService.getDetail(id));
    }
}