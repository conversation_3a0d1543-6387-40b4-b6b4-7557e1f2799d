package com.lanshan.base.commonservice.typehandler;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * JSONB对象数组类型处理器使用示例
 * 
 * 展示如何处理 [{"aaa":""},{"aaa":""}] 这种对象数组类型
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class JsonbObjectArrayExample {

    /**
     * 示例实体类 - 表单配置
     */
    @Data
    @TableName("form_config")
    public static class FormConfig {
        
        private Long id;
        private String formName;
        
        /**
         * 使用JsonbStringObjectListTypeHandler处理纯字符串对象数组
         * 适用于：[{"aaa":""},{"aaa":""}] 或 [{"fieldName":"username","fieldValue":"john"}]
         */
        @TableField(typeHandler = JsonbStringObjectListTypeHandler.class)
        private List<Map<String, String>> formFields;
        
        /**
         * 使用JsonbObjectListTypeHandler处理混合类型对象数组
         * 适用于：[{"id":1,"name":"test","active":true},{"id":2,"name":"demo","active":false}]
         */
        @TableField(typeHandler = JsonbObjectListTypeHandler.class)
        private List<Map<String, Object>> formItems;
        
        /**
         * 使用自定义类型处理器处理特定结构的对象数组
         */
        @TableField(typeHandler = FormFieldListTypeHandler.class)
        private List<FormField> customFields;
    }

    /**
     * 自定义表单字段对象
     */
    @Data
    public static class FormField {
        private String fieldName;
        private String fieldType;
        private String fieldValue;
        private Boolean required;
        private String placeholder;
    }

    /**
     * 自定义FormField列表类型处理器
     */
    public static class FormFieldListTypeHandler extends UniversalJsonbTypeHandler<List<FormField>> {
        public FormFieldListTypeHandler() {
            super(new TypeReference<List<FormField>>() {});
        }
    }

    /**
     * 使用示例
     */
    public static class UsageExample {
        
        public void demonstrateUsage() {
            FormConfig config = new FormConfig();
            
            // 示例1：处理 [{"aaa":""},{"aaa":""}] 类型
            // 数据库中存储：[{"aaa":""},{"bbb":"value"}]
            // Java中获取：List<Map<String, String>>
            List<Map<String, String>> fields = config.getFormFields();
            if (fields != null && !fields.isEmpty()) {
                for (Map<String, String> field : fields) {
                    String aaaValue = field.get("aaa");
                    String bbbValue = field.get("bbb");
                    // 处理字段值...
                }
            }
            
            // 示例2：处理混合类型对象数组
            // 数据库中存储：[{"id":1,"name":"test","active":true}]
            // Java中获取：List<Map<String, Object>>
            List<Map<String, Object>> items = config.getFormItems();
            if (items != null && !items.isEmpty()) {
                for (Map<String, Object> item : items) {
                    Integer id = (Integer) item.get("id");
                    String name = (String) item.get("name");
                    Boolean active = (Boolean) item.get("active");
                    // 处理对象值...
                }
            }
        }
    }

    /**
     * MyBatis XML配置示例
     */
    /*
    <!-- 查询 -->
    <select id="selectFormConfig" resultType="FormConfig">
        SELECT id, form_name, form_fields, form_items, custom_fields
        FROM form_config 
        WHERE id = #{id}
    </select>

    <!-- 插入 -->
    <insert id="insertFormConfig">
        INSERT INTO form_config (form_name, form_fields, form_items, custom_fields)
        VALUES (
            #{formName},
            #{formFields,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringObjectListTypeHandler},
            #{formItems,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbObjectListTypeHandler},
            #{customFields,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbObjectArrayExample$FormFieldListTypeHandler}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateFormFields">
        UPDATE form_config 
        SET form_fields = #{formFields,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringObjectListTypeHandler}
        WHERE id = #{id}
    </update>
    */

    /**
     * 数据库表结构示例
     */
    /*
    CREATE TABLE form_config (
        id BIGSERIAL PRIMARY KEY,
        form_name VARCHAR(100) NOT NULL,
        form_fields JSONB,
        form_items JSONB,
        custom_fields JSONB
    );
    */

    /**
     * 测试数据示例
     */
    /*
    INSERT INTO form_config (form_name, form_fields, form_items, custom_fields) VALUES (
        'User Registration Form',
        '[{"aaa":""},{"bbb":"default_value"},{"fieldName":"username","fieldValue":""}]',
        '[{"id":1,"name":"username","type":"text","required":true},{"id":2,"name":"email","type":"email","required":true}]',
        '[{"fieldName":"username","fieldType":"text","fieldValue":"","required":true,"placeholder":"Enter username"}]'
    );
    */

    /**
     * 常见使用场景说明
     */
    /*
    1. 表单字段配置：[{"fieldName":"username","fieldValue":""},{"fieldName":"email","fieldValue":""}]
    2. 动态属性列表：[{"propertyName":"color","propertyValue":"red"},{"propertyName":"size","propertyValue":"large"}]
    3. 配置项数组：[{"configKey":"theme","configValue":"dark"},{"configKey":"language","configValue":"zh-CN"}]
    4. 简单对象列表：[{"aaa":""},{"aaa":""}] - 您提到的这种类型
    5. 复杂对象数组：[{"id":1,"name":"item1","metadata":{"type":"A","priority":1}}]
    */
}
