package com.lanshan.base.commonservice.schooldata.whut.converter;


import java.util.List;

import com.lanshan.base.commonservice.schooldata.whut.entity.WhutMasterCourse;
import com.lanshan.base.commonservice.schooldata.whut.vo.WhutMasterCourseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 研究生课表信息(WhutMasterCourse)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface WhutMasterCourseConverter {

    WhutMasterCourseConverter INSTANCE = Mappers.getMapper(WhutMasterCourseConverter.class);

    WhutMasterCourseVO toVO(WhutMasterCourse entity);

    WhutMasterCourse toEntity(WhutMasterCourseVO vo);
    
    List<WhutMasterCourseVO> toVO(List<WhutMasterCourse> entityList);

    List<WhutMasterCourse> toEntity(List<WhutMasterCourseVO> voList);
}


