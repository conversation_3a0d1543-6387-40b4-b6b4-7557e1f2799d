package com.lanshan.base.commonservice.standardapp.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.qo.StdRewardQO;
import com.lanshan.base.commonservice.standardapp.service.StdRewardService;
import com.lanshan.base.commonservice.standardapp.vo.StdRewardVO;
import com.lanshan.base.commonservice.standardapp.vo.StdRewardYearMonthVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 酬金查询表(StdReward)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("stdReward")
@Api(tags = "酬金查询表(StdReward)控制层", hidden = true)
public class StdRewardController {
    /**
     * 服务对象
     */
    @Resource
    private StdRewardService stdRewardService;

    /**
     * 分页查询所有数据
     *
     * @param rewardQO 查询实体
     * @return 分页数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<StdRewardVO>> page(StdRewardQO rewardQO) {
        return Result.build(this.stdRewardService.pageByParam(rewardQO));
    }

    @ApiOperation("获取年月列表")
    @GetMapping("/getYearMonthList")
    public Result<List<StdRewardYearMonthVO>> getYearMonthList() {
        return Result.build(this.stdRewardService.getYearMonthList());
    }

}

