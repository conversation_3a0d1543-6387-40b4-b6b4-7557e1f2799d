package com.lanshan.base.commonservice.task.batch.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.commonservice.task.batch.entity.BatchJobExec;
import com.lanshan.base.commonservice.task.batch.service.BatchJobExecService;
import com.lanshan.base.commonservice.task.batch.converter.BatchJobExecConverter;
import com.lanshan.base.commonservice.task.batch.vo.BatchJobExecVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 批量任务执行信息(BatchJobExec)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("batchJobExec")
@Api(tags = "批量任务执行信息(BatchJobExec)控制层", hidden = true)
public class BatchJobExecController {
    /**
     * 服务对象
     */
    @Resource
    private BatchJobExecService batchJobExecService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<BatchJobExecVO>> selectAll(Page<BatchJobExecVO> page, BatchJobExecVO vo) {
        QueryWrapper<BatchJobExec> queryWrapper = new QueryWrapper<>(BatchJobExecConverter.INSTANCE.toEntity(vo));
        IPage<BatchJobExec> pageData = this.batchJobExecService.page(page.convert(BatchJobExecConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(BatchJobExecConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<BatchJobExecVO> selectOne(@PathVariable Serializable id) {
        return Result.build(BatchJobExecConverter.INSTANCE.toVO(this.batchJobExecService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> insert(@RequestBody BatchJobExecVO vo) {
        return Result.build(this.batchJobExecService.save(BatchJobExecConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody BatchJobExecVO vo) {
        return Result.build(this.batchJobExecService.updateById(BatchJobExecConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.batchJobExecService.removeByIds(idList));
    }
}

