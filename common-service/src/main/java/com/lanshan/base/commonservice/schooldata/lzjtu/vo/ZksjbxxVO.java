package com.lanshan.base.commonservice.schooldata.lzjtu.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * 专科生信息(Zksjbxx)表实体VO类
 */
@ApiModel(value = "专科生信息VO")
@Data
public class ZksjbxxVO implements Serializable{

    @ApiModelProperty(value = "学号")
    private String xh;

    @ApiModelProperty(value = "姓名")
    private String xm;

    @ApiModelProperty(value = "性别")
    private String xbmc;

    @ApiModelProperty(value = "院系/单位代码")
    private String yxdm;

    @ApiModelProperty(value = "院系/单位")
    private String xymc;

    @ApiModelProperty(value = "专业代码")
    private String zydm;

    @ApiModelProperty(value = "专业名称")
    private String zymc;

    @ApiModelProperty(value = "班级代码")
    private String bjdm;

    @ApiModelProperty(value = "班级名称")
    private String bjmc;

    @ApiModelProperty(value = "现在年级")
    private String xznj;

    @ApiModelProperty(value = "学籍状态名称")
    private String xjztmc;

    @ApiModelProperty(value = "学生类别")
    private String xslb;

    @ApiModelProperty(value = "数据来源")
    private String sjly;
}

