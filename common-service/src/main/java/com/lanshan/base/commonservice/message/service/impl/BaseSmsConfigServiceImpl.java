package com.lanshan.base.commonservice.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.message.dao.BaseSmsConfigDao;
import com.lanshan.base.commonservice.message.entity.BaseSmsConfig;
import com.lanshan.base.commonservice.message.service.BaseSmsConfigService;
import org.springframework.stereotype.Service;

/**
 * 短信发送配置表(BaseSmsConfig)表服务实现类
 *
 * <AUTHOR>
 */
@Service("baseSmsConfigService")
public class BaseSmsConfigServiceImpl extends ServiceImpl<BaseSmsConfigDao, BaseSmsConfig> implements BaseSmsConfigService {

}

