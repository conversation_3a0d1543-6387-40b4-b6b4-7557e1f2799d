package com.lanshan.base.commonservice.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("角色菜单")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysRoleMenuDTO implements Serializable {

    private static final long serialVersionUID = 7304833819597679420L;
    @ApiModelProperty(value = "角色ID", required = true)
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @ApiModelProperty(value = "菜单ID列表", required = true)
    @NotNull(message = "菜单ID列表不能为空")
    private List<Long> menuIds;
}
