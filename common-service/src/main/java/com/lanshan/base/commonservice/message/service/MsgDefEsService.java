package com.lanshan.base.commonservice.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.dto.app.AppDTO;
import com.lanshan.base.api.dto.common.IdDTO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.message.entity.MsgDefIndex;
import com.lanshan.base.commonservice.message.qo.MsgDefPageQo;
import com.lanshan.base.commonservice.message.qo.MsgSaveQo;
import com.lanshan.base.commonservice.message.qo.MsgSingleReadQo;
import com.lanshan.base.commonservice.message.qo.ReSendMsgQo;
import com.lanshan.base.commonservice.message.vo.MsgAppVo;
import com.lanshan.base.commonservice.message.vo.MsgDefVo;
import me.chanjar.weixin.common.error.WxErrorException;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 消息定义ES服务接口
 */
public interface MsgDefEsService{


    /**
     * 分页查询消息定义
     * @param pageQo 分页查询参数
     * @return 消息定义列表
     */
    IPage<MsgDefVo> pageMsgDef(MsgDefPageQo pageQo);

    /**
     * 新增消息
     * @param qo 新增消息参数
     */
    void saveMsg(MsgSaveQo qo);

    /**
     * 撤回消息
     * @param idDTO id
     */
    void withdraw(IdDTO idDTO) throws WxErrorException;

    /**
     * 设置单条消息已读
     * @param idDTO id
     */
    void setSingleRead(MsgSingleReadQo idDTO);

    /**
     * 设置全部消息已读
     */
    void setAllRead();

    /**
     * 发送消息
     * @param msgDef 消息定义
     * @param userInfoList 用户信息
     * @param publishChannel 发送渠道
     */
    Map<String, Integer> sendMsg(MsgDefIndex msgDef, Set<UserInfoVo> userInfoList, List<String> publishChannel);

    /**
     * 查询消息中心所有关联应用
     * @return
     */
    List<MsgAppVo> listAllApp();

    /**
     * 定时发送消息
     */
    void timedSendMsg();

    /**
     * 重新发送消息
     * @param qo
     */
    void resendMsg(ReSendMsgQo qo);

    /**
     * 根据appId获取应用详情
     * @param appid
     * @return
     */
    AppDTO getAppInfoById(Long appid);

    /**
     * 根据id查询消息定义
     * @param id
     * @return
     */
    MsgDefVo getMsgDefById(Long id);

    /**
     * 创建消息相关索引
     */
    void createMsgIndex();

    /**
     * 批量更新消息定义
     */
    void batchUpdateMsgDef(List<MsgDefIndex> msgDefIndexList);
}
