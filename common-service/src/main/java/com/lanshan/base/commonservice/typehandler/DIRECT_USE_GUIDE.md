# 可直接使用的JSONB类型处理器列表

## 概述

以下所有类型处理器都可以直接在`@TableField`注解或MyBatis XML中使用，无需额外配置。

## 可直接使用的类型处理器

### 1. 字符串类型

```java
// 类名：JsonbStringTypeHandler
// 用途：获取JSONB字段的原始JSON字符串内容
@TableField(typeHandler = JsonbStringTypeHandler.class)
private String profileJson;
```

### 2. 字符串列表类型

```java
// 类名：JsonbStringListTypeHandler  
// 用途：处理字符串数组，如 ["tag1", "tag2", "tag3"]
@TableField(typeHandler = JsonbStringListTypeHandler.class)
private List<String> tags;
```

### 3. 整数列表类型

```java
// 类名：JsonbIntegerListTypeHandler
// 用途：处理整数数组，如 [1, 2, 3]
@TableField(typeHandler = JsonbIntegerListTypeHandler.class)
private List<Integer> categoryIds;
```

### 4. 长整数列表类型

```java
// 类名：JsonbLongListTypeHandler
// 用途：处理长整数数组，如 [1001, 1002, 1003] 或 ["1001", "1002", "1003"]
@TableField(typeHandler = JsonbLongListTypeHandler.class)
private List<Long> joinInUser;
```

### 5. 混合类型列表

```java
// 类名：JsonbListTypeHandler
// 用途：处理混合类型数组，如 ["string", 123, true]
@TableField(typeHandler = JsonbListTypeHandler.class)
private List<Object> mixedItems;
```

### 6. 混合类型对象

```java
// 类名：JsonbMapTypeHandler
// 用途：处理混合类型JSON对象，如 {"name":"John", "age":30, "active":true}
@TableField(typeHandler = JsonbMapTypeHandler.class)
private Map<String, Object> settings;
```

### 7. 字符串键值对

```java
// 类名：JsonbStringMapTypeHandler
// 用途：处理纯字符串键值对，如 {"key1":"value1", "key2":"value2"}
@TableField(typeHandler = JsonbStringMapTypeHandler.class)
private Map<String, String> labels;
```

### 8. 对象数组（混合类型）

```java
// 类名：JsonbObjectListTypeHandler
// 用途：处理对象数组，如 [{"id":1,"name":"test","active":true},{"id":2,"name":"demo"}]
@TableField(typeHandler = JsonbObjectListTypeHandler.class)
private List<Map<String, Object>> objectList;
```

### 9. 字符串对象数组

```java
// 类名：JsonbStringObjectListTypeHandler
// 用途：处理字符串对象数组，如 [{"aaa":""},{"aaa":""}] 或 [{"key1":"value1"},{"key2":"value2"}]
@TableField(typeHandler = JsonbStringObjectListTypeHandler.class)
private List<Map<String, String>> formFields;
```

## 在XML中使用

```xml
<!-- 插入示例 -->
<insert id="insertUser">
    INSERT INTO user_info (
        profile_json, tags, category_ids, join_in_user, settings, labels
    ) VALUES (
        #{profileJson,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringTypeHandler},
        #{tags,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringListTypeHandler},
        #{categoryIds,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbIntegerListTypeHandler},
        #{joinInUser,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbLongListTypeHandler},
        #{settings,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbMapTypeHandler},
        #{labels,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringMapTypeHandler}
    )
</insert>

<!-- 更新示例 -->
<update id="updateUserTags">
    UPDATE user_info 
    SET tags = #{tags,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringListTypeHandler}
    WHERE id = #{id}
</update>
```

## 完整实体类示例

```java
@Data
@TableName("user_info")
public class UserInfo {
    
    private Long id;
    private String username;
    
    // 原始JSON字符串
    @TableField(typeHandler = JsonbStringTypeHandler.class)
    private String profileJson;
    
    // 字符串标签列表：["developer", "java", "spring"]
    @TableField(typeHandler = JsonbStringListTypeHandler.class)
    private List<String> tags;
    
    // 整数分类ID列表：[1, 2, 3]
    @TableField(typeHandler = JsonbIntegerListTypeHandler.class)
    private List<Integer> categoryIds;
    
    // 长整数用户ID列表：[1001, 1002, 1003] 或 ["1001", "1002", "1003"]
    @TableField(typeHandler = JsonbLongListTypeHandler.class)
    private List<Long> joinInUser;
    
    // 混合类型配置：{"theme":"dark", "count":5, "enabled":true}
    @TableField(typeHandler = JsonbMapTypeHandler.class)
    private Map<String, Object> settings;
    
    // 字符串标签映射：{"department":"IT", "level":"senior"}
    @TableField(typeHandler = JsonbStringMapTypeHandler.class)
    private Map<String, String> labels;
}
```

## 数据库表结构

```sql
CREATE TABLE user_info (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    profile_json JSONB,
    tags JSONB,
    category_ids JSONB,
    join_in_user JSONB,
    settings JSONB,
    labels JSONB
);
```

## 测试数据

```sql
INSERT INTO user_info (username, profile_json, tags, category_ids, join_in_user, settings, labels) VALUES (
    'john_doe',
    '{"name":"John Doe","email":"<EMAIL>","age":30}',
    '["developer", "java", "spring", "postgresql"]',
    '[1, 2, 5, 8]',
    '[1001, 1002, 1003]',
    '{"theme":"dark", "language":"zh-CN", "notifications":true, "maxItems":100}',
    '{"department":"IT", "level":"senior", "location":"Beijing"}'
);
```

## 特殊说明

### joinInUser字段处理

根据您的需求，`joinInUser`字段可能存储为字符串数组格式`["1","2","3"]`，使用`JsonbLongListTypeHandler`可以自动将字符串转换为Long类型：

```java
// 数据库中：["1001", "1002", "1003"]
// Java中：List<Long> [1001L, 1002L, 1003L]
@TableField(typeHandler = JsonbLongListTypeHandler.class)
private List<Long> joinInUser;
```

如果您希望保持字符串格式，可以使用：

```java
// 数据库中：["1001", "1002", "1003"]  
// Java中：List<String> ["1001", "1002", "1003"]
@TableField(typeHandler = JsonbStringListTypeHandler.class)
private List<String> joinInUser;
```

## 优势

1. **即插即用**：所有类型处理器都有无参构造函数，可直接在注解中使用
2. **类型安全**：每个处理器都针对特定的Java类型优化
3. **自动转换**：支持数据库JSONB与Java对象的自动双向转换
4. **异常处理**：内置完善的错误处理和日志记录
5. **性能优化**：支持FastJSON2和Jackson双重保障

现在您可以直接复制这些类名在您的项目中使用了！
