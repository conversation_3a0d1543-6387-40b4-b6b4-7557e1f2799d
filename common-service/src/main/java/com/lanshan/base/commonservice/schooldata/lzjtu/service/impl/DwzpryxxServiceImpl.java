package com.lanshan.base.commonservice.schooldata.lzjtu.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.commonservice.schooldata.lzjtu.dao.DwzpryxxDao;
import com.lanshan.base.commonservice.schooldata.lzjtu.entity.Dwzpryxx;
import com.lanshan.base.commonservice.schooldata.lzjtu.properties.LzjtuOpenApiProperties;
import com.lanshan.base.commonservice.schooldata.lzjtu.service.DwzpryxxService;
import demo.DSDemo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工状态信息表(Dwzpryxx)表服务实现类
 */
@Service
@Slf4j
public class DwzpryxxServiceImpl extends ServiceImpl<DwzpryxxDao, Dwzpryxx> implements DwzpryxxService {

    @Resource
    private LzjtuOpenApiProperties lzjtuOpenApiProperties;

    @Resource
    private DwzpryxxDao dwzpryxxDao;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncDwzpryxx() {
        String key = lzjtuOpenApiProperties.getKey();
        String secret = lzjtuOpenApiProperties.getSecret();
        String url = lzjtuOpenApiProperties.getBaseUrl() + lzjtuOpenApiProperties.getDwzpryxx();

        try {
            String result = DSDemo.getData(url, key, secret);
            String value = JacksonUtils.toObj(result).get("value").toString();

            List<Dwzpryxx> entityList = JacksonUtils.toObj(value, new TypeReference<>() {
            });

            if (CollUtil.isNotEmpty(entityList)) {
                //清空数据
                dwzpryxxDao.truncate();
                //新增数据
                super.saveBatch(entityList);

                log.info("同步单位自聘职工信息{}条", entityList.size());
            }
        } catch (Exception e) {
            log.error("同步单位自聘职工信息失败", e);
        }
    }
}

