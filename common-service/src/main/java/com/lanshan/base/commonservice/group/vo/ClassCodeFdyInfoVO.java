package com.lanshan.base.commonservice.group.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2024/5/24 19:50
 */
@Data
@ApiModel(value = "班级代码-辅导员信息VO")
public class ClassCodeFdyInfoVO {

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "学院编码")
    private String instituteCode;

    @ApiModelProperty(value = "专业编码")
    private String majorCode;

    @ApiModelProperty(value = "班级编码")
    private String classCode;

    @ApiModelProperty(value = "辅导员工号")
    private String canCreateGh;

    @ApiModelProperty(value = "辅导员姓名")
    private String canCreateName;
}
