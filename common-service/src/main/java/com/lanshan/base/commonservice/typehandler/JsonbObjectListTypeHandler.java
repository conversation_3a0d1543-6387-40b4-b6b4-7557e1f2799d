package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;
import java.util.Map;

/**
 * JSONB到List<Map<String, Object>>的类型处理器
 * 
 * 专门用于处理数据库JSONB字段到Java List<Map<String, Object>>类型的转换
 * 
 * 使用场景：
 * 1. 数据库中存储对象数组，如：[{"aaa":""},{"aaa":""}]
 * 2. 数据库中存储复杂对象数组，如：[{"id":1,"name":"test"},{"id":2,"name":"demo"}]
 * 3. 需要将JSON对象数组转换为Java List<Map<String, Object>>对象
 * 4. 适用于动态结构的对象列表
 * 
 * 使用方式：
 * @TableField(typeHandler = JsonbObjectListTypeHandler.class)
 * private List<Map<String, Object>> objectList;
 * 
 * 或在XML中：
 * #{objectList,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbObjectListTypeHandler}
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({List.class})
public class JsonbObjectListTypeHandler extends UniversalJsonbTypeHandler<List<Map<String, Object>>> {
    
    /**
     * 默认构造函数，处理List<Map<String, Object>>类型
     */
    public JsonbObjectListTypeHandler() {
        super(new TypeReference<List<Map<String, Object>>>() {});
    }
}
