package com.lanshan.base.commonservice.addressbook.converter;


import com.lanshan.base.commonservice.addressbook.entity.UserInfoNewStuGuide;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoNewStuGuideVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 新生指南表(UserInfoNewStuGuide)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface UserInfoNewStuGuideConverter {

    UserInfoNewStuGuideConverter INSTANCE = Mappers.getMapper(UserInfoNewStuGuideConverter.class);

    UserInfoNewStuGuideVO toVO(UserInfoNewStuGuide entity);

    UserInfoNewStuGuide toEntity(UserInfoNewStuGuideVO vo);

    List<UserInfoNewStuGuideVO> toVO(List<UserInfoNewStuGuide> entityList);

    List<UserInfoNewStuGuide> toEntity(List<UserInfoNewStuGuideVO> voList);
}


