package com.lanshan.base.commonservice.addressbook.listener;

import com.lanshan.base.commonservice.addressbook.service.DepartmentService;
import com.lanshan.base.commonservice.addressbook.service.TagService;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class AddressBookCallbackListener {

	@Resource
	private UserService userService;

	@Resource
	private DepartmentService departmentService;

	@Resource
	private TagService tagService;

	/**
	 * 处理通讯录回调消息
	 * @param message 企微消息
	 */
	@RabbitListener(queues = "${common.mq.callback.address-book.queue}")
	public void addressBookCallbackHandler(@Payload WxCpXmlMessage message) {
		try {
			String changeType = message.getChangeType();
			switch (message.getChangeType()) {
				//创建成员
				case WxCpConsts.ContactChangeType.CREATE_USER:
					//更新成员
				case WxCpConsts.ContactChangeType.UPDATE_USER:
					userService.saveUserByMsg(message);
					break;

				//删除成员
				case WxCpConsts.ContactChangeType.DELETE_USER:
					userService.delUserByMsg(message);
					break;

				//创建部门
				case WxCpConsts.ContactChangeType.CREATE_PARTY:
					//更新部门
				case WxCpConsts.ContactChangeType.UPDATE_PARTY:
					departmentService.saveDeptByMsg(message);
					break;

				//删除部门
				case WxCpConsts.ContactChangeType.DELETE_PARTY:
					departmentService.delByByMsg(message);
					break;

				//更新标签成员
				case WxCpConsts.ContactChangeType.UPDATE_TAG:
					tagService.updateTagUsersByMsg(message);
					break;
				default:
					log.info("未知的变更类型：{}", changeType);
			}
		} catch (Exception e) {
			log.error("处理通讯录回调异常", e);
		}
	}
}
