package com.lanshan.base.commonservice.todo.qo;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("待办更新流程入参")
public class TodoUpdateFlowInfoQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("流水号")
    private String serialNo;

    @ApiModelProperty("提交人id")
    private String submitterId;

    @ApiModelProperty("提交人名称")
    private String submitterName;

    @ApiModelProperty("流程状态")
    private Integer flowStatus;

    @ApiModelProperty("待办名称")
    private String name;

    @ApiModelProperty("待办描述")
    private String description;

    @ApiModelProperty("提交信息")
    private ObjectNode submitInfo;

    @ApiModelProperty("流程节点")
    private ArrayNode flowNodeList;

    @ApiModelProperty("流程类型")
    private String type;
}

