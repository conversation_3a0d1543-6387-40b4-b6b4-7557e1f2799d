package com.lanshan.base.commonservice.excel.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EasyExcel自适应大小处理器（同时支持列宽和行高）
 * 
 * 功能：
 * 1. 自动调整列宽
 * 2. 自动调整行高
 * 3. 支持中文字符优化
 * 4. 支持自动换行
 * 5. 支持自定义配置
 * 
 * 使用方式：
 * EasyExcel.write(outputStream, ExportDTO.class)
 *     .registerWriteHandler(new AutoSizeHandler())
 *     .sheet("数据")
 *     .doWrite(data);
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
public class AutoSizeHandler implements SheetWriteHandler, RowWriteHandler {

    /**
     * 列宽配置
     */
    private final ColumnWidthConfig columnWidthConfig;
    
    /**
     * 行高配置
     */
    private final RowHeightConfig rowHeightConfig;
    
    /**
     * 缓存每列的最大宽度
     */
    private final Map<Integer, Integer> maxColumnWidthMap = new HashMap<>();

    /**
     * 默认构造函数
     */
    public AutoSizeHandler() {
        this.columnWidthConfig = new ColumnWidthConfig();
        this.rowHeightConfig = new RowHeightConfig();
    }

    /**
     * 自定义构造函数
     * 
     * @param columnWidthConfig 列宽配置
     * @param rowHeightConfig 行高配置
     */
    public AutoSizeHandler(ColumnWidthConfig columnWidthConfig, RowHeightConfig rowHeightConfig) {
        this.columnWidthConfig = columnWidthConfig;
        this.rowHeightConfig = rowHeightConfig;
    }

    // ==================== SheetWriteHandler 方法 ====================

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 设置工作表的默认行高
        Sheet sheet = writeSheetHolder.getSheet();
        sheet.setDefaultRowHeightInPoints(rowHeightConfig.getDefaultRowHeight());
    }

    @Override
    public void afterCellDataConverted(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                                     List<WriteCellData<?>> cellDataList, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 计算并缓存每列的最大宽度
        int columnIndex = head.getColumnIndex();
        
        for (WriteCellData<?> cellData : cellDataList) {
            if (cellData.getStringValue() != null) {
                int length = calculateStringWidth(cellData.getStringValue());
                Integer maxWidth = maxColumnWidthMap.get(columnIndex);
                if (maxWidth == null || length > maxWidth) {
                    maxColumnWidthMap.put(columnIndex, Math.min(length, columnWidthConfig.getMaxColumnWidth()));
                }
            }
        }
    }

    @Override
    public void afterCellDispose(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                                List<WriteCellData<?>> cellDataList, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 调整列宽
        Sheet sheet = writeSheetHolder.getSheet();
        int columnIndex = head.getColumnIndex();
        
        Integer maxWidth = maxColumnWidthMap.get(columnIndex);
        if (maxWidth != null) {
            int finalWidth = Math.max(columnWidthConfig.getMinColumnWidth(), 
                                    Math.min(maxWidth, columnWidthConfig.getMaxColumnWidth()));
            sheet.setColumnWidth(columnIndex, finalWidth * 256);
        }
    }

    // ==================== RowWriteHandler 方法 ====================

    @Override
    public void beforeRowCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                               Integer rowIndex, Integer relativeRowIndex, Boolean isHead) {
        // 行创建前不需要特殊处理
    }

    @Override
    public void afterRowCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                              Row row, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            row.setHeightInPoints(rowHeightConfig.getHeaderRowHeight());
        } else {
            row.setHeightInPoints(rowHeightConfig.getDefaultRowHeight());
        }
    }

    @Override
    public void afterRowDispose(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder,
                               Row row, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            return;
        }

        float maxHeight = calculateRowHeight(row);
        float finalHeight = Math.max(rowHeightConfig.getMinRowHeight(), 
                                   Math.min(maxHeight, rowHeightConfig.getMaxRowHeight()));
        row.setHeightInPoints(finalHeight);
    }

    // ==================== 私有方法 ====================

    /**
     * 计算字符串显示宽度
     */
    private int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }
        
        if (!columnWidthConfig.isEnableChineseOptimization()) {
            return str.length();
        }
        
        int width = 0;
        for (char c : str.toCharArray()) {
            if (isChineseCharacter(c)) {
                width += 2;
            } else {
                width += 1;
            }
        }
        
        return width;
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChineseCharacter(char c) {
        return (c >= 0x4E00 && c <= 0x9FFF) ||
               (c >= 0x3400 && c <= 0x4DBF) ||
               (c >= 0xFF00 && c <= 0xFFEF);
    }

    /**
     * 计算行高
     */
    private float calculateRowHeight(Row row) {
        float maxHeight = rowHeightConfig.getDefaultRowHeight();
        
        for (int i = 0; i < row.getLastCellNum(); i++) {
            if (row.getCell(i) != null) {
                String cellValue = getCellValueAsString(row.getCell(i));
                if (cellValue != null && !cellValue.isEmpty()) {
                    float cellHeight = calculateCellHeight(cellValue);
                    maxHeight = Math.max(maxHeight, cellHeight);
                }
            }
        }
        
        return maxHeight;
    }

    /**
     * 获取单元格值的字符串表示
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 计算单元格内容所需的高度
     */
    private float calculateCellHeight(String content) {
        if (content == null || content.isEmpty()) {
            return rowHeightConfig.getDefaultRowHeight();
        }
        
        int lineCount = 1;
        if (rowHeightConfig.isEnableAutoWrap()) {
            lineCount = content.split("\n").length;
            
            if (lineCount == 1 && content.length() > 20) {
                lineCount = (content.length() / 20) + 1;
            }
        }
        
        return Math.max(rowHeightConfig.getDefaultRowHeight(), lineCount * rowHeightConfig.getHeightPerLine());
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建默认配置的处理器
     */
    public static AutoSizeHandler create() {
        return new AutoSizeHandler();
    }

    /**
     * 创建自定义配置的处理器
     */
    public static AutoSizeHandler create(ColumnWidthConfig columnConfig, RowHeightConfig rowConfig) {
        return new AutoSizeHandler(columnConfig, rowConfig);
    }

    // ==================== 配置类 ====================

    /**
     * 列宽配置类
     */
    public static class ColumnWidthConfig {
        private int maxColumnWidth = 50;
        private int minColumnWidth = 8;
        private boolean enableChineseOptimization = true;

        public ColumnWidthConfig() {}

        public ColumnWidthConfig(int maxColumnWidth, int minColumnWidth, boolean enableChineseOptimization) {
            this.maxColumnWidth = maxColumnWidth;
            this.minColumnWidth = minColumnWidth;
            this.enableChineseOptimization = enableChineseOptimization;
        }

        // Getters and Setters
        public int getMaxColumnWidth() { return maxColumnWidth; }
        public void setMaxColumnWidth(int maxColumnWidth) { this.maxColumnWidth = maxColumnWidth; }
        public int getMinColumnWidth() { return minColumnWidth; }
        public void setMinColumnWidth(int minColumnWidth) { this.minColumnWidth = minColumnWidth; }
        public boolean isEnableChineseOptimization() { return enableChineseOptimization; }
        public void setEnableChineseOptimization(boolean enableChineseOptimization) { this.enableChineseOptimization = enableChineseOptimization; }
    }

    /**
     * 行高配置类
     */
    public static class RowHeightConfig {
        private float maxRowHeight = 100f;
        private float minRowHeight = 12f;
        private float defaultRowHeight = 15f;
        private float headerRowHeight = 20f;
        private float heightPerLine = 15f;
        private boolean enableAutoWrap = true;

        public RowHeightConfig() {}

        public RowHeightConfig(float maxRowHeight, float minRowHeight, float defaultRowHeight, 
                              float headerRowHeight, float heightPerLine, boolean enableAutoWrap) {
            this.maxRowHeight = maxRowHeight;
            this.minRowHeight = minRowHeight;
            this.defaultRowHeight = defaultRowHeight;
            this.headerRowHeight = headerRowHeight;
            this.heightPerLine = heightPerLine;
            this.enableAutoWrap = enableAutoWrap;
        }

        // Getters and Setters
        public float getMaxRowHeight() { return maxRowHeight; }
        public void setMaxRowHeight(float maxRowHeight) { this.maxRowHeight = maxRowHeight; }
        public float getMinRowHeight() { return minRowHeight; }
        public void setMinRowHeight(float minRowHeight) { this.minRowHeight = minRowHeight; }
        public float getDefaultRowHeight() { return defaultRowHeight; }
        public void setDefaultRowHeight(float defaultRowHeight) { this.defaultRowHeight = defaultRowHeight; }
        public float getHeaderRowHeight() { return headerRowHeight; }
        public void setHeaderRowHeight(float headerRowHeight) { this.headerRowHeight = headerRowHeight; }
        public float getHeightPerLine() { return heightPerLine; }
        public void setHeightPerLine(float heightPerLine) { this.heightPerLine = heightPerLine; }
        public boolean isEnableAutoWrap() { return enableAutoWrap; }
        public void setEnableAutoWrap(boolean enableAutoWrap) { this.enableAutoWrap = enableAutoWrap; }
    }
}
