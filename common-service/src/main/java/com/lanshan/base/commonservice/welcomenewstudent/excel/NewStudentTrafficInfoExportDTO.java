package com.lanshan.base.commonservice.welcomenewstudent.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 新生交通信息导出DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel("新生交通信息导出对象")
public class NewStudentTrafficInfoExportDTO {

    @ExcelProperty(value = "姓名", index = 0)
    private String name;

    @ExcelProperty(value = "性别", index = 1)
    private String gender;

    @ExcelProperty(value = "学工号", index = 2)
    private String xh;

    @ExcelProperty(value = "证件号码", index = 3)
    private String idCardNum;

    @ExcelProperty(value = "手机号", index = 4)
    private String phoneNo;

    @ExcelProperty(value = "学院名称", index = 5)
    private String collegeName;

    @ExcelProperty(value = "专业名称", index = 6)
    private String majorName;

    @ExcelProperty(value = "班级名称", index = 7)
    private String className;

    @ExcelProperty(value = "预计到校时间", index = 8)
    private String expectedArrivalDate;

    @ExcelProperty(value = "交通方式", index = 9)
    private String transportationType;

    @ExcelProperty(value = "车次/航班号", index = 10)
    private String vehicleNumber;

    @ExcelProperty(value = "到站车站", index = 11)
    private String arrivalStation;

    @ExcelProperty(value = "其他到站车站", index = 12)
    private String arrivalStationOther;

    @ExcelProperty(value = "随行人员人数", index = 13)
    private Integer accompanyingPersonsCount;
} 