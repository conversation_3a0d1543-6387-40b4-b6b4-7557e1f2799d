package com.lanshan.base.commonservice.standarddata.dto;

import com.lanshan.base.api.qo.PageQo;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class StdUserPageDTO extends PageQo {
    private static final long serialVersionUID = 3812278745272501132L;

    //部门id
    private Long departmentId;

    //账号
    private String userid;

    //姓名
    private String name;

    //手机哈
    private String mobile;


    private String deptIdPath;


    private String deptName;
}
