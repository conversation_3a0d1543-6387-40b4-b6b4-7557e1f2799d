package com.lanshan.base.commonservice.group.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.service.GroupChatScopeService;
import com.lanshan.base.commonservice.group.converter.GroupChatScopeConverter;
import com.lanshan.base.commonservice.group.vo.GroupChatScopeVO;
import com.lanshan.base.commonservice.log.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 群聊范围表(GroupChatScope)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("groupChatScope")
@Api(tags = "群聊范围表(GroupChatScope)控制层", hidden = true)
public class GroupChatScopeController {
    /**
     * 服务对象
     */
    @Resource
    private GroupChatScopeService groupChatScopeService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<GroupChatScopeVO>> selectAll(Page<GroupChatScopeVO> page, GroupChatScopeVO vo) {
        QueryWrapper<GroupChatScope> queryWrapper = new QueryWrapper<>(GroupChatScopeConverter.INSTANCE.toEntity(vo));
        IPage<GroupChatScope> pageData = this.groupChatScopeService.page(page.convert(GroupChatScopeConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(GroupChatScopeConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<GroupChatScopeVO> selectOne(@PathVariable Serializable id) {
        return Result.build(GroupChatScopeConverter.INSTANCE.toVO(this.groupChatScopeService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    @OperateLog("新增数据")
    public Result<Boolean> insert(@RequestBody GroupChatScopeVO vo) {
        return Result.build(this.groupChatScopeService.save(GroupChatScopeConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    @OperateLog("修改数据")
    public Result<Boolean> update(@RequestBody GroupChatScopeVO vo) {
        return Result.build(this.groupChatScopeService.updateById(GroupChatScopeConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    @OperateLog("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.groupChatScopeService.removeByIds(idList));
    }
}

