package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 学生奖助金发放查询表(StdGrantIssue)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class StdGrantIssue extends Model<StdGrantIssue> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 工号
     */
    private String userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 发放单位
     */
    private String issueUnit;
    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 金额
     */
    private String amount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 发放等级
     */
    private String issueLevel;
    /**
     * 财务审核
     */
    private String financeAudit;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

