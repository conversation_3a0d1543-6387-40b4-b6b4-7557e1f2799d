package com.lanshan.base.commonservice.system.controller;


import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.dto.AjaxResult;
import com.lanshan.base.api.dto.system.RouterVo;
import com.lanshan.base.api.dto.system.SysMenuVo;
import com.lanshan.base.api.dto.system.TreeSelect;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityUtils;
import com.lanshan.base.commonservice.system.converter.SysMenuConverter;
import com.lanshan.base.commonservice.system.entity.SysMenu;
import com.lanshan.base.commonservice.system.service.ISysMenuService;
import com.lanshan.base.starter.log.annotation.Log;
import com.lanshan.base.starter.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 菜单信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/menu")
@Api(tags = "系统菜单管理控制器", hidden = true)
public class SysMenuController extends BaseController {
    @Resource
    private ISysMenuService sysMenuService;

    @ApiOperation("获取菜单列表")
    @RequiresPermissions("system:menu:list")
    @PostMapping("/list")
    public Result<List<SysMenuVo>> list(@RequestBody SysMenuVo menuVo) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        List<SysMenu> menus = sysMenuService.selectMenuList(SysMenuConverter.INSTANCE.toEntity(menuVo), userId);
        return Result.build(menus.stream().map(SysMenuConverter.INSTANCE::toVO).collect(Collectors.toList()));
    }

    @ApiOperation("根据菜单ID获取详细信息")
    @RequiresPermissions("system:menu:query")
    @GetMapping(value = "/{menuId}")
    public Result<SysMenuVo> getInfo(@PathVariable Long menuId) {
        return Result.build(SysMenuConverter.INSTANCE.toVO(sysMenuService.selectMenuById(menuId)));
    }

    @ApiOperation("获取菜单下拉树列表")
    @GetMapping("/treeselect")
    public Result<List<TreeSelect>> treeselect(SysMenuVo menuVo) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        List<SysMenu> menus = sysMenuService.selectMenuList(SysMenuConverter.INSTANCE.toEntity(menuVo), userId);
        return Result.build(sysMenuService.buildMenuTreeSelect(menus));
    }

    @ApiOperation("加载对应角色菜单列表树")
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        List<SysMenu> menus = sysMenuService.selectMenuList(userId);
        Map<String, Object> map = new HashMap<>(4);
        map.put("checkedKeys", sysMenuService.selectMenuListByRoleId(roleId));
        map.put("menus", sysMenuService.buildMenuVOTreeSelect(menus));
        return AjaxResult.success(map);
    }

    @ApiOperation("新增菜单")
    @RequiresPermissions("system:menu:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public Result<Integer> add(@Validated @RequestBody SysMenuVo menuVo) {
        SysMenu menu = SysMenuConverter.INSTANCE.toEntity(menuVo);
        if (!sysMenuService.checkMenuNameUnique(menu)) {
            return Result.build("新增菜单'" + menu.getName() + "'失败，菜单名称已存在", -1);
        }
        menu.setCreateBy(SecurityUtils.getUsername());
        return Result.build(sysMenuService.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    @ApiOperation("修改菜单")
    @RequiresPermissions("system:menu:edit")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/put")
    public AjaxResult edit(@Validated @RequestBody SysMenuVo menuVo) {
        SysMenu menu = SysMenuConverter.INSTANCE.toEntity(menuVo);
        if (!sysMenuService.checkMenuNameUnique(menu)) {
            return error("修改菜单'" + menu.getName() + "'失败，菜单名称已存在");
        } else if (menu.getId().equals(menu.getParentId())) {
            return error("修改菜单'" + menu.getName() + "'失败，上级菜单不能选择自己");
        }
        menu.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(sysMenuService.updateMenu(menu));
    }

    @ApiOperation("删除菜单")
    @RequiresPermissions("system:menu:remove")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @PostMapping("/{menuId}/delete")
    public AjaxResult remove(@PathVariable("menuId") Long menuId) {
        if (sysMenuService.hasChildByMenuId(menuId)) {
            return warn("存在子菜单,不允许删除");
        }
        if (sysMenuService.checkMenuExistRole(menuId)) {
            return warn("菜单已分配,不允许删除");
        }
        return toAjax(sysMenuService.deleteMenuById(menuId));
    }

    @ApiOperation("获取路由信息")
    @GetMapping("getRouters")
    public Result<List<RouterVo>> getRouters() {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        List<SysMenuVo> menus = sysMenuService.selectMenuTreeByUserId(userId);
        return Result.build(sysMenuService.buildMenuVos(menus));
    }

    @ApiOperation("获取路由信息")
    @GetMapping("getNewRouters")
    public Result<List<SysMenuVo>> getNewRouters() {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        return Result.build(sysMenuService.selectMenuTreeByUserId(userId));
    }
}