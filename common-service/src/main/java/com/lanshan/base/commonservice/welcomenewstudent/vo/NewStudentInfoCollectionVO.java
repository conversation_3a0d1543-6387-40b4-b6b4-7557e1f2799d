package com.lanshan.base.commonservice.welcomenewstudent.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新生基本信息采集表视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("新生基本信息采集表视图对象")
public class NewStudentInfoCollectionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("新生基本信息表ID")
    private Long newStudentDataId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("政治面貌")
    private String politicalStatus;

    @ApiModelProperty("民族")
    private String nationality;

    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate;

    @ApiModelProperty("身份证号码")
    private String idCardNumber;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("常住地址")
    private String permanentAddress;

    @ApiModelProperty("家长联系方式信息，包含多个联系人的详细信息")
    private Object parentContactInfo;

    @ApiModelProperty("籍贯")
    private String nativePlace;

    @ApiModelProperty("户口所在地")
    private String householdAddress;

    @ApiModelProperty("学生类别")
    private String studentCategory;

    @ApiModelProperty("是否有意愿参加意外伤害保险")
    private Boolean accidentInsuranceFlag;

    @ApiModelProperty("身高")
    private String height;

    @ApiModelProperty("体重")
    private String weight;

    @ApiModelProperty("鞋码")
    private String shoeSize;

    @ApiModelProperty("日常用品多个用逗号分割")
    private String dailyNecessities;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

} 