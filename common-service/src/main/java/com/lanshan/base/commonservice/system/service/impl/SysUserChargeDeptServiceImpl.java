package com.lanshan.base.commonservice.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.system.entity.SysUserChargeDept;
import com.lanshan.base.commonservice.system.mapper.SysUserChargeDeptMapper;
import com.lanshan.base.commonservice.system.service.SysUserChargeDeptService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统用户通讯录权限关联表(SysUserAddressDept)表服务实现类
 *
 * <AUTHOR>
 */
@Service("sysUSerChargeDeptService")
public class SysUserChargeDeptServiceImpl extends ServiceImpl<SysUserChargeDeptMapper, SysUserChargeDept> implements SysUserChargeDeptService {

    @Override
    public void deleteUserChargeDeptByUsernames(List<String> usernames) {
        this.remove(Wrappers.lambdaQuery(SysUserChargeDept.class)
                .in(SysUserChargeDept::getUserid, usernames));
    }
}

