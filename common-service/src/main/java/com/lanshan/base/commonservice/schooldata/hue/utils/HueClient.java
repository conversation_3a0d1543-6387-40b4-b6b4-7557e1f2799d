package com.lanshan.base.commonservice.schooldata.hue.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.TypeReference;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.hue.entity.StdBookBorrowRecord;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2025/5/19 11:37
 */
@Slf4j
@RefreshScope
@Component
@RequiredArgsConstructor
public class HueClient {

    private final HueConfigProperties hueConfigProperties;

    private static final Map<String, Map<String, Long>> TOKEN_CACHE = new ConcurrentHashMap<>(4);

    /**
     * 获取 token
     *
     * @return token
     */
    public String getAccessToken() {
        synchronized (TOKEN_CACHE) {
            if (TOKEN_CACHE.containsKey("token")) {
                Map<String, Long> token = TOKEN_CACHE.get("token");
                Set<Map.Entry<String, Long>> entries = token.entrySet();
                Map.Entry<String, Long> next = entries.iterator().next();
                String accessToken = next.getKey();
                Long expiredTime = next.getValue();
                if (expiredTime > System.currentTimeMillis()) {
                    return accessToken;
                }
            }
        }
        Map<String, Object> params = Map.of("key", hueConfigProperties.getKey(),
                "secret", hueConfigProperties.getSecret());
        log.info("请求湖北二师获取 token 接口");
        String respStr = HttpUtil.get(hueConfigProperties.getBaseUri() + hueConfigProperties.getAccessTokenUri(), params);
        log.info("请求湖北二师获取 token 响应结果：{}", respStr);
        JSONObject jsonObject = JSON.parseObject(respStr);
        Integer code = jsonObject.getInteger("code");
        if (!Objects.equals(hueConfigProperties.getSuccessCode(), code)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请求湖北二师获取 Token 接口失败！失败原因：" + jsonObject.getString("message"));
        }
        JSONObject result = jsonObject.getJSONObject("result");
        String accessToken = result.getString("access_token");
        Long expiresIn = result.getLong("expires_in");
        TOKEN_CACHE.put("token", Map.of(accessToken, System.currentTimeMillis() + expiresIn * 1000));
        return accessToken;
    }

    /**
     * 获取借阅记录
     *
     * @param page    页码
     * @param perPage 每页条数
     * @param maxPage 总页数，用于首次请求初始化
     * @return 借阅记录列表和最大页数的包装对象
     */
    public BookBorrowRecordResult bookBorrowRecord(Integer page, Integer perPage, Integer maxPage) {
        Map<String, Object> params = Map.of("order", Map.of("ID", "asc"), "JSRQ", Map.of("gte", "2021-01-0100:00:00"), "page", page, "per_page", perPage);
        log.info("请求湖北二师获取借阅记录接口，页码：{}", page);
        MultiValueMap<String, String> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.add("access_token", getAccessToken());
        String url = UriComponentsBuilder.fromUriString(hueConfigProperties.getBaseUri() + hueConfigProperties.getBookBorrowRecordUri())
                .queryParams(paramsMap).build().toUriString();
        String respStr = HttpUtil.post(url, JSON.toJSONString(params));
        log.debug("请求湖北二师获取借阅记录接口响应结果：{}", respStr);
        HueCommonRespResult<List<StdBookBorrowRecord>> respResult = JSON.parseObject(respStr, new TypeReference<HueCommonRespResult<List<StdBookBorrowRecord>>>() {
        }, JSONReader.Feature.TrimString, JSONReader.Feature.SupportSmartMatch);
        if (!Objects.equals(hueConfigProperties.getSuccessCode(), respResult.getCode())) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请求湖北二师获取借阅记录接口失败！失败原因：" + respResult.getMessage());
        }

        BookBorrowRecordResult result = new BookBorrowRecordResult();
        result.setRecords(respResult.getResult().getData());
        result.setMaxPage(respResult.getResult().getMaxPage());
        return result;
    }

    /**
     * @param gh
     * @param year 202512
     * @return
     */
    public Object queryNzjx(String gh, String year) {
        Map<String, Object> params = Map.of("GH", gh, "FGZNY", year);
        MultiValueMap<String, String> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.add("access_token", getAccessToken());
        String url = UriComponentsBuilder.fromUriString(hueConfigProperties.getBaseUri() + hueConfigProperties.getNzjxUri())
                .queryParams(paramsMap).build().toUriString();
        log.info("请求湖北二师查询年终绩效接口，地址：{}，参数：{}", url, params);
        String respStr = HttpUtil.post(url, JSON.toJSONString(params));
        log.info("请求湖北二师查询年终绩效接口响应结果：{}", respStr);

        JSONObject jsonObject = JSON.parseObject(respStr);
        Integer code = jsonObject.getInteger("code");
        if (!Objects.equals(hueConfigProperties.getSuccessCode(), code)) {
            log.info("请求湖北二师查询年终绩效接口失败！失败原因：{}", jsonObject.getString("message"));
            return null;
        }
        // 直接返回result部分给前端
        return jsonObject.get("result");
    }

    /**
     * 借阅记录结果包装类
     */
    @Data
    public static class BookBorrowRecordResult {

        private List<StdBookBorrowRecord> records;

        private Integer maxPage;
    }

}
