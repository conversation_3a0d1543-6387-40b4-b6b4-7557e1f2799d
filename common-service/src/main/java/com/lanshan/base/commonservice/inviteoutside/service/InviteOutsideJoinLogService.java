package com.lanshan.base.commonservice.inviteoutside.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.inviteoutside.entity.InviteOutsideJoinLog;
import com.lanshan.base.commonservice.inviteoutside.pojo.InviteSetting;
import com.lanshan.base.commonservice.inviteoutside.pojo.qo.InviteOutsidePageQO;
import com.lanshan.base.commonservice.inviteoutside.pojo.vo.InviteOutsideJoinLogVO;

/**
 * 邀请外部人员加入企微记录(InviteOutsideJoinLog)表服务接口
 *
 * <AUTHOR>
 */
public interface InviteOutsideJoinLogService extends IService<InviteOutsideJoinLog> {
    /**
     * 获取邀请设置
     *
     * @return 邀请设置
     */
    InviteSetting getInviteDeptList();

    /**
     * 提交
     *
     * @param inviteOutsideJoinLogVO 提交信息
     * @return true 成功 false 失败
     */
    Boolean submit(InviteOutsideJoinLogVO inviteOutsideJoinLogVO) throws Exception;

    /**
     * 审核
     *
     * @param submitId    提交唯一标识
     * @param auditStatus 审核状态
     * @return true 成功 false 失败
     */
    Boolean audit(Long submitId, Integer auditStatus) throws Exception;

    /**
     * 分页查询
     *
     * @param qo 查询对象
     * @return 分页数据
     */
    IPage<InviteOutsideJoinLogVO> pageByParam(InviteOutsidePageQO qo);

}

