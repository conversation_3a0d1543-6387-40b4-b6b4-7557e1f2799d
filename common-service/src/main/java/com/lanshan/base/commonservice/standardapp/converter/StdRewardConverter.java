package com.lanshan.base.commonservice.standardapp.converter;


import com.lanshan.base.commonservice.standardapp.entity.StdReward;
import com.lanshan.base.commonservice.standardapp.vo.StdRewardVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 酬金查询表(StdReward)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface StdRewardConverter {

    StdRewardConverter INSTANCE = Mappers.getMapper(StdRewardConverter.class);

    StdRewardVO toVO(StdReward entity);

    StdReward toEntity(StdRewardVO vo);

    List<StdRewardVO> toVO(List<StdReward> entityList);

    List<StdReward> toEntity(List<StdRewardVO> voList);
}


