package com.lanshan.base.commonservice.todo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TodoCompleteFlowNodeDto implements Serializable {
    private static final long serialVersionUID = -49473812991539941L;

    @ApiModelProperty("节点id")
    private Long nodeId;

    @ApiModelProperty("节点用户列表")
    @NotEmpty(message = "节点用户列表不能为空")
    private List<NodeUserInfo> userInfoList;

    @Data
    public static class NodeUserInfo implements Serializable {
        @ApiModelProperty("用户id")
        private String userid;

        @ApiModelProperty("操作类型")
        private Integer operateStatus;

        @ApiModelProperty("完成时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date completeTime;
    }
}

