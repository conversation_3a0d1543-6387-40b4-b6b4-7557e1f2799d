# 对象数组类型快速指南

## 针对 `[{"aaa":""},{"aaa":""}]` 这种类型

### 🎯 直接使用方案

```java
// 方案1：使用JsonbStringObjectListTypeHandler（推荐）
@TableField(typeHandler = JsonbStringObjectListTypeHandler.class)
private List<Map<String, String>> objectArray;

// 方案2：使用JsonbObjectListTypeHandler（如果包含非字符串类型）
@TableField(typeHandler = JsonbObjectListTypeHandler.class)
private List<Map<String, Object>> objectArray;
```

### 📋 使用示例

#### 实体类定义
```java
@Data
@TableName("your_table")
public class YourEntity {
    
    private Long id;
    
    // 处理 [{"aaa":""},{"aaa":""}] 类型
    @TableField(typeHandler = JsonbStringObjectListTypeHandler.class)
    private List<Map<String, String>> objectArray;
}
```

#### 数据访问
```java
// 获取数据
YourEntity entity = yourMapper.selectById(1L);
List<Map<String, String>> array = entity.getObjectArray();

// 遍历处理
if (array != null) {
    for (Map<String, String> obj : array) {
        String aaaValue = obj.get("aaa");  // 获取aaa字段的值
        // 处理其他字段...
        for (Map.Entry<String, String> entry : obj.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            System.out.println(key + " = " + value);
        }
    }
}

// 设置数据
List<Map<String, String>> newArray = new ArrayList<>();
Map<String, String> obj1 = new HashMap<>();
obj1.put("aaa", "");
obj1.put("bbb", "value1");

Map<String, String> obj2 = new HashMap<>();
obj2.put("aaa", "");
obj2.put("ccc", "value2");

newArray.add(obj1);
newArray.add(obj2);

entity.setObjectArray(newArray);
yourMapper.updateById(entity);
```

### 🗄️ 数据库表结构

```sql
CREATE TABLE your_table (
    id BIGSERIAL PRIMARY KEY,
    object_array JSONB
);
```

### 📝 测试数据

```sql
-- 插入您提到的数据格式
INSERT INTO your_table (object_array) VALUES 
('[{"aaa":""},{"aaa":""}]');

-- 插入更复杂的数据
INSERT INTO your_table (object_array) VALUES 
('[{"aaa":"","bbb":"value1"},{"aaa":"","ccc":"value2"},{"fieldName":"username","fieldValue":"john"}]');
```

### 🔧 MyBatis XML配置（可选）

```xml
<!-- 如果不使用@TableField注解，可以在XML中指定 -->
<insert id="insertEntity">
    INSERT INTO your_table (object_array) VALUES (
        #{objectArray,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringObjectListTypeHandler}
    )
</insert>

<update id="updateObjectArray">
    UPDATE your_table 
    SET object_array = #{objectArray,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringObjectListTypeHandler}
    WHERE id = #{id}
</update>

<select id="selectByObjectField" resultType="YourEntity">
    SELECT * FROM your_table 
    WHERE object_array @> #{searchValue,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringTypeHandler}::jsonb
</select>
```

### 🚀 常见使用场景

1. **表单字段配置**
   ```json
   [{"fieldName":"username","fieldValue":""},{"fieldName":"email","fieldValue":""}]
   ```

2. **动态属性列表**
   ```json
   [{"propertyName":"color","propertyValue":"red"},{"propertyName":"size","propertyValue":"large"}]
   ```

3. **简单对象数组**（您的场景）
   ```json
   [{"aaa":""},{"aaa":""}]
   ```

4. **配置项数组**
   ```json
   [{"configKey":"theme","configValue":"dark"},{"configKey":"language","configValue":"zh-CN"}]
   ```

### ⚡ 性能提示

1. **索引优化**：如果需要查询对象数组中的特定字段，可以创建GIN索引
   ```sql
   CREATE INDEX idx_object_array_gin ON your_table USING GIN (object_array);
   ```

2. **查询优化**：使用PostgreSQL的JSONB操作符进行高效查询
   ```sql
   -- 查询包含特定键值对的记录
   SELECT * FROM your_table WHERE object_array @> '[{"aaa":""}]';
   
   -- 查询对象数组中包含特定键的记录
   SELECT * FROM your_table WHERE object_array @> '[{"fieldName":"username"}]';
   ```

### 🔍 调试技巧

1. **查看原始JSON**：使用JsonbStringTypeHandler获取原始JSON字符串
   ```java
   @TableField(typeHandler = JsonbStringTypeHandler.class)
   private String objectArrayRaw;  // 用于调试
   ```

2. **日志输出**：UniversalJsonbTypeHandler会自动记录转换过程中的异常

3. **类型验证**：在代码中添加类型检查
   ```java
   if (objectArray != null) {
       for (Map<String, String> obj : objectArray) {
           if (obj.containsKey("aaa")) {
               String aaaValue = obj.get("aaa");
               // 处理aaa字段
           }
       }
   }
   ```

### ✅ 总结

对于 `[{"aaa":""},{"aaa":""}]` 这种类型，直接使用：

```java
@TableField(typeHandler = JsonbStringObjectListTypeHandler.class)
private List<Map<String, String>> objectArray;
```

这样就可以完美处理您的数据格式了！
