package com.lanshan.base.commonservice.schooldata.hue.service.impl;

import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.schooldata.hue.service.TgxcwzxrynzjxService;
import com.lanshan.base.commonservice.schooldata.hue.utils.HueClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service("tgxcwzxrynzjxService")
public class TgxcwzxrynzjxServiceImpl implements TgxcwzxrynzjxService {

    private final HueClient hueClient;

    @Override
    public Object queryNzjx(String year) {
        String userId = SecurityContextHolder.getUserId();
        return hueClient.queryNzjx(userId, year);
    }
}
