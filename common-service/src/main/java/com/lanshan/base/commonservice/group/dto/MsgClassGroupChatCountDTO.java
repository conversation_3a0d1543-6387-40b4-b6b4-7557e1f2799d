package com.lanshan.base.commonservice.group.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "班级群聊人数DTO")
@Data
public class MsgClassGroupChatCountDTO implements Serializable{

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "学院编码")
    private String instituteCode;

    @ApiModelProperty(value = "专业编码")
    private String majorCode;

    @ApiModelProperty(value = "班级编码")
    private String classCode;

    @ApiModelProperty(value = "班级人数")
    private Integer classUserCount;
}

