package com.lanshan.base.commonservice.schooldata.whut.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutMaster;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 学生基本信息表(WhutMaster)表数据库访问层
 *
 * <AUTHOR>
 */
public interface WhutMasterDao extends BaseMapper<WhutMaster> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<WhutMaster> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WhutMaster> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<WhutMaster> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<WhutMaster> entities);

    /**
     * 清空表
     */
    @Update("truncate table school_data.whut_master")
    void truncate();
}

