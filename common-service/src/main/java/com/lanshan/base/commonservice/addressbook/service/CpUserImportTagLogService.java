package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.entity.CpUserImportTagLog;
import com.lanshan.base.commonservice.addressbook.qo.CpUserImportTagLogQO;
import com.lanshan.base.commonservice.addressbook.vo.CpUserImportResutlVO;
import com.lanshan.base.commonservice.addressbook.vo.CpUserImportTagLogVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 企业微信用户导入标签日志服务接口
 */
public interface CpUserImportTagLogService extends IService<CpUserImportTagLog> {

    /**
     * 分页查询
     *
     * @param qo   查询对象
     * @return 分页结果
     */
    IPage<CpUserImportTagLogVO> page(CpUserImportTagLogQO qo);

    /**
     * 新增
     *
     * @param qo 查询对象
     * @return 是否成功
     */
    boolean add(CpUserImportTagLogQO qo);

    /**
     * 修改
     *
     * @param qo 查询对象
     * @return 是否成功
     */
    boolean update(CpUserImportTagLogQO qo);

    /**
     * 获取详情
     *
     * @param id ID
     * @return 详情
     */
    CpUserImportTagLogVO getDetail(Long id);

    /**
     * 导入标签
     *
     * @param file 文件
     *             createNewTag 是否创建新的标签
     */
    CpUserImportResutlVO importTag(MultipartFile file, boolean createNewTag);

    void generateImportTemplate(HttpServletResponse response);
}