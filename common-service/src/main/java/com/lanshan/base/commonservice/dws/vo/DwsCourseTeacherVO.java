package com.lanshan.base.commonservice.dws.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * 教师排课(DwsCourseTeacher)表实体VO类
 */
@ApiModel(value = "教师排课VO")
@Data
public class DwsCourseTeacherVO implements Serializable{

    @ApiModelProperty(value = "教学班代码")
    private String jxbdm;

    @ApiModelProperty(value = "教学班名称")
    private String jxbmc;

    @ApiModelProperty(value = "授课教师工号")
    private String skjsgh;

    @ApiModelProperty(value = "授课教师姓名")
    private String skjsxm;

    @ApiModelProperty(value = "学年学期")
    private String xnxq;

    @ApiModelProperty(value = "课程编码")
    private String kcbm;

    @ApiModelProperty(value = "课程名称")
    private String kcmc;
}

