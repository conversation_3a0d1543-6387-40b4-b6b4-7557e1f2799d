package com.lanshan.base.commonservice.todo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "待办自动完成dto")
public class TodoAutoCompleteDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "待办用户关联id")
    private Long id;

    @ApiModelProperty(value = "待办结束时间")
    private Date endTime;
}
