package com.lanshan.base.commonservice.schooldata.whut.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 本科生基础信息表(WhutUndergraduate)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WhutUndergraduate extends Model<WhutUndergraduate> {
    /**
     * 自增主键ID
     */
    private Long id;
    /**
     * 企业微信用户id
     */
    private String qywxUserId;
    /**
     * 学号
     */
    private String xh;
    /**
     * 考生号
     */
    private String ksh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 外文姓名
     */
    private String wwxm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 曾用名
     */
    private String cym;
    /**
     * 性别码
     */
    private String xbm;
    /**
     * 性别码代码名称
     */
    private String xbmdmmc;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 出生地
     */
    private String csd;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 民族
     */
    private String mz;
    /**
     * 国籍/地区码
     */
    private String gjdqm;
    /**
     * 身份证件类型
     */
    private String sfzjlx;
    /**
     * 身份证件号
     */
    private String sfzjh;
    /**
     * 身份证件有效期
     */
    private String sfzjyxq;
    /**
     * 婚姻状况码
     */
    private String hyzkm;
    /**
     * 港澳台侨外码
     */
    private String gatqwm;
    /**
     * 政治面貌
     */
    private String zzmm;
    /**
     * 健康状况
     */
    private String jkzk;
    /**
     * 信仰宗教码
     */
    private String xyzjm;
    /**
     * 血型码
     */
    private String xxm;
    /**
     * 是否独生子女
     */
    private String sfdszn;
    /**
     * 来源地区
     */
    private String lydq;
    /**
     * 学生来源
     */
    private String xsly;
    /**
     * 户口性质
     */
    private String hkxz;
    /**
     * 户口所在地
     */
    private String hkszd;
    /**
     * 邮政编码
     */
    private String yzbm;
    /**
     * 通讯地址
     */
    private String txdz;
    /**
     * 联系电话
     */
    private String lxdh;
    /**
     * 电子邮箱
     */
    private String dzyx;
    /**
     * 即时通讯号
     */
    private String jstxh;
    /**
     * 校园卡号
     */
    private String yktzh;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

