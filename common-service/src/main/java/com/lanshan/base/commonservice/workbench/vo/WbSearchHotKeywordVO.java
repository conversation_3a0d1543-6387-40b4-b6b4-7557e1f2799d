package com.lanshan.base.commonservice.workbench.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 热门搜索关键词(WbSearchHotKeyword)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "热门搜索关键词VO")
@Data
@ToString
public class WbSearchHotKeywordVO implements Serializable {

    private static final long serialVersionUID = 5217653606225941214L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "搜索关键词")
    private String keyword;

    @ApiModelProperty(value = "搜索次数")
    private Long searchTimes;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;
}

