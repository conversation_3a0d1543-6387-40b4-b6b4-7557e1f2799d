package com.lanshan.base.commonservice.identify.controller;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.annotation.ApiCallLog;
import com.lanshan.base.commonservice.access.enums.AppTypeEnum;
import com.lanshan.base.commonservice.identify.dto.ChangeCurrentIdentifyDto;
import com.lanshan.base.commonservice.identify.dto.CurrentIdentifyDto;
import com.lanshan.base.commonservice.identify.dto.IDentifyNumDto;
import com.lanshan.base.commonservice.identify.dto.IdentifyListDto;
import com.lanshan.base.commonservice.identify.entity.StaffEntity;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;
import com.lanshan.base.commonservice.identify.mapper.StaffMapper;
import com.lanshan.base.commonservice.identify.service.IdentifyApiService;
import com.lanshan.base.commonservice.identify.service.StaffService;
import com.lanshan.base.commonservice.identify.service.mq.producer.RabbitProducer;
import com.lanshan.base.starter.rabbitmq.service.EnhancedMqMessageSender;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = ServiceConstant.COMMON_IDENTIFY_API)
@Api(tags = "身份对外接口", hidden = true)
@Slf4j
public class IdentifyApiController {

    @Value("${identifyworkbench.agentId:}")
    private String agentId;
    @Value("${identifyworkbench.url-pre:}")
    private String urlPrefix;
    @Value("${identifyworkbench.corpId:}")
    private String corpId;


    @Autowired
    private IdentifyApiService identifyApiService;
    @Autowired
    private StaffService staffService;

    @Autowired
    private RabbitProducer producer;
    @Autowired
    private StaffMapper staffMapper;

    @Autowired
    private EnhancedMqMessageSender enhancedMqMessageSender;

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "获取ls_userid")
    @GetMapping(value = "getLsUserId")
    public Result<Object> getLsUserId(@Validated IDentifyNumDto iDentifyNumDto) {
        try {
            iDentifyNumDto.verify();
            String lanshanUserId = identifyApiService.getLsUserId(iDentifyNumDto);
            Map data = new HashMap<>();
            data.put("lanshanUserId", lanshanUserId);
            return Result.build(data);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(10).setMsg(e.getMessage());
        }
    }

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "获取用户身份列表")
    @GetMapping(value = "getIdentifyList")
    public Result<Object> getIdentifyList(@Validated IdentifyListDto identifyListDto) {
        try {
            if (StringUtils.isEmpty(identifyListDto.getLanshanUserId())) {
                throw new ServiceException("请求参数不能为空", 21);
            }
            Map resultMap = new HashMap<String, Object>();
            resultMap.put("lanshanUserId", identifyListDto.getLanshanUserId());
            resultMap.put("identifyList", identifyApiService.getIdentifyList(identifyListDto));
            return Result.build(resultMap);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(20).setMsg(e.getMessage());
        }
    }

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "获取当前身份")
    @GetMapping(value = "getCurrentIdentify")
    public Result<Object> getCurrentIdentify(@Validated CurrentIdentifyDto currentIdentifyDto) {
        try {
            if (StringUtils.isEmpty(currentIdentifyDto.getLanshanUserId())) {
                throw new ServiceException("请求参数不能为空", 31);
            }
            Map resultMap = new HashMap<String, Object>();
            StaffIdentifyRelation identifyRelation = identifyApiService.getCurrentIdentify(currentIdentifyDto);
            StaffEntity staffEntity = staffMapper.selectByStaffId(identifyRelation.getStaffId());
            resultMap.put("lanshanUserId", currentIdentifyDto.getLanshanUserId());
            resultMap.put("currentIdentify", identifyRelation);
            resultMap.put("staffName", staffEntity==null?"":staffEntity.getStaffName());
            return Result.build(resultMap);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(30).setMsg(e.getMessage());
        }
    }

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "更换当前身份")
    @PostMapping(value = "changeCurrentIdentify")
    public Result<Object> changeCurrentIdentify(@Validated ChangeCurrentIdentifyDto changeCurrentIdentifyDto) {
        try {
            if (StringUtils.isEmpty(changeCurrentIdentifyDto.getLanshanUserId())) {
                throw new ServiceException("览山用户id不能为空", 41);
            }
            if (changeCurrentIdentifyDto.getIdentifyId() == null) {
                throw new ServiceException("新身份id不能为空", 42);
            }
            identifyApiService.changeCurrentIdentify(changeCurrentIdentifyDto);
            return Result.build("成功", 0);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(40).setMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "发送消息")
    @PostMapping(value = "sendTest")
    public Result<Boolean> sendMsg() {
        producer.sendMessage("test:..........................");
        return Result.build(true);
    }
    @Resource
    private WxCpServiceFactory wxCpServiceFactory;
    @ApiOperation("设置与应用的webview展示模板")
    @PostMapping("setwebviewtemplate")
    public Result<String> setwebviewtemplate() {
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        try {
            WxCpAgentWorkBench dto = new WxCpAgentWorkBench();
            String url = urlPrefix+"/app-base/identityCard/index?scope=snsapi_base&corpId="+corpId+"&agentId="+agentId;
            dto.setUrl(url);
            dto.setJumpUrl(url);
            dto.setHeight("single_row");
            dto.setEnableWebviewClick(false);
            dto.setHideTitle(true);
            dto.setType("webview");
            dto.setAgentId(Long.parseLong(agentId));
            wxCpService.getWorkBenchService().setWorkBenchTemplate(dto);
        } catch (WxErrorException e) {
            log.error("设置用户身份码展示数据失败：{}", e.getMessage());
        }
        return Result.build("OK");
    }

    @ApiOperation(value = "测试新增强消息发送功能")
    @PostMapping(value = "sendEnhancedTest")
    public Result<Boolean> sendEnhancedMsg() {
        // 测试各种类型的消息发送
        Map<String, Object> testData = new HashMap<>();
        testData.put("message", "Enhanced MQ Test");
        testData.put("timestamp", System.currentTimeMillis());

        // 测试Direct交换机
        enhancedMqMessageSender.sendToDirectExchange("test.exchange", "test.routing.key", testData);

        // 测试优先级消息
        enhancedMqMessageSender.sendMessageWithPriority("test.exchange", "test.routing.key", testData, 5);

        return Result.build(true);
    }
    @ApiOperation("设置与应用的webview展示模板")
    @PostMapping("setwebviewteData")
    public Result<String> setwebviewteData(@RequestParam(name = "userId") String userId, @RequestParam(name = "staffName") String staffName, @RequestParam(name = "staffNoType") String staffNoType) {
        try {
            String url = urlPrefix+"/app-base/identityCard/index?scope=snsapi_base&corpId="+corpId+"&agentId="+agentId+"&userId="+userId+"&username="+staffName+"&staffNoType="+staffNoType;
            log.info("workbench url::::::::::::::::::::::::::::::::::::::{}",url);
            WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
            WxCpAgentWorkBench dto = new WxCpAgentWorkBench();
            dto.setUrl(url);
            dto.setJumpUrl(url);
            dto.setHeight("single_row");
            dto.setEnableWebviewClick(false);
            dto.setHideTitle(true);
            dto.setType("webview");
            dto.setAgentId(Long.parseLong(agentId));
            dto.setUserId(userId);
            wxCpService.getWorkBenchService().setWorkBenchData(dto);
        } catch (WxErrorException e) {
            log.error("设置用户身份码展示数据失败：{}", e.getMessage());
        }
        return Result.build("OK");
    }

}
