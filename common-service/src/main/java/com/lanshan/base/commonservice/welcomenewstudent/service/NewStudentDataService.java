package com.lanshan.base.commonservice.welcomenewstudent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentDataQO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.DashboardVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.ImportResultVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentDataVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 新生信息表(NewStudentData)表服务接口
 *
 * <AUTHOR>
 */
public interface NewStudentDataService extends IService<NewStudentData> {

    /**
     * 获取新生信息统计
     *
     * @return 新生信息统计
     */
    List<NewStudentStatVO> getNewStudentStat(String year);

    /**
     * 导入新生信息
     *
     * @param file 文件
     * @return 导入结果
     */
    ImportResultVO importData(MultipartFile file);

    /**
     * 获取导入新生信息错误数据
     *
     * @return 新生信息错误数据
     */
    List<NewStudentExportDTO> getImportNewStudentErrorData();

    /**
     * 发送欢迎信息
     */
    void sendWelcomeMsg(String decUserid);

    /**
     * 新生入群
     */
    void joinUserToWelcomeGroup();

    /**
     * 导出错误数据
     *
     * @param response 响应
     */
    void exportErrorData(HttpServletResponse response);

    /**
     * 分页查询
     *
     * @param qo 查询参数
     * @return 分页数据
     */
    IPage<NewStudentDataVO> pageByParam(NewStudentDataQO qo);

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    NewStudentDataVO getCurrentUserInfo(String userid);

    /**
     * 批量删除未报到数据
     *
     * @return 是否成功
     */
    Boolean batchDelNoReport();

    /**
     * 新生转在校生（变更学号）
     *
     * @return 是否成功
     */
    Boolean transferZxs();

    /**
     * 导出新生数据
     *
     * @param qo 查询参数
     * @return 新生数据
     */
    List<NewStudentDataVO> exportStudentData(NewStudentDataQO qo);

    /**
     * 添加用户入群
     *
     * @param newStudentDataList 新生数据
     * @param canSendUserIdSet   可发送用户id集合
     * @param agentId            应用id
     */
    void addUserJoinGroup(List<NewStudentData> newStudentDataList, Set<String> canSendUserIdSet, String agentId);

    /**
     * 获取数据大屏展示数据
     *
     * @param year 年份
     * @return 数据大屏展示数据
     */
    DashboardVO getDashboardData(String year);
}

