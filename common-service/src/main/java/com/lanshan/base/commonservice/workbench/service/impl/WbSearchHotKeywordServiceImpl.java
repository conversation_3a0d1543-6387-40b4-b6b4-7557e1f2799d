package com.lanshan.base.commonservice.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.workbench.dao.WbSearchHotKeywordDao;
import com.lanshan.base.commonservice.workbench.entity.WbSearchHotKeyword;
import com.lanshan.base.commonservice.workbench.service.WbSearchHotKeywordService;
import org.springframework.stereotype.Service;

/**
 * 热门搜索关键词(WbSearchHotKeyword)表服务实现类
 *
 * <AUTHOR>
 */
@Service("wbSearchHotKeywordService")
public class WbSearchHotKeywordServiceImpl extends ServiceImpl<WbSearchHotKeywordDao, WbSearchHotKeyword> implements WbSearchHotKeywordService {

}

