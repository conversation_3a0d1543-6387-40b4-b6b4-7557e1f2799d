package com.lanshan.base.commonservice.workbench.controller;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.app.AppSearchVO;
import com.lanshan.base.commonservice.workbench.service.AppSearchService;
import com.lanshan.base.commonservice.workbench.vo.AppTagVO;
import com.lanshan.base.commonservice.workbench.vo.AppZoneVO;
import com.lanshan.base.commonservice.workbench.vo.ZoneAndAppVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应用搜索控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("appSearch")
@Api(tags = "应用搜索控制器")
public class AppSearchController {

    @Resource
    private AppSearchService appSearchService;

    @GetMapping("/search")
    @ApiOperation("应用搜索")
    public Result<List<AppSearchVO>> search(@RequestParam String keyword) {
        return Result.build(appSearchService.search(keyword));
    }

    @GetMapping("/getRecentSearchWords")
    @ApiOperation("获取最近搜索的关键字")
    public Result<List<String>> getRecentSearchWords() {
        return Result.build(appSearchService.getRecentSearchWords());
    }

    @PostMapping("/removeRecentlySearchWords/delete")
    @ApiOperation("删除最新搜索的关键字")
    public Result<Boolean> deleteRecentSearchWords(@RequestParam(required = false) String keyword) {
        return Result.build(appSearchService.deleteRecentSearchWords(keyword));
    }

    @GetMapping("/getHotSearchWords")
    @ApiOperation("获取热门搜索的关键字")
    public Result<List<String>> getHotSearchWords() {
        return Result.build(appSearchService.getHotSearchWords());
    }

    @PostMapping("/initAppIndex")
    @ApiOperation("初始化应用索引")
    public Result<Boolean> initAppIndex() {
        return Result.build(appSearchService.initAppIndex());
    }

    @PostMapping("/addAppFavorite")
    @ApiOperation("添加应用收藏")
    public Result<Boolean> addFavoriteApp(@RequestParam Long appId) {
        return Result.build(appSearchService.addFavoriteApp(appId));
    }

    @PostMapping("/sortAppFavorite")
    @ApiOperation("设置应用收藏里的应用排序")
    public Result<Boolean> addFavoriteApp(@RequestBody List<Long> appIdSort) {
        return Result.build(appSearchService.sortFavoriteApp(appIdSort));
    }

    @PostMapping("/removeAppFavorite/delete")
    @ApiOperation("移除应用收藏")
    public Result<Boolean> removeFavoriteApp(@RequestBody List<Long> appIds) {
        return Result.build(appSearchService.removeFavoriteApp(appIds));
    }

    @GetMapping("/getFavoriteApps")
    @ApiOperation("获取收藏的应用")
    public Result<List<AppTagVO>> getFavoriteApps() {
        return Result.build(appSearchService.getFavoriteApps());
    }

    @PostMapping("recordAppUsed")
    @ApiOperation("记录应用使用")
    public Result<Boolean> recordAppUsed(@RequestParam Long appId) {
        return Result.build(appSearchService.recordAppUsed(appId));
    }

    @GetMapping("/getTopUsedApps")
    @ApiOperation("获取最近使用的应用")
    public Result<List<AppTagVO>> getRecentlyUsedApps() {
        return Result.build(appSearchService.getRecentlyUsedApps());
    }

    @GetMapping("/getUserAppZone")
    @ApiOperation("获取用户可见的应用专区")
    public Result<List<AppZoneVO>> getUserAppZone() {
        return Result.build(appSearchService.getUserAppZone());
    }

    @GetMapping("/getUserAppZoneApps")
    @ApiOperation("获取用户可见的应用专区应用")
    public Result<List<AppTagVO>> getUserAppZoneApps(@RequestParam Long zoneId) {
        return Result.build(appSearchService.getUserAppZoneApps(zoneId));
    }

    @GetMapping("/getAllZoneAndApp")
    @ApiOperation("获取用户可见的应用专区和所有应用")
    public Result<List<ZoneAndAppVO>> getAllZoneAndApps() {
        return Result.build(appSearchService.getAllZoneAndApps());
    }
}
