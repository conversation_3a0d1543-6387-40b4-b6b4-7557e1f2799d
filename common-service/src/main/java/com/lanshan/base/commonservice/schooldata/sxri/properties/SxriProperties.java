package com.lanshan.base.commonservice.schooldata.sxri.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 陕铁院配置属性类
 *
 * <AUTHOR>
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "sxri")
public class SxriProperties {

    /**
     * 班主任信息接口地址
     */
    private String bzrUrl;

    /**
     * 辅导员信息接口地址
     */
    private String fdyUrl;

    /**
     * 每页数据量
     */
    private Integer pageSize = 10;

    /**
     * 应用ID
     */
    private String applyId = "36701700450290688";

    /**
     * 密钥
     */
    private String secretKey = "02046fdec3fc4f5bbf0ccd0dc0209d18";

    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;

    /**
     * 重试次数
     */
    private Integer retryCount = 3;

    /**
     * 重试间隔（毫秒）
     */
    private Integer retryInterval = 1000;

    /**
     * 批量插入大小
     */
    private Integer batchSize = 500;
} 