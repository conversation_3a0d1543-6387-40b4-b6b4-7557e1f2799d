package com.lanshan.base.commonservice.common.handler.impl;

import com.lanshan.base.commonservice.common.handler.OpenApprovalChangeHandler;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.config.properties.GroupChatProperties;
import com.lanshan.base.commonservice.group.handler.GroupChatHandler;
import com.lanshan.base.commonservice.group.handler.GroupChatHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Component
@Service("groupChatOpenApproval")
public class GroupChatOpenApprovalChangeHandler implements OpenApprovalChangeHandler {

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private GroupChatHandlerFactory groupChatHandlerFactory;

    @Resource
    private GroupChatProperties groupChatProperties;

    @Override
    public void processOpenApprovalChange(WxCpXmlMessage message) {
        String agentId = message.getAgentId();

        //班级建群处理器
        if (agentId.equals(agentProperties.getClassGroupAgentId())) {
            //获取处理器
            GroupChatHandler handler = groupChatHandlerFactory.getHandler(groupChatProperties.getHandler());
            //处理审批状态变更事件
            handler.processOpenApprovalChange(message);

        } else {
            log.error("应用【{}】未配置处理器", agentId);
        }
    }
}
