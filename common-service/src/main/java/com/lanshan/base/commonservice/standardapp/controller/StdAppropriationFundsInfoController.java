package com.lanshan.base.commonservice.standardapp.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.dto.AppropriationFundsSearchDTO;
import com.lanshan.base.commonservice.standardapp.service.StdAppropriationFundsInfoService;
import com.lanshan.base.commonservice.standardapp.vo.StdAppropriationFundsInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 经费上帐信息(StdAppropriationFundsInfo)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("stdAppropriationFundsInfo")
@Api(tags = "经费上帐信息(StdAppropriationFundsInfo)控制层", hidden = true)
public class StdAppropriationFundsInfoController {
    /**
     * 服务对象
     */
    @Resource
    private StdAppropriationFundsInfoService stdAppropriationFundsInfoService;

    /**
     * 分页查询
     *
     * @param appropriationFundsInfoDTO 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<StdAppropriationFundsInfoVO>> page(AppropriationFundsSearchDTO appropriationFundsInfoDTO) {
        return Result.build(this.stdAppropriationFundsInfoService.pageByParam(appropriationFundsInfoDTO));
    }
}

