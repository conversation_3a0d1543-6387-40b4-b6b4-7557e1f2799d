package com.lanshan.base.commonservice.workbench.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用健康检查日志信息表(WbAppHealthCheckLog)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WbAppHealthCheckLog extends Model<WbAppHealthCheckLog> {
    private static final long serialVersionUID = -5663912518882057569L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 检查批次号
     */
    private Long checkBatchNo;
    /**
     * 请求返回状态码
     */
    private Integer responseStatus;
    /**
     * 健康状态。 1：正常；2：异常
     */
    private Integer healthStatus;
    /**
     * 检查时间
     */
    private Date checkTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

