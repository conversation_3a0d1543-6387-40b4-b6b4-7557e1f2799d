package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentTaskConverter;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentTaskDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTaskStatus;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentTaskQO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentTaskService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentTaskStatusService;
import com.lanshan.base.commonservice.welcomenewstudent.util.WxCpServiceUtil;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentTaskVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 新生任务表(NewStudentTask)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("newStudentTaskService")
public class NewStudentTaskServiceImpl extends ServiceImpl<NewStudentTaskDao, NewStudentTask> implements NewStudentTaskService {

    @Resource
    private NewStudentTaskStatusService newStudentTaskStatusService;

    @Resource
    private WxCpServiceUtil wxCpServiceUtil;

    @Resource
    private NewStudentDataService newStudentDataService;

    @Override
    public IPage<NewStudentTaskVO> pageByParam(NewStudentTaskQO qo) {
        Page<NewStudentTask> page = this.page(Page.<NewStudentTask>of(qo.getPage(), qo.getSize()), Wrappers.lambdaQuery(NewStudentTask.class)
                .like(StringUtils.isNotBlank(qo.getTaskName()), NewStudentTask::getName, qo.getTaskName())
                .orderByDesc(NewStudentTask::getSort, NewStudentTask::getUpdateDate)
        );
        return page.convert(NewStudentTaskConverter.INSTANCE::toVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeNewStudentTask(Long newStudentTaskId) {
        String userId = SecurityContextHolder.getUserId();

        //先检查任务是否已完成，避免重复操作
        NewStudentTaskStatus existingStatus = newStudentTaskStatusService.getOne(
                Wrappers.lambdaQuery(NewStudentTaskStatus.class)
                        .eq(NewStudentTaskStatus::getNewStuTaskId, newStudentTaskId)
                        .eq(NewStudentTaskStatus::getUserid, userId)
        );

        if (Objects.nonNull(existingStatus)) {
            log.info("任务已完成，用户：{}，任务ID：{}", userId, newStudentTaskId);
            return true;
        }

        // 创建新的任务状态记录
        NewStudentTaskStatus newStudentTaskStatus = new NewStudentTaskStatus();
        newStudentTaskStatus.setNewStuTaskId(newStudentTaskId);
        newStudentTaskStatus.setUserid(userId);

        boolean saveResult = newStudentTaskStatusService.save(newStudentTaskStatus);
        if (!saveResult) {
            log.error("保存任务状态失败，用户：{}，任务ID：{}", userId, newStudentTaskId);
            return false;
        }

        //异步检查是否完成所有任务并发送通知
        CompletableFuture.runAsync(() -> {
            try {
                checkAndNotifyTaskCompletion(userId);
            } catch (Exception e) {
                log.error("检查任务完成状态失败，用户：{}", userId, e);
            }
        });

        return true;
    }

    /**
     * 检查并通知任务完成
     *
     * @param userId 用户ID
     */
    private void checkAndNotifyTaskCompletion(String userId) {
        long totalTask = this.count(Wrappers.lambdaQuery(NewStudentTask.class)
                .eq(NewStudentTask::getTaskStatus, true));
        long completedTotal = newStudentTaskStatusService.count(
                Wrappers.lambdaQuery(NewStudentTaskStatus.class)
                        .eq(NewStudentTaskStatus::getUserid, userId));

        if (completedTotal >= totalTask) {
            doNoticeLastMsg(userId);
        }
    }

    /**
     * 通知最后一条消息
     *
     * @param userId 学工号
     */
    private void doNoticeLastMsg(String userId) {
        WxCpService welcomeWxCpService = wxCpServiceUtil.getWelcomeWxCpService();
        WxCpMessage message = WxCpMessage.TEXT()
                .content("新生任务已全部完成！")
                .toUser(userId)
                .build();
        try {
            welcomeWxCpService.getMessageService().send(message);
        } catch (WxErrorException e) {
            log.error("发送完成信息异常", e);
        }
    }

    @Override
    public List<Long> getAllCompletedNewStudentTask() {
        String userId = SecurityContextHolder.getUserId();
        return Optional.ofNullable(newStudentTaskStatusService.list(Wrappers.lambdaQuery(NewStudentTaskStatus.class)
                        .eq(NewStudentTaskStatus::getUserid, userId)))
                .orElseGet(Collections::emptyList)
                .stream()
                .map(NewStudentTaskStatus::getNewStuTaskId)
                .collect(Collectors.toList());
    }

    @Override
    public Integer getMaxOrder() {
        NewStudentTask newStudentTask = this.getOne(Wrappers.lambdaQuery(NewStudentTask.class).orderByDesc(NewStudentTask::getSort).last("limit 1"));
        if (Objects.isNull(newStudentTask)) {
            return 0;
        }
        return newStudentTask.getSort();
    }

    @Override
    public Boolean clockIn() {
        String userId = SecurityContextHolder.getUserId();
        return newStudentDataService.update(
                Wrappers.lambdaUpdate(NewStudentData.class)
                        .set(NewStudentData::getCheckInStatus, true)
                        .eq(NewStudentData::getUserid, userId)
        );
    }
}

