package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;
import java.util.Map;

/**
 * JSONB到List<Map<String, String>>的类型处理器
 * 
 * 专门用于处理数据库JSONB字段到Java List<Map<String, String>>类型的转换
 * 
 * 使用场景：
 * 1. 数据库中存储字符串对象数组，如：[{"aaa":""},{"aaa":""}]
 * 2. 数据库中存储简单键值对数组，如：[{"key1":"value1"},{"key2":"value2"}]
 * 3. 需要将JSON字符串对象数组转换为Java List<Map<String, String>>对象
 * 4. 适用于表单字段、配置项等纯字符串结构的对象列表
 * 
 * 使用方式：
 * @TableField(typeHandler = JsonbStringObjectListTypeHandler.class)
 * private List<Map<String, String>> formFields;
 * 
 * 或在XML中：
 * #{formFields,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringObjectListTypeHandler}
 * 
 * 数据库示例：
 * [{"aaa":""},{"aaa":""}]
 * [{"fieldName":"username","fieldValue":"john"},{"fieldName":"email","fieldValue":"<EMAIL>"}]
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({List.class})
public class JsonbStringObjectListTypeHandler extends UniversalJsonbTypeHandler<List<Map<String, String>>> {
    
    /**
     * 默认构造函数，处理List<Map<String, String>>类型
     */
    public JsonbStringObjectListTypeHandler() {
        super(new TypeReference<List<Map<String, String>>>() {});
    }
}
