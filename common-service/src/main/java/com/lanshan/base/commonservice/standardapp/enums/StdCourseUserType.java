package com.lanshan.base.commonservice.standardapp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 课程用户类型
 */
@Getter
@AllArgsConstructor
public enum StdCourseUserType {

    STUDENT("course.type.student", "学生", "学生"),
    TEACHER("course.type.teacher", "教师", "教师"),
    SUPERVISION("course.type.supervision", "督导", "督导"),
    ASSISTANT("course.type.assistant", "助教", "助教"),
    FREE("course.type.free", "空闲", "空闲"),
    OTHER("course.type.other", "其他", "其他");

    private final String configKey;

    private final String code;

    private final String desc;
}
