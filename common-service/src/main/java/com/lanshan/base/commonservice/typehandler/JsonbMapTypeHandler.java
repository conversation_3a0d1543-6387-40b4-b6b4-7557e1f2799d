package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.Map;

/**
 * JSONB到Map<String, Object>的类型处理器
 *
 * 专门用于处理数据库JSONB字段到Java Map<String, Object>类型的转换
 *
 * 使用场景：
 * 1. 数据库中存储JSON对象，如：{"key1":"value1","key2":123,"key3":true}
 * 2. 需要将JSON对象转换为Java Map<String, Object>对象
 * 3. 适用于配置信息、扩展属性等混合类型的键值对
 *
 * 使用方式：
 * @TableField(typeHandler = JsonbMapTypeHandler.class)
 * private Map<String, Object> properties;
 *
 * 或在XML中：
 * #{properties,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbMapTypeHandler}
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({Map.class})
public class JsonbMapTypeHandler extends UniversalJsonbTypeHandler<Map<String, Object>> {

    /**
     * 默认构造函数，处理Map<String, Object>类型
     */
    public JsonbMapTypeHandler() {
        super(new TypeReference<Map<String, Object>>() {});
    }
}
