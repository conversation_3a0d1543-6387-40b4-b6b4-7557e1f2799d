package com.lanshan.base.commonservice.access.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.access.converter.AcApiConverter;
import com.lanshan.base.commonservice.access.dao.AcApiDao;
import com.lanshan.base.commonservice.access.entity.AcApi;
import com.lanshan.base.commonservice.access.qo.AcApiControlQO;
import com.lanshan.base.commonservice.access.qo.AcApiPageQO;
import com.lanshan.base.commonservice.access.qo.AcApiQO;
import com.lanshan.base.commonservice.access.service.AcApiService;
import com.lanshan.base.commonservice.access.vo.AcApiVO;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 系统开放的API(AcApi)表服务实现类
 *
 * <AUTHOR>
 */
@Service("acApiService")
public class AcApiServiceImpl extends ServiceImpl<AcApiDao, AcApi> implements AcApiService {

    @Resource
    private AcApiDao acApiDao;

    @Resource
    private RedisService redisService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    /**
     * 项目启动时，初始化API列表到缓存
     */
    @PostConstruct
    public void initCache() {
        cacheApi();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AcApiQO qo) {
        //校验路径是否存在
        checkPathExist(qo);

        //校验请求方式、分组id、名称是否存在
        checkParamsExist(qo);

        //新增API
        super.save(AcApiConverter.INSTANCE.toEntity(qo));

        //异步更新缓存
        commonExecutor.execute(this::cacheApi);
    }

    /**
     * //校验请求方式、分组id、名称是否存在
     *
     * @param qo API对象
     */
    private void checkParamsExist(AcApiQO qo) {
        LambdaQueryWrapper<AcApi> wrapper = Wrappers.lambdaQuery(AcApi.class);
        wrapper.eq(AcApi::getMethod, qo.getMethod())
                .eq(AcApi::getGroupId, qo.getGroupId())
                .eq(AcApi::getName, qo.getName());
        //查询API是否存在
        if (super.exists(wrapper)) {
            throw ExceptionCodeEnum.API_EXIST.toServiceException();
        }
    }

    /**
     * 校验路径是否存在
     *
     * @param qo API对象
     */
    private void checkPathExist(AcApiQO qo) {
        LambdaQueryWrapper<AcApi> pathWrapper = Wrappers.lambdaQuery(AcApi.class);
        pathWrapper.eq(AcApi::getPath, qo.getPath());
        //查询路径是否存在
        if (super.exists(pathWrapper)) {
            throw ExceptionCodeEnum.API_PATH_EXIST.toServiceException();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AcApiQO qo) {
        //查询API是否存在
        AcApi acApi = super.getById(qo.getId());
        if (acApi == null) {
            throw ExceptionCodeEnum.API_NOT_EXIST.toServiceException();
        }

        //校验路径是否存在
        if (!acApi.getPath().equals(qo.getPath())) {
            checkPathExist(qo);
        }

        //校验请求方式、分组id、名称是否存在
        if (!acApi.getMethod().equals(qo.getMethod()) || !acApi.getGroupId().equals(qo.getGroupId()) || !acApi.getName().equals(qo.getName())) {
            checkParamsExist(qo);
        }

        //更新API
        super.updateById(AcApiConverter.INSTANCE.toEntity(qo));

        //异步更新缓存
        commonExecutor.execute(this::cacheApi);
    }

    @Override
    public List<AcApiVO> listByGroupId(Long groupId) {
        //根据分组ID查询数据
        LambdaQueryWrapper<AcApi> wrapper = Wrappers.lambdaQuery(AcApi.class);
        wrapper.eq(AcApi::getGroupId, groupId);
        List<AcApi> list = super.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return AcApiConverter.INSTANCE.toVO(list);
    }

    @Override
    public IPage<AcApiVO> pageApi(AcApiPageQO pageQO) {
        Page<AcApi> page = new Page<>(pageQO.getPage(), pageQO.getSize());
        LambdaQueryWrapper<AcApi> queryWrapper = Wrappers.lambdaQuery(AcApi.class);

        //groupId
        if (pageQO.getGroupId() != null) {
            queryWrapper.eq(AcApi::getGroupId, pageQO.getGroupId());
        }

        //API名称
        if (StringUtils.isNotBlank(pageQO.getName())) {
            queryWrapper.like(AcApi::getName, pageQO.getName());
        }
        //按照创建时间倒序排序
        queryWrapper.orderByDesc(AcApi::getCreateTime);

        //分页查询
        IPage<AcApi> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQO.getPage(), pageQO.getSize());
        }

        //转换VO
        return result.convert(AcApiConverter.INSTANCE::toVO);

    }

    @Override
    public List<AcApiVO> listByControl(AcApiControlQO qo) {
        //通过控制id、控制类型查询API
        List<AcApi> list = acApiDao.listByControl(qo);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return AcApiConverter.INSTANCE.toVO(list);
    }

    @Override
    public List<AcApi> cacheApi() {
        List<AcApi> list = super.list();
        if (CollUtil.isNotEmpty(list)) {
            //缓存到redis
            Map<String, AcApi> apiMap = CollUtil.toMap(list, null, item -> String.valueOf(item.getId()), item -> item);
            redisService.deleteObject(CommonServiceRedisKeys.ACCESS_API_MAP);
            redisService.setCacheMap(CommonServiceRedisKeys.ACCESS_API_MAP, apiMap);
        }
        return list;
    }

    @Override
    public List<AcApi> listByCache() {
        List<AcApi> list;
        //判断缓存中是否存数据
        if (Boolean.TRUE.equals(redisService.hasKey(CommonServiceRedisKeys.ACCESS_API_MAP))) {
            //从缓存中获取数据
            Map<String, AcApi> apiMap = redisService.getCacheMap(CommonServiceRedisKeys.ACCESS_API_MAP);
            list = JacksonUtils.toObj(JacksonUtils.toJson(apiMap.values()), new TypeReference<>(){});
        } else {
            //如果缓存中不存在，则从数据库中查询并放入缓存
            list = cacheApi();
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delByIds(List<Long> idList) {
        super.removeByIds(idList);

        //异步更新缓存
        commonExecutor.execute(this::cacheApi);
    }

}

