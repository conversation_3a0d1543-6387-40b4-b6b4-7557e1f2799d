package com.lanshan.base.commonservice.schooldata.lzjtu.converter;


import java.util.List;

import com.lanshan.base.commonservice.schooldata.lzjtu.entity.Lxsjbxx;
import com.lanshan.base.commonservice.schooldata.lzjtu.vo.LxsjbxxVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 留学生信息(Lxsjbxx)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface LxsjbxxConverter {

    LxsjbxxConverter INSTANCE = Mappers.getMapper(LxsjbxxConverter.class);

    LxsjbxxVO toVO(Lxsjbxx entity);

    Lxsjbxx toEntity(LxsjbxxVO vo);
    
    List<LxsjbxxVO> toVO(List<Lxsjbxx> entityList);

    List<Lxsjbxx> toEntity(List<LxsjbxxVO> voList);
}


