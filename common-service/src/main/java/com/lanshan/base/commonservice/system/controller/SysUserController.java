package com.lanshan.base.commonservice.system.controller;


import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.dto.AjaxResult;
import com.lanshan.base.api.dto.system.SysDeptVo;
import com.lanshan.base.api.dto.system.SysUserOverallVo;
import com.lanshan.base.api.dto.system.SysUserVo;
import com.lanshan.base.api.dto.user.LoginUser;
import com.lanshan.base.api.enums.SysUserTypeEnum;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.RyStringUtils;
import com.lanshan.base.api.utils.system.SecurityUtils;
import com.lanshan.base.api.vo.user.SysUserVO;
import com.lanshan.base.commonservice.system.dto.SysUserAddDTO;
import com.lanshan.base.commonservice.system.dto.SysUserDTO;
import com.lanshan.base.commonservice.system.dto.SysUserSearchDTO;
import com.lanshan.base.commonservice.system.dto.SysUserV2DTO;
import com.lanshan.base.commonservice.system.entity.SysDept;
import com.lanshan.base.commonservice.system.entity.SysRole;
import com.lanshan.base.commonservice.system.entity.SysUser;
import com.lanshan.base.commonservice.system.service.*;
import com.lanshan.base.starter.log.annotation.Log;
import com.lanshan.base.starter.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ServiceConstant.COMMON_SYS_USER)
@Api(tags = "系统用户信息控制器")
public class SysUserController extends BaseController {
    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ISysRoleService sysRoleService;

    @Resource
    private ISysDeptService sysDeptService;

    @Resource
    private ISysPostService sysPostService;

    @Resource
    private ISysPermissionService sysPermissionService;

    @Resource
    private ISysConfigService sysConfigService;

    @ApiOperation("获取所有用户")
    @GetMapping("/getAllUserIds")
    public Result<List<String>> getAllUserIds() {
        List<SysUser> sysUserList = sysUserService.list(Wrappers.lambdaQuery(SysUser.class)
                .select(SysUser::getUserName)
                .eq(SysUser::getDelFlag, "0")
                .eq(SysUser::getUserType, SysUserTypeEnum.WX_USER.getCode()));
        return Result.build(sysUserList.stream().map(sysUser -> String.valueOf(sysUser.getUserName())).collect(Collectors.toList()));
    }

    @ApiOperation("获取用户通讯录权限")
    @GetMapping("/addressBookScope")
    public Result<Set<Long>> getUserAddressBookScope() {
        return Result.build(sysUserService.getUserAddressBookDeptScope());
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户V2")
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping("/v2")
    public Result<Boolean> addV2(@RequestBody @Validated SysUserAddDTO dto) {
        return Result.build(sysUserService.addUser(dto));
    }

    @ApiOperation("修改用户V2")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/v2/put")
    public AjaxResult editV2(@RequestBody @Validated SysUserV2DTO dto) {
        SysUser user = new SysUser(dto);
        sysUserService.checkUserAllowed(user);
        sysUserService.checkUserDataScope(user.getUserId());
        if (!sysUserService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (RyStringUtils.isNotEmpty(user.getPhonenumber()) && !sysUserService.checkPhoneUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (RyStringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        if (StringUtils.isNotEmpty(user.getPassword())) {
            user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        }
        return toAjax(sysUserService.updateUser(user));
    }

    @ApiOperation("获取系统用户信息列表")
    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    public Result<IPage<SysUserVo>> list(SysUserSearchDTO user) {
        return Result.build(sysUserService.pageUser(user));
    }

    @ApiOperation("获取当前系统用户信息")
    @GetMapping("/info/{username}")
    public Result<LoginUser> info(@PathVariable("username") String username) {
        SysUser sysUser = sysUserService.selectUserByUserName(username);
        if (RyStringUtils.isNull(sysUser)) {
            return new Result<LoginUser>().error("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = sysPermissionService.getRolePermission(sysUser);
        // 权限集合
        Set<String> permissions = sysPermissionService.getMenuPermission(sysUser);
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser.toSysUserVo());
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return Result.build(sysUserVo);
    }

    @ApiOperation("注册用户信息")
    @PostMapping("/register")
    public Result<Boolean> register(@RequestBody SysUserV2DTO sysUserVo) {
        String username = sysUserVo.getUserName();
        SysUser sysUser = new SysUser(sysUserVo);
        if (!("true".equals(sysConfigService.selectConfigByKey("sys.account.registerUser")))) {
            return new Result<Boolean>().error("当前系统没有开启注册功能！");
        }
        if (!sysUserService.checkUserNameUnique(sysUser)) {
            return new Result<Boolean>().error("保存用户'" + username + "'失败，注册账号已存在");
        }
        sysUser.setPassword(SecurityUtils.encryptPassword(sysUser.getPassword()));
        return Result.build(sysUserService.registerUser(sysUser));
    }

    @ApiOperation("获取用户信息")
    @GetMapping("getInfo")
    public Result<SysUserOverallVo> getInfo() {
        SysUser user;
        if (StringUtils.isNumeric(SecurityUtils.getUserId())) {
            user = sysUserService.selectUserById(Long.valueOf(SecurityUtils.getUserId()));
        } else {
            user = sysUserService.selectUserByUserName(SecurityUtils.getUserId());
        }
        // 角色集合
        Set<String> roles = sysPermissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = sysPermissionService.getMenuPermission(user);

        return Result.build(SysUserOverallVo.builder()
                .user(user.toSysUserVo())
                .permissions(permissions)
                .roles(roles).build());
    }

    @ApiOperation("根据用户ID获取用户全面信息")
    @RequiresPermissions("system:user:query")
    @GetMapping("/{userId}")
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        sysUserService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = sysRoleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", sysPostService.selectPostAll());
        if (RyStringUtils.isNotNull(userId)) {
            SysUser sysUser = sysUserService.selectUserById(userId);
            sysUser.setPassword(null);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", sysPostService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Validated SysUserDTO dto) {
        SysUser user = new SysUser(dto);
        if (!sysUserService.checkUserNameUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (RyStringUtils.isNotEmpty(user.getPhonenumber()) && !sysUserService.checkPhoneUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (RyStringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());

        if (StringUtils.isEmpty(user.getPassword())) {
            user.setPassword(DigestUtil.md5Hex(user.getUserName() + "lskj"));
        }

        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(sysUserService.insertUser(user));
    }

    @ApiOperation("修改用户")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/put")
    public AjaxResult edit(@RequestBody @Validated SysUserDTO dto) {
        SysUser user = new SysUser(dto);
        sysUserService.checkUserAllowed(user);
        sysUserService.checkUserDataScope(user.getUserId());
        if (!sysUserService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (RyStringUtils.isNotEmpty(user.getPhonenumber()) && !sysUserService.checkPhoneUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (RyStringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        if (StringUtils.isNotEmpty(user.getPassword())) {
            user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        }
        return toAjax(sysUserService.updateUser(user));
    }

    @ApiOperation("删除用户")
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @PostMapping("/{userIds}/delete")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(sysUserService.deleteUserByIds(userIds));
    }

    @ApiOperation("重置密码")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPwd/put")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        sysUserService.checkUserAllowed(user);
        sysUserService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(sysUserService.resetPwd(user));
    }

    @ApiOperation("状态修改")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus/put")
    public AjaxResult changeStatus(@RequestBody SysUserVo userVo) {
        SysUser user = new SysUser(userVo);
        sysUserService.checkUserAllowed(user);
        sysUserService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(sysUserService.updateUserStatus(user));
    }

    @ApiOperation("根据用户ID获取授权角色")
    @RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = sysUserService.selectUserById(userId);
        List<SysRole> roles = sysRoleService.selectRolesByUserId(userId);
        user.setPassword(null);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    @ApiOperation("用户授权角色")
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PostMapping("/authRole/put")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        sysUserService.checkUserDataScope(userId);
        sysUserService.insertUserAuth(userId, roleIds);
        return success();
    }

    @ApiOperation("获取部门树列表")
    @RequiresPermissions("system:user:list")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDeptVo dept) {
        return success(sysDeptService.selectDeptTreeList(new SysDept(dept)));
    }

    /**
     * Description: 根据id获取用户信息
     * Author: jiacheng yang.
     * Date: 2025/02/28 19:37
     * Param: [id]
     */
    @PostMapping("/getUserById/{id}")
    public Result<SysUserVO> getUserById(@PathVariable Long id) {
        SysUserVO sysUser = sysUserService.getUserById(id);
        return Result.build(sysUser);
    }
}
