package com.lanshan.base.commonservice.todo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 已完成统计VO
 */
@ApiModel(description = "已完成统计VO")
@Data
public class CompletedStatVO implements Serializable {

    private static final long serialVersionUID = -5828597111896708091L;

    @ApiModelProperty("每小时的完成数")
    private Map<String, Integer> hourOfCompletedVOMap;

    @ApiModelProperty("每天的完成数")
    private Map<String, Integer> dayOfCompletedVOMap;
}
