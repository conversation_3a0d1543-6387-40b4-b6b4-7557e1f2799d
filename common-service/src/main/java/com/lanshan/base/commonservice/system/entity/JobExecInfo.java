package com.lanshan.base.commonservice.system.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 任务执行信息(JobExecInfo)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class JobExecInfo extends Model<JobExecInfo> {
    /**
     * 同步数据任务作业执行ID
     */
    private String id;
    /**
     * 任务类型。xxl_job里的jobId
     */
    private String jobType;
    /**
     * 任务名称
     */
    private String jobName;
    /**
     * 开始执行时间
     */
    private Date execStartTime;
    /**
     * 任务执行结束时间
     */
    private Date execEndTime;
    /**
     * 过期时间。任务锁释放时间。一般是开始时间后1小时
     */
    private Date expiredTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

