package com.lanshan.base.commonservice.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * JSONB到List<Object>的类型处理器
 *
 * 专门用于处理数据库JSONB字段到Java List<Object>类型的转换
 *
 * 使用场景：
 * 1. 数据库中存储JSON数组，如：["1","2","3"]或[{"id":1,"name":"test"}]
 * 2. 需要将JSON数组转换为Java List<Object>对象
 * 3. 适用于混合类型的数组
 *
 * 使用方式：
 * @TableField(typeHandler = JsonbListTypeHandler.class)
 * private List<Object> items;
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/7/6
 */
@MappedTypes({List.class})
public class JsonbListTypeHandler extends UniversalJsonbTypeHandler<List<Object>> {

    /**
     * 默认构造函数，处理List<Object>类型
     */
    public JsonbListTypeHandler() {
        super(new TypeReference<List<Object>>() {});
    }
}
