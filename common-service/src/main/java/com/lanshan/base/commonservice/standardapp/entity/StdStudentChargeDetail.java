package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 学生收费详情查询表(StdStudentChargeDetail)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class StdStudentChargeDetail extends Model<StdStudentChargeDetail> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 工号
     */
    private String userId;
    /**
     * 类型 1:欠费 2:已缴纳
     */
    private Integer type;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目区间
     */
    private String projectSection;
    /**
     * 收费时间
     */
    private String chargeTime;
    /**
     * 应交
     */
    private String payableNum;
    /**
     * 实缴
     */
    private String paidNum;
    /**
     * 减免总计
     */
    private String derateNum;
    /**
     * 退费总计
     */
    private String returnPremiumNum;
    /**
     * 欠费总计
     */
    private String arrearageNum;

    /**
     * 年度
     */
    private Integer year;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

