package com.lanshan.base.commonservice.schooldata.whut.converter;


import java.util.List;

import com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduateCourseTeacher;
import com.lanshan.base.commonservice.schooldata.whut.vo.WhutUndergraduateCourseTeacherVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 本科生课表信息-教师表(WhutUndergraduateCourseTeacher)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface WhutUndergraduateCourseTeacherConverter {

    WhutUndergraduateCourseTeacherConverter INSTANCE = Mappers.getMapper(WhutUndergraduateCourseTeacherConverter.class);

    WhutUndergraduateCourseTeacherVO toVO(WhutUndergraduateCourseTeacher entity);

    WhutUndergraduateCourseTeacher toEntity(WhutUndergraduateCourseTeacher entity);

    WhutUndergraduateCourseTeacher toEntity(WhutUndergraduateCourseTeacherVO vo);

    List<WhutUndergraduateCourseTeacherVO> toVO(List<WhutUndergraduateCourseTeacher> entityList);

    List<WhutUndergraduateCourseTeacher> toEntity(List<WhutUndergraduateCourseTeacherVO> voList);
}


