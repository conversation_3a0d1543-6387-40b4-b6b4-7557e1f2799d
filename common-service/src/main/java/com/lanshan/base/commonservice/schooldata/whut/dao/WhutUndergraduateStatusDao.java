package com.lanshan.base.commonservice.schooldata.whut.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.group.dto.MsgClassGroupChatCountDTO;
import com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduateStatus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 本科生学籍基本信息（本科生院）(WhutUndergraduateStatus)表数据库访问层
 */
public interface WhutUndergraduateStatusDao extends BaseMapper<WhutUndergraduateStatus> {

    /**
     * 根据班级代码列表查询班级人数
     * @param classCodeList 班级代码列表
     * @return 班级人数列表
     */
    List<MsgClassGroupChatCountDTO> listCountByClassCode(@Param("classCodeList") List<String> classCodeList);

    /**
     * 清空表
     */
    @Update("truncate table school_data.whut_undergraduate_status")
    void truncate();
}

