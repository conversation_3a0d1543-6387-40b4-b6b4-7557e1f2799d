package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.lanshan.base.commonservice.standardapp.typehandler.StdNetWorkTrafficInfoTypeHandler;
import com.lanshan.base.commonservice.standardapp.vo.NetworkTrafficVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 网络流量使用情况(StdNetworkTrafficUsageInfo)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName(value = "std_network_traffic_usage_info", autoResultMap = true)
public class StdNetworkTrafficUsageInfo extends Model<StdNetworkTrafficUsageInfo> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 工号
     */
    private String userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 当日余额单位 元 保留两位小数
     */
    private Integer balance;
    /**
     * 当日使用的流量 单位 MB * 100
     */
    private Integer networkTrafficUsed;
    /**
     * 当日使用时长 单位 分钟
     */
    private Integer usageTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 网络流量信息
     */
    @TableField(value = "network_traffic_info", typeHandler = StdNetWorkTrafficInfoTypeHandler.class)
    private NetworkTrafficVO networkTrafficInfo;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

