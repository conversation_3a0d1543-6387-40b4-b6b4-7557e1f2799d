package com.lanshan.base.commonservice.workbench.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.dto.app.AppDTO;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.app.AppVO;
import com.lanshan.base.commonservice.workbench.converter.WbAppConverter;
import com.lanshan.base.commonservice.workbench.dto.AppGroupDTO;
import com.lanshan.base.commonservice.workbench.dto.AppSearchDTO;
import com.lanshan.base.commonservice.workbench.dto.AppShowStyleDTO;
import com.lanshan.base.commonservice.workbench.entity.WbApp;
import com.lanshan.base.commonservice.workbench.service.AppManageService;
import com.lanshan.base.commonservice.workbench.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用管理控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("appManage")
@Api(tags = "应用管理控制层", hidden = true)
public class AppManageController {

    @Resource
    private AppManageService appManageService;

    @RequiresPermissions("work:appManage:add")
    @ApiOperation("添加企业微信应用")
    @PostMapping("/add")
    public Result<AppDTO> addApp(@Valid @RequestBody AppDTO appDTO) throws WxErrorException {
        return Result.build(appManageService.addApp(appDTO));
    }

    @RequiresPermissions("work:appManage:list")
    @ApiOperation("分页查询应用列表")
    @PostMapping("/page")
    public Result<IPage<WbAppVO>> pageAppList(@RequestBody AppSearchDTO searchDTO) {
        return Result.build(appManageService.pageApp(searchDTO));
    }
    @RequiresPermissions("work:appManage:list")
    @ApiOperation("应用列表卡片视图")
    @PostMapping("/card/list")
    public Result<List<AppCardListVO>> getAppCardList(@RequestBody AppSearchDTO searchDTO) {
        return Result.build(appManageService.getAppCardList(searchDTO));
    }
    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("获取企业微信应用信息")
    @GetMapping("/getById")
    public Result<AppDTO> getAppInfo(@RequestParam Long appId) {
        return Result.build(appManageService.getAppInfo(appId));
    }

    @ApiOperation("获取企业微信应用信息")
    @GetMapping("/info")
    public Result<WbApp> getAppInfoByCorpIdAndAgentId(@RequestParam(name = "agentId") Integer agentId, @RequestParam(name = "corpId") String corpId) {
        return Result.build(appManageService.getAppInfo(agentId,corpId));
    }

    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("获取企业微信应用自定义展示样式")
    @GetMapping("/getAppShowStyle")
    public Result<AppShowStyleVO> getAppShowStyle(@RequestParam Long appId) {
        return Result.build(appManageService.getAppShowStyle(appId));
    }
    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("设置企业微信应用自定义展示样式")
    @PostMapping("/setAppShowStyle")
    public Result<Boolean> setAppShowStyle(@RequestBody AppShowStyleDTO dto) throws WxErrorException {
        return Result.build(appManageService.setAppShowStyle(dto));
    }

    @ApiOperation("搜索企业微信应用的跳转信息")
    @GetMapping("/searchAppJumpSelect")
    public Result<List<AppTagVO>> searchAppJumpSelect(@RequestParam(required = false) String keyWord) {
        return Result.build(appManageService.searchAppJumpSelect(keyWord));
    }
    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("更新企业微信应用信息")
    @PostMapping("/update")
    public Result<Boolean> updateApp(@RequestBody AppDTO appDTO) throws WxErrorException {
        return Result.build(appManageService.updateApp(appDTO));
    }
    @RequiresPermissions("work:appManage:remove")
    @ApiOperation("删除企业微信应用")
    @PostMapping("/delete/delete")
    public Result<Boolean> deleteApp(@RequestParam Long appId) {
        return Result.build(appManageService.deleteApp(appId));
    }
    @RequiresPermissions("work:appManage:remove")
    @ApiOperation("批量删除企业微信应用")
    @PostMapping("/batch/delete/{appIds}/delete")
    @Deprecated(since = "2022-12-5")
    public Result<Boolean> deleteAppList(@PathVariable("appIds") Long[] appIds) {
        return Result.build(appManageService.deleteApp(Arrays.asList(appIds)));
    }
    @RequiresPermissions("work:appManage:remove")
    @ApiOperation("批量删除企业微信应用（参数放body）")
    @PostMapping("/delete/batch/delete")
    public Result<Boolean> deleteAppBatch(@RequestBody List<Long> appIds) {
        return Result.build(appManageService.deleteApp(appIds));
    }
    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("同步所有企业微信应用")
    @GetMapping("/syncAllFromWechatCp")
    public Result<Boolean> syncAllFromWechatCp() {
        return Result.build(appManageService.syncAllFromWechatCp());
    }

    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("设置应用分类")
    @PostMapping("/setGroup")
    public Result<Boolean> setGroup(@RequestBody AppGroupDTO dto) {
        return Result.build(appManageService.setGroup(dto));
    }
    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("获取应用详情")
    @GetMapping("/app/detail")
    public Result<WbAppVO> getAppDetail(Long appId) {
        return Result.build(appManageService.getAppDetail(appId));
    }
    @RequiresPermissions("work:appManage:edit")
    @ApiOperation("启用/停用应用")
    @GetMapping("/swift/enable")
    public Result<Boolean> swiftAppEnable(Long appId) throws WxErrorException {
        return Result.build(appManageService.swiftAppEnable(appId));
    }

    @ApiOperation("应用健康检查一次")
    @GetMapping("/checkAllAppHealth")
    public Result<Boolean> checkAllAppHealth() {
        appManageService.checkAppHealthStatus();
        return Result.build(true);
    }


    @ApiOperation("根据appId查询应用")
    @GetMapping("/listAppByAppId")
    public Result<List<AppVO>> listAppByAppId(@RequestParam Collection<Long> appIds) {
        return Result.build(appManageService.listAppByAppId(appIds));
    }

    @ApiOperation("根据appId查询应用")
    @GetMapping("/listAppByAgentIds")
    public Result<List<AppVO>> listAppByAgentIds(@RequestParam Collection<Integer> agentIds) {
        return Result.build(appManageService.listAppByAgentIds(agentIds));
    }

    @RequiresPermissions("work:appManage:export")
    @ApiOperation("分页查询应用列表")
    @PostMapping("/export")
    public void exportAppList(@RequestBody AppSearchDTO searchDTO, HttpServletResponse response) {
        IPage<WbAppVO> wbAppVOIPage = appManageService.pageApp(searchDTO);
        try {
            List<WbAppExportVO> wbAppExportVOS = Optional.ofNullable(wbAppVOIPage.getRecords()).orElseGet(ArrayList::new).stream()
                    .map(WbAppConverter.INSTANCE::voToExportVO).collect(Collectors.toList());
            EasyExcel.write(response.getOutputStream()).head(WbAppExportVO.class).sheet("应用列表").doWrite(wbAppExportVOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}
