# JSONB 类型处理器快速使用指南

## 可直接使用的类型处理器

### 1. 基础类型处理器

| 类型处理器 | 处理类型 | 使用场景 | 数据库示例 |
|-----------|---------|---------|-----------|
| `JsonbStringTypeHandler` | `String` | 获取JSONB原始内容 | `{"name":"<PERSON>"}` |
| `JsonbStringListTypeHandler` | `List<String>` | 字符串数组 | `["tag1","tag2","tag3"]` |
| `JsonbIntegerListTypeHandler` | `List<Integer>` | 整数数组 | `[1,2,3]` |
| `JsonbLongListTypeHandler` | `List<Long>` | 长整数数组 | `[1001,1002,1003]` |
| `JsonbListTypeHandler` | `List<Object>` | 混合类型数组 | `["str",123,true]` |
| `JsonbMapTypeHandler` | `Map<String,Object>` | 混合类型对象 | `{"key1":"value","key2":123}` |
| `JsonbStringMapTypeHandler` | `Map<String,String>` | 字符串键值对 | `{"key1":"value1","key2":"value2"}` |

## 使用方法

### 方法1：在实体类注解中使用（推荐）

```java
@Data
@TableName("user_info")
public class UserInfo {
    
    // 字符串类型 - 直接获取JSONB原始内容
    @TableField(typeHandler = JsonbStringTypeHandler.class)
    private String profileJson;
    
    // 字符串列表 - 处理 ["tag1", "tag2", "tag3"]
    @TableField(typeHandler = JsonbStringListTypeHandler.class)
    private List<String> tags;
    
    // 整数列表 - 处理 [1, 2, 3]
    @TableField(typeHandler = JsonbIntegerListTypeHandler.class)
    private List<Integer> categoryIds;
    
    // 长整数列表 - 处理 [1001, 1002, 1003] 或 ["1001", "1002", "1003"]
    @TableField(typeHandler = JsonbLongListTypeHandler.class)
    private List<Long> joinInUser;
    
    // 混合类型对象 - 处理 {"theme":"dark","count":5,"enabled":true}
    @TableField(typeHandler = JsonbMapTypeHandler.class)
    private Map<String, Object> settings;
    
    // 字符串键值对 - 处理 {"label1":"value1","label2":"value2"}
    @TableField(typeHandler = JsonbStringMapTypeHandler.class)
    private Map<String, String> labels;
}
```

### 方法2：在MyBatis XML中使用

```xml
<!-- 插入 -->
<insert id="insertUser">
    INSERT INTO user_info (tags, join_in_user, settings) VALUES (
        #{tags,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringListTypeHandler},
        #{joinInUser,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbLongListTypeHandler},
        #{settings,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbMapTypeHandler}
    )
</insert>

<!-- 更新 -->
<update id="updateUserTags">
    UPDATE user_info 
    SET tags = #{tags,typeHandler=com.lanshan.base.commonservice.typehandler.JsonbStringListTypeHandler}
    WHERE id = #{id}
</update>
```

## 常见使用场景

### 1. 处理joinInUser字段（根据您的记忆）

```java
// 如果数据库中存储的是 ["1","2","3"] 格式
@TableField(typeHandler = JsonbStringListTypeHandler.class)
private List<String> joinInUser;

// 如果数据库中存储的是 [1,2,3] 格式，但您希望转换为Long类型
@TableField(typeHandler = JsonbLongListTypeHandler.class)
private List<Long> joinInUser;
```

### 2. 处理标签和分类

```java
// 标签列表
@TableField(typeHandler = JsonbStringListTypeHandler.class)
private List<String> tags;

// 分类ID列表
@TableField(typeHandler = JsonbIntegerListTypeHandler.class)
private List<Integer> categoryIds;
```

### 3. 处理配置信息

```java
// 混合类型配置
@TableField(typeHandler = JsonbMapTypeHandler.class)
private Map<String, Object> userSettings;

// 纯字符串配置
@TableField(typeHandler = JsonbStringMapTypeHandler.class)
private Map<String, String> userLabels;
```

## 自定义类型处理器

如果需要处理自定义对象，可以这样创建：

```java
// 自定义对象类型处理器
public class UserProfileTypeHandler extends UniversalJsonbTypeHandler<UserProfile> {
    public UserProfileTypeHandler() {
        super(UserProfile.class);
    }
}

// 自定义对象列表类型处理器
public class UserProfileListTypeHandler extends UniversalJsonbTypeHandler<List<UserProfile>> {
    public UserProfileListTypeHandler() {
        super(new TypeReference<List<UserProfile>>() {});
    }
}
```

然后在实体类中使用：

```java
@TableField(typeHandler = UserProfileTypeHandler.class)
private UserProfile profile;

@TableField(typeHandler = UserProfileListTypeHandler.class)
private List<UserProfile> profileHistory;
```

## 注意事项

1. **类型处理器必须有无参构造函数**，这样才能在注解中直接使用
2. **推荐使用注解方式**，比在XML中指定更简洁
3. **数据库字段类型必须是JSONB或JSON**
4. **支持null值处理**，数据库null会转换为Java null
5. **自动异常处理**，JSON解析失败时会记录日志并抛出SQLException

## 数据库表结构示例

```sql
CREATE TABLE user_info (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    profile_json JSONB,
    tags JSONB,
    category_ids JSONB,
    join_in_user JSONB,
    settings JSONB,
    labels JSONB
);

-- 插入示例数据
INSERT INTO user_info (username, tags, join_in_user, settings, labels) VALUES (
    'john_doe',
    '["developer", "java", "spring"]',
    '[1001, 1002, 1003]',
    '{"theme": "dark", "language": "zh-CN", "notifications": true}',
    '{"department": "IT", "level": "senior"}'
);
```

这样您就可以直接在注解或XML中使用这些类型处理器，无需额外配置！
