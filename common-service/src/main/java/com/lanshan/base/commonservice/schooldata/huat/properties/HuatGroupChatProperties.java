package com.lanshan.base.commonservice.schooldata.huat.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 湖北汽院群聊配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "huat.group-chat")
public class HuatGroupChatProperties {

    /**
     * 建群指定二级部门id
     */
    private String groupChatDepartmentid;

    /**
     * 班级建群审批模板id
     */
    private String classApprovalTemplateId;
}
