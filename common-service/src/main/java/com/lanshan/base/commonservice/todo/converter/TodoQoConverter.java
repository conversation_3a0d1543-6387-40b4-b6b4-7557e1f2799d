package com.lanshan.base.commonservice.todo.converter;

import com.lanshan.base.commonservice.todo.qo.*;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(builder = @Builder(disableBuilder = true))
public interface TodoQoConverter {
    TodoQoConverter INSTANCE = Mappers.getMapper(TodoQoConverter.class);

    TodoSaveFlowInfoQo toSaveFlowInfoQo(TodoFlowInfoQo qo);

    TodoSaveFlowNodeQo toSaveFlowNodeQo(TodoSaveFlowQo qo);


    TodoSaveFlowNodeQo toUpdateFlowNodeQo(TodoUpdateFlowQo qo);

    TodoDelFlowInfoQo toDelFlowInfoQo(TodoDelFlowQo qo);

    TodoDelFlowNodeQo toDelFlowNodeQo(TodoDelFlowQo qo);

    @Mapping(source = "flowNodeListInfo", target = "flowNodeList")
    TodoUpdateFlowInfoQo toUpdateFlowInfoQo(TodoFlowInfoQo flowInfo);
}
