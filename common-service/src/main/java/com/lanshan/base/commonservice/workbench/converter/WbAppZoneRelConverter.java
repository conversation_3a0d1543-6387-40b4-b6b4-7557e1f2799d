package com.lanshan.base.commonservice.workbench.converter;


import com.lanshan.base.commonservice.workbench.entity.WbAppZoneRel;
import com.lanshan.base.commonservice.workbench.vo.WbAppZoneRelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 应用专区关联应用表(WbAppZoneRel)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface WbAppZoneRelConverter {

    WbAppZoneRelConverter INSTANCE = Mappers.getMapper(WbAppZoneRelConverter.class);

    WbAppZoneRelVO toVO(WbAppZoneRel entity);

    WbAppZoneRel toEntity(WbAppZoneRelVO vo);

    List<WbAppZoneRelVO> toVO(List<WbAppZoneRel> entityList);

    List<WbAppZoneRel> toEntity(List<WbAppZoneRelVO> voList);
}


