package com.lanshan.base.commonservice.addressbook.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息状态(UserInfoStatus)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class UserInfoStatus extends Model<UserInfoStatus> {
    private static final long serialVersionUID = -3515302432403295098L;
    /**
     * 工号
     */
    @TableId
    private String userId;
    /**
     * 认证状态
     */
    private Boolean authStatus;
    /**
     * 开通状态
     */
    private Boolean openStatus;
    /**
     * 认证时间
     */
    private Date authTime;
    /**
     * 开通时间
     */
    private Date openTime;
    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.userId;
    }
}

