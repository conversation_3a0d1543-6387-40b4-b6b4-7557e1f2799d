package com.lanshan.base.commonservice.standardapp.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.standardapp.dao.StdStudentChargeDetailDao;
import com.lanshan.base.commonservice.standardapp.entity.StdStudentChargeDetail;
import com.lanshan.base.commonservice.standardapp.qo.StdStudentChargeDetailQo;
import com.lanshan.base.commonservice.standardapp.service.StdStudentChargeDetailService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 学生收费详情查询表(StdStudentChargeDetail)表服务实现类
 *
 * <AUTHOR>
 */
@Service("stdStudentChargeDetailService")
public class StdStudentChargeDetailServiceImpl extends ServiceImpl<StdStudentChargeDetailDao, StdStudentChargeDetail> implements StdStudentChargeDetailService {

    @Override
    public List<StdStudentChargeDetail> listStdStudentChargeDetail(StdStudentChargeDetailQo qo) {
        LambdaQueryWrapper<StdStudentChargeDetail> qw = new LambdaQueryWrapper<>();
        qw.eq(StdStudentChargeDetail::getUserId, SecurityContextHolder.getUserId());
        if (qo.getYear() != null) {
            qw.eq(StdStudentChargeDetail::getYear, qo.getYear());
        }
        if (qo.getType() != null) {
            qw.eq(StdStudentChargeDetail::getType, qo.getType());
        }
        List<StdStudentChargeDetail> chargeDetails = this.list(qw);
        if (CollUtil.isEmpty(chargeDetails)) {
            return Collections.emptyList();
        }
        return chargeDetails;
    }
}

