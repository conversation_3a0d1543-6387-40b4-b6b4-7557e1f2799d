package com.lanshan.base.commonservice.common.controller;

import com.lanshan.base.api.qo.user.CheckSignatureQo;
import com.lanshan.base.commonservice.common.service.CpCallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping(value = "/cpCallback")
@Validated
@Slf4j
@Api(tags = "企微回调接口", hidden = true)
public class CpCallbackController {

    @Resource
    private CpCallbackService callbackService;


    @ApiOperation("企微回调GET请求-验证URL有效性")
    @GetMapping("callback")
    public void verifyUrlValid(CheckSignatureQo qo, HttpServletResponse response) throws IOException {
        log.info("get请求参数：{}", qo);
        String msg = callbackService.get(qo);
        log.info("get响应消息：{}", msg);
        response.getWriter().print(msg);
    }

    @ApiOperation("企微回调POST请求-回调通知")
    @PostMapping("callback")
    public void addressBookCallback(CheckSignatureQo qo, @RequestBody String requestBody, HttpServletResponse response) throws IOException {
        log.info("post请求参数：{}", qo);
        String msg = callbackService.post(qo, requestBody);
        log.info("post响应消息：{}", msg);
        response.getWriter().print(msg);
    }
}

