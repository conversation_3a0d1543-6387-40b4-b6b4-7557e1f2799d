package com.lanshan.base.commonservice.group.qo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MsgClassGroupChatDetailQo implements Serializable {

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "学院编码")
    private String instituteCode;

    @ApiModelProperty(value = "专业编码")
    private String majorCode;

    @ApiModelProperty(value = "班级编码")
    private String classCode;

    @ApiModelProperty(value = "学生类型 1：本科生 2：研究生")
    private Integer studentType;

    @ApiModelProperty(value = "班级群聊ID")
    private Long classGroupChatId;
}

