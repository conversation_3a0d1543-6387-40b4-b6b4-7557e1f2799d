package com.lanshan.base.commonservice.doublecheck.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.enums.AgentTypeEnum;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpUserService;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 二次验证任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DoubleCheckTask {

    private final NewStudentDataService newStudentDataService;

    private final WxCpServiceFactory wxCpServiceFactory;

    private final AgentProperties agentProperties;

    @XxlJob("doubleCheck-changeUserid")
    public void changeUserid() {
        //获取有学号还未将学号修改过来的用户
        List<NewStudentData> list = newStudentDataService.list(Wrappers.lambdaQuery(NewStudentData.class)
                .isNotNull(NewStudentData::getXh)
                .ne(NewStudentData::getXh, "")
                .isNotNull(NewStudentData::getUserid)
                .ne(NewStudentData::getUserid, "")
                .eq(NewStudentData::getChanged, false)
        );
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Set<Long> idSet = list.stream().map(NewStudentData::getId).collect(Collectors.toSet());
        WxCpUserService userService = wxCpServiceFactory
                .get(agentProperties.getCorpId(), AgentTypeEnum.ADDRESS_BOOK.getCode()).getUserService();
        for (NewStudentData newStudentData : list) {
            WxCpUser wxCpUser = new WxCpUser();
            wxCpUser.setUserId(newStudentData.getUserid());
            wxCpUser.setNewUserId(newStudentData.getXh());
            try {
                userService.update(wxCpUser);
            } catch (WxErrorException e) {
                log.error("修改 userid 失败", e);
                idSet.remove(newStudentData.getId());
            }
        }
        //批量更新用户
        newStudentDataService.update(Wrappers.lambdaUpdate(NewStudentData.class)
                .set(NewStudentData::getChanged, true)
                .setSql("userid = xh")
                .in(NewStudentData::getId, idSet)
        );
    }
}
