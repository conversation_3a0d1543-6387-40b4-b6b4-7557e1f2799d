package com.lanshan.base.commonservice.standardapp.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 图书欠费违章表(StdBookArrearsViolation)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "图书欠费违章表VO")
@Data
@ToString
public class StdBookArrearsViolationVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "工号")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "欠费金额")
    private String arrearageAmount;

    @ApiModelProperty(value = "违规状态")
    private String violationStatus;

    @ApiModelProperty(value = "部门")
    private String dept;

    @ApiModelProperty(value = "借阅等级")
    private String borrowLevel;

    @ApiModelProperty(value = "读者类型")
    private String readerType;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "最大可借图书")
    private String loanableMax;

    @ApiModelProperty(value = "可借图书信息")
    private String loanableInfo;

    @ApiModelProperty(value = "累计借书")
    private String borrowTotal;

    @ApiModelProperty(value = "证件有效期")
    private String certificateValidity;
}

