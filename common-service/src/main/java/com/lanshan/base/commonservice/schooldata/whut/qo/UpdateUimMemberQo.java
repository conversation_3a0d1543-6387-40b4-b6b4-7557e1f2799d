package com.lanshan.base.commonservice.schooldata.whut.qo;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * 修改用户接口
 */

@Data
public class UpdateUimMemberQo {

    //密钥
    private String secret_key = Base64.encode(SecureUtil.sha1().digest("sUTpzlELC1sXNuKnB4DxmRGEgkQ=", StandardCharsets.UTF_8));

    //对应的第三方系统名称（协议值）
    @JsonProperty("tp_name")
    private String tp_name = "wechat";

    //对应的模块名称（协议值）
    @JsonProperty("module_id")
    private String module_id = "uim";

    //对应的平台系统名称（协议值）
    @JsonProperty("sys_id")
    private String sys_id = "sys";

    @JsonProperty("interface_method")
    private String interface_method = "updateUimMember";

    //登录密码
    @JsonProperty("USER_PWD")
    private String USER_PWD;

    //用户登录状态
    @JsonProperty("USER_FIRST_LOGIN")
    private String USER_FIRST_LOGIN = "4";

    //学号或者工号
    @JsonProperty("ID_NUMBER")
    private String ID_NUMBER;

    //手机号
    @JsonProperty("MOBILE")
    private String MOBILE;

    //时间戳
    @JsonProperty("timestamp")
    private long timestamp;

    @JsonProperty("sign")
    private String sign;

}
