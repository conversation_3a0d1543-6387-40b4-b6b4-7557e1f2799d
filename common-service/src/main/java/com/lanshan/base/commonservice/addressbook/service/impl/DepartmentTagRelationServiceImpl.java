package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.addressbook.dto.DeptTagsRelationDto;
import com.lanshan.base.commonservice.addressbook.entity.CpDepartmentTagRelation;
import com.lanshan.base.commonservice.addressbook.mapper.CpDepartmentTagRelationMapper;
import com.lanshan.base.commonservice.addressbook.service.DepartmentTagRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门标签-关联表(TagDepartmentRelation)服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Slf4j
@Service
public class DepartmentTagRelationServiceImpl extends ServiceImpl<CpDepartmentTagRelationMapper, CpDepartmentTagRelation> implements DepartmentTagRelationService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDeptTagsRelation(DeptTagsRelationDto dto) {
        //清除旧关联
        LambdaQueryWrapper<CpDepartmentTagRelation> queryWrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class);
        queryWrapper.eq(CpDepartmentTagRelation::getDepartmentid, dto.getDepartmentid());
        super.remove(queryWrapper);

        //添加新关联
        List<CpDepartmentTagRelation> entityList = dto.getTagidList().stream().map(tagid ->
                CpDepartmentTagRelation.builder()
                        .departmentid(dto.getDepartmentid())
                        .tagid(tagid)
                        .build()
        ).collect(Collectors.toList());
        super.saveBatch(entityList);
    }

    @Override
    public List<CpDepartmentTagRelation> listByDeptIds(List<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CpDepartmentTagRelation> queryWrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class);
        queryWrapper.in(CpDepartmentTagRelation::getDepartmentid, deptIds);
        return list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delByTagidList(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }

        super.remove(Wrappers.<CpDepartmentTagRelation>lambdaQuery().in(CpDepartmentTagRelation::getTagid, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delByDeptIdList(List<Long> deptIdList) {
        if (CollUtil.isEmpty(deptIdList)) {
            return;
        }

        super.remove(Wrappers.<CpDepartmentTagRelation>lambdaQuery().in(CpDepartmentTagRelation::getDepartmentid, deptIdList));
    }

    @Override
    public List<CpDepartmentTagRelation> listTagRelationByDeptidList(List<Long> deptIdList) {
        if (CollUtil.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<CpDepartmentTagRelation> queryWrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class);
        queryWrapper.in(CpDepartmentTagRelation::getDepartmentid, deptIdList);
        List<CpDepartmentTagRelation> list = list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list;
    }

    /**
     * 同步标签关联部门关系到本地标准库
     *
     * @param tagId       标签id
     * @param partyIdlist 部门id列表（从企业微信接口获取的数据）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncTagDeptFromWx(Long tagId, List<Long> partyIdlist) {

        super.remove(new LambdaQueryWrapper<CpDepartmentTagRelation>().eq(CpDepartmentTagRelation::getTagid, tagId));
        if (CollUtil.isEmpty(partyIdlist)) {
            return;
        }

        List<CpDepartmentTagRelation> entityList = partyIdlist.stream().map(partyId ->
                CpDepartmentTagRelation.builder()
                        .departmentid(partyId)
                        .tagid(tagId)
                        .build()
        ).collect(Collectors.toList());
        saveBatch(entityList);
    }
}
