package com.lanshan.base.commonservice.workbench.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用统计(WbAppStat)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "应用统计VO")
@Data
@ToString
public class WbAppStatVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用主键")
    private Long appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "用户使用次数")
    private Integer userUsed;

    @ApiModelProperty(value = "学生使用次数")
    private Integer stuUsed;

    @ApiModelProperty(value = "老师使用次数")
    private Integer tchUsed;

    @ApiModelProperty(value = "用户点击次数")
    private Integer userClick;

    @ApiModelProperty(value = "学生点击次数")
    private Integer stuClick;

    @ApiModelProperty(value = "教师点击次数")
    private Integer tchClick;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "当月使用人数")
    private Integer monthUsed;

    @ApiModelProperty(value = "当月学生使用人数")
    private Integer monthStuUsed;

    @ApiModelProperty(value = "当月教师使用人数")
    private Integer monthTchUsed;

    @ApiModelProperty(value = "本科生使用人数")
    private Integer stuUndergraduateUsed;

    @ApiModelProperty(value = "研究生使用人数")
    private Integer stuMasterUsed;

    @ApiModelProperty(value = "本科生点击次数")
    private Integer stuUndergraduateClick;

    @ApiModelProperty(value = "研究生点击次数")
    private Integer stuMasterClick;

    @ApiModelProperty(value = "当月本科生使用人数")
    private Integer monthStuUndergraduateUsed;

    @ApiModelProperty(value = "当月研究生使用人数")
    private Integer monthStuMasterUsed;

    @ApiModelProperty(value = "其他使用人数")
    private Integer otherUsed;

    @ApiModelProperty(value = "其他点击次数")
    private Integer otherClick;

    @ApiModelProperty(value = "当月其他使用人数")
    private Integer monthOtherUsed;

    @ApiModelProperty(value = "标签主键")
    private Long tagId;
}

