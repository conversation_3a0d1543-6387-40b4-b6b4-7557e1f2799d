spring:
  cloud:
    nacos:
      discovery:
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        server-addr: ${NACOS_SERVER_IP:127.0.0.1}:${NACOS_SERVER_PORT:8848}
        namespace: ${NACOS_NAMESPACE:9e1db6d0-61cf-40d2-9cc3-978a519f0ecf}
        group: ${NACOS_GROUP:DEV_GROUP}
      config:
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: yaml
        # 共享配置
        shared-configs:
          - data-id: base-start-config.yaml
            group: ${spring.cloud.nacos.config.group}
            refresh: true

