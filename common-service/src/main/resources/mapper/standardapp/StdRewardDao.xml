<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdRewardDao">
    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdReward" id="StdRewardMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="amountPaidOut" column="amount_paid_out" jdbcType="VARCHAR"/>
        <result property="amountPayable" column="amount_payable" jdbcType="VARCHAR"/>
        <result property="aggregateDeduction" column="aggregate_deduction" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="inputDate" column="input_date" jdbcType="VARCHAR"/>
        <result property="invoiceNumber" column="invoice_number" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="month" column="month" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="StdRewardYearMonthMap" type="com.lanshan.base.commonservice.standardapp.vo.StdRewardYearMonthVO">
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="months" column="months" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_reward(user_id, user_name, amount_paid_out, amount_payable, aggregate_deduction,
                                            create_time, update_time, input_date, invoice_number, type, year, month,
                                            summary)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.userName}, #{entity.amountPaidOut}, #{entity.amountPayable},
             #{entity.aggregateDeduction}, #{entity.createTime}, #{entity.updateTime}, #{entity.inputDate},
             #{entity.invoiceNumber}, #{entity.type}, #{entity.year}, #{entity.month}, #{entity.summary})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_app.std_reward(user_id, user_name, amount_paid_out, amount_payable, aggregate_deduction,
                                            create_time, update_time, input_date, invoice_number, type, year, month,
                                            summary)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.userName}, #{entity.amountPaidOut}, #{entity.amountPayable},
             #{entity.aggregateDeduction}, #{entity.createTime}, #{entity.updateTime}, #{entity.inputDate},
             #{entity.invoiceNumber}, #{entity.type}, #{entity.year}, #{entity.month}, #{entity.summary})
        </foreach>
        ON CONFLICT(id) DO update set user_id             = EXCLUDED.user_id,
                                      user_name           = EXCLUDED.user_name,
                                      amount_paid_out     = EXCLUDED.amount_paid_out,
                                      amount_payable      = EXCLUDED.amount_payable,
                                      aggregate_deduction = EXCLUDED.aggregate_deduction,
                                      create_time         =
                                          EXCLUDED.create_time,
                                      update_time         = EXCLUDED.update_time,
                                      input_date          = EXCLUDED.input_date,
                                      invoice_number      =
                                          EXCLUDED.invoice_number,
                                      type                = EXCLUDED.type,
                                      year                = EXCLUDED.year,
                                      month               = EXCLUDED.month,
                                      summary             =
                                          EXCLUDED.summary
    </insert>

    <select id="getYearMonthList" resultMap="StdRewardYearMonthMap">
        SELECT year,
               json_agg(month) AS months
        FROM standard_app.std_reward
        WHERE year IS NOT NULL
          AND month IS NOT NULL
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        GROUP BY year
    </select>
</mapper>

