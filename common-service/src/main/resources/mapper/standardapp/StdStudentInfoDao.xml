<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdStudentInfoDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdStudentInfo" id="StdStudentInfoMap">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="enrollmentDate" column="enrollment_date" jdbcType="TIMESTAMP"/>
        <result property="collegeNumber" column="college_number" jdbcType="VARCHAR"/>
        <result property="collegeName" column="college_name" jdbcType="VARCHAR"/>
        <result property="professionalNumber" column="professional_number" jdbcType="VARCHAR"/>
        <result property="professionalName" column="professional_name" jdbcType="VARCHAR"/>
        <result property="classNumber" column="class_number" jdbcType="VARCHAR"/>
        <result property="className" column="class_name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ClassTreeVOMap" type="com.lanshan.base.commonservice.standardapp.vo.ClassTreeVO">
        <result property="uniqueNumber" column="unique_number"/>
        <result property="name" column="name"/>
        <result property="children" column="children"
                typeHandler="com.lanshan.base.commonservice.typehandler.ClassTreeVOListJacksonTypeHandler"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into standard_app.std_student_info(name, enrollment_date, college_number, college_name, professional_number, professional_name, class_number, class_name, type, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.enrollmentDate} , #{entity.collegeNumber} , #{entity.collegeName} , #{entity.professionalNumber} , #{entity.professionalName} , #{entity.classNumber} , #{entity.className} , #{entity.type} , #{entity.createDate} , #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into standard_app.std_student_info(name, enrollment_date, college_number, college_name, professional_number, professional_name, class_number, class_name, type, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.enrollmentDate}, #{entity.collegeNumber}, #{entity.collegeName}, #{entity.professionalNumber}, #{entity.professionalName}, #{entity.classNumber}, #{entity.className}, #{entity.type}, #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , enrollment_date = EXCLUDED.enrollment_date , college_number = EXCLUDED.college_number , college_name = EXCLUDED.college_name , professional_number = EXCLUDED.professional_number , professional_name = EXCLUDED.professional_name , class_number = EXCLUDED.class_number , class_name = EXCLUDED.class_name , type = EXCLUDED.type , create_date = EXCLUDED.create_date , update_date = EXCLUDED.update_date     </insert>

    <select id="getClassTree" resultMap="ClassTreeVOMap">
        WITH class_agg AS (SELECT college_number,
                                  college_name,
                                  grade,
                                  professional_number,
                                  professional_name,
                                  class_number,
                                  class_name
                           FROM standard_app.std_student_info
                           WHERE type = 1
                           GROUP BY college_number, college_name, grade, professional_number, professional_name,
                                    class_number,
                                    class_name),
             professional_agg AS (SELECT college_number,
                                         college_name,
                                         grade,
                                         professional_number,
                                         professional_name,
                                         json_agg(
                                                 json_build_object(
                                                         'uniqueNumber', class_number,
                                                         'name', class_name
                                                 )
                                         ) AS classes
                                  FROM class_agg
                                  GROUP BY college_number, college_name, grade, professional_number, professional_name),
             grade_agg AS (SELECT college_number,
                                  college_name,
                                  grade,
                                  json_agg(
                                          json_build_object(
                                                  'uniqueNumber', professional_number,
                                                  'name', professional_name,
                                                  'children', classes
                                          )
                                  ) AS professionals
                           FROM professional_agg
                           GROUP BY college_number, college_name, grade)

        SELECT college_number AS unique_number,
               college_name   AS name,
               json_agg(
                       json_build_object(
                               'uniqueNumber', grade,
                               'name', grade,
                               'children', professionals
                       )
               )              AS children
        FROM grade_agg
        GROUP BY college_number, college_name
        ORDER BY college_number
    </select>

    <select id="getTeachClassTree" resultMap="ClassTreeVOMap">
        WITH class_agg AS (SELECT college_number,
                                  college_name,
                                  grade,
                                  professional_number,
                                  professional_name,
                                  teach_class_num,
                                  teach_class_name
                           FROM standard_app.std_student_info
                           WHERE type = 1
                           GROUP BY college_number, college_name, grade, professional_number, professional_name,
                                    teach_class_num,
                                    teach_class_name),
             professional_agg AS (SELECT college_number,
                                         college_name,
                                         grade,
                                         professional_number,
                                         professional_name,
                                         json_agg(
                                                 json_build_object(
                                                         'uniqueNumber', teach_class_num,
                                                         'name', teach_class_name
                                                 )
                                         ) AS classes
                                  FROM class_agg
                                  GROUP BY college_number, college_name, grade, professional_number, professional_name),
             grade_agg AS (SELECT college_number,
                                  college_name,
                                  grade,
                                  json_agg(
                                          json_build_object(
                                                  'uniqueNumber', professional_number,
                                                  'name', professional_name,
                                                  'children', classes
                                          )
                                  ) AS professionals
                           FROM professional_agg
                           GROUP BY college_number, college_name, grade)

        SELECT college_number AS unique_number,
               college_name   AS name,
               json_agg(
                       json_build_object(
                               'uniqueNumber', grade,
                               'name', grade,
                               'children', professionals
                       )
               )              AS children
        FROM grade_agg
        GROUP BY college_number, college_name
        ORDER BY college_number
    </select>
</mapper>

