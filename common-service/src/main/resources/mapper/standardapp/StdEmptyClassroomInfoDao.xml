<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.StdEmptyClassroomInfoDao">

    <resultMap type="com.lanshan.base.commonservice.standardapp.entity.StdEmptyClassroomInfo" id="StdEmptyClassroomInfoMap">
        <result property="id" column="id"/>
        <result property="campusCode" column="campus_code"/>
        <result property="campusName" column="campus_name"/>
        <result property="buildingCode" column="building_code"/>
        <result property="buildingName" column="building_name"/>
        <result property="classroomCode" column="classroom_code"/>
        <result property="classroomName" column="classroom_name"/>
        <result property="date" column="date"/>
        <result property="sessionFree" column="session_free" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

</mapper>

