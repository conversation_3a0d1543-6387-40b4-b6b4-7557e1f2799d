<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.workbench.dao.WbAppUsedDao">
    <resultMap type="com.lanshan.base.commonservice.workbench.entity.WbAppUsed" id="WbAppUsedMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_used(app_id, user_id, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId} , #{entity.userId} , #{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into workbench.wb_app_used(app_id, user_id, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.userId}, #{entity.createTime})
        </foreach>
        on duplicate key update
        app_id = values(app_id) , user_id = values(user_id) , create_time = values(create_time)
    </insert>
</mapper>

