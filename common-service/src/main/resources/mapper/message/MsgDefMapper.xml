<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.message.dao.MsgDefMapper">

    <resultMap type="com.lanshan.base.commonservice.message.entity.MsgDef" id="MsgDefMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="linkUrl" column="link_url"/>
        <result property="linkAppId" column="link_app_id"/>
        <result property="imgUrl" column="img_url"/>
        <result property="source" column="source"/>
        <result property="appId" column="app_id"/>
        <result property="appName" column="app_name"/>
        <result property="agentId" column="agent_id"/>
        <result property="publisher" column="publisher"/>
        <result property="publishType" column="publish_type"/>
        <result property="publishChannel" column="publish_channel" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="publishChannelSendStatus" column="publish_channel_send_status" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="publishTime" column="publish_time"/>
        <result property="userList" column="user_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="departmentList" column="department_list" typeHandler="com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler"/>
        <result property="tagList" column="tag_list" typeHandler="com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler"/>
        <result property="sendUserCount" column="send_user_count"/>
        <result property="readUserCount" column="read_user_count"/>
        <result property="status" column="status"/>
        <result property="isMustRead" column="is_must_read"/>
        <result property="cpMsgId" column="cp_msg_id"/>
        <result property="creator" column="creator"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="parcelLinkUrl" column="parcel_link_url"/>
    </resultMap>

    <select id="listAllApp" resultType="com.lanshan.base.commonservice.message.vo.MsgAppVo">
        SELECT
            DISTINCT md.app_id as value,
	        md.app_name as label
        FROM
            msg_def md
        WHERE md.app_id IS NOT NULL
    </select>

</mapper>

