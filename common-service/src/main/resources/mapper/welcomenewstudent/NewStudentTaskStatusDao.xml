<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentTaskStatusDao">
    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTaskStatus"
               id="NewStudentTaskStatusMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="newStuTaskId" column="new_stu_task_id"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task_status(id_card_num, new_stu_task_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userid}, #{entity.newStuTaskId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task_status(id_card_num, new_stu_task_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userid}, #{entity.newStuTaskId})
        </foreach>
        ON CONFLICT(id) DO update set id_card_num     = EXCLUDED.id_card_num,
                                      new_stu_task_id = EXCLUDED.new_stu_task_id
    </insert>
</mapper>

