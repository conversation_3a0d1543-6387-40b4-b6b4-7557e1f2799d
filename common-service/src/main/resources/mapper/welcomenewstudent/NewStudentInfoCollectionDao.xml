<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentInfoCollectionDao">

    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentInfoCollection" id="NewStudentInfoCollectionMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="newStudentDataId" column="new_student_data_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="politicalStatus" column="political_status" jdbcType="VARCHAR"/>
        <result property="nationality" column="nationality" jdbcType="VARCHAR"/>
        <result property="birthDate" column="birth_date" jdbcType="TIMESTAMP"/>
        <result property="idCardNumber" column="id_card_number" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="permanentAddress" column="permanent_address" jdbcType="VARCHAR"/>
        <result property="parentContactInfo" column="parent_contact_info" typeHandler="com.lanshan.base.commonservice.typehandler.ObjectListTypeHandler"/>
        <result property="nativePlace" column="native_place" jdbcType="VARCHAR"/>
        <result property="householdAddress" column="household_address" jdbcType="VARCHAR"/>
        <result property="studentCategory" column="student_category" jdbcType="VARCHAR"/>
        <result property="accidentInsuranceFlag" column="accident_insurance_flag" jdbcType="BOOLEAN"/>
        <result property="height" column="height" jdbcType="VARCHAR"/>
        <result property="weight" column="weight" jdbcType="VARCHAR"/>
        <result property="shoeSize" column="shoe_size" jdbcType="VARCHAR"/>
        <result property="dailyNecessities" column="daily_necessities" jdbcType="VARCHAR"/>
    </resultMap>

</mapper> 