<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.access.dao.AcDataDao">
    <resultMap type="com.lanshan.base.commonservice.access.entity.AcData" id="AcDataMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="dataKey" column="data_key" jdbcType="VARCHAR"/>
        <result property="dataName" column="data_name" jdbcType="VARCHAR"/>
        <result property="controlMeta" column="control_meta" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.ac_data(data_key, data_name, control_meta)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dataKey} , #{entity.dataName} , #{entity.controlMeta, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.ac_data(data_key, data_name, control_meta)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dataKey}, #{entity.dataName}, #{entity.controlMeta, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
        </foreach>
        ON CONFLICT(id) DO update set
        data_key = EXCLUDED.data_key , data_name = EXCLUDED.data_name , control_meta = EXCLUDED.control_meta
    </insert>
</mapper>

