<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.access.dao.AcApiDao">

    <resultMap type="com.lanshan.base.commonservice.access.entity.AcApi" id="AcApiMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="module" column="module" jdbcType="VARCHAR"/>
        <result property="system" column="system" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="groupId" column="group_id" jdbcType="INTEGER"/>
        <result property="openDocUrl" column="open_doc_url" jdbcType="VARCHAR"/>
        <result property="serviceId" column="service_id"/>
        <result property="serviceName" column="service_name"/>
    </resultMap>

    <sql id="Api_Column_List">
        ac_api.id, ac_api.name, ac_api.path, ac_api.method, ac_api.description, ac_api.module, ac_api.system, ac_api.create_by, ac_api.create_time, ac_api.update_by, ac_api.update_time, ac_api.group_id, ac_api.open_doc_url ,ac_api.service_id, ac_api.service_name    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.ac_api(name, path, method, description, module, system, create_by, create_time, update_by, update_time, group_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.path} , #{entity.method} , #{entity.description} , #{entity.module} , #{entity.system} , #{entity.createBy} , #{entity.createTime} , #{entity.updateBy} , #{entity.updateTime} , #{entity.groupId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.ac_api(name, path, method, description, module, system, create_by, create_time, update_by, update_time, group_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.path}, #{entity.method}, #{entity.description}, #{entity.module}, #{entity.system}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.groupId})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , path = EXCLUDED.path , method = EXCLUDED.method , description = EXCLUDED.description , module = EXCLUDED.module , system = EXCLUDED.system , create_by = EXCLUDED.create_by , create_time = EXCLUDED.create_time , update_by = EXCLUDED.update_by , update_time = EXCLUDED.update_time , group_id = EXCLUDED.group_id     </insert>

    <select id="listByControl" resultMap="AcApiMap">
        SELECT
        <include refid="Api_Column_List"/>
        FROM ac_api_control,
             ac_api
        WHERE ac_api_control.api_id = ac_api.id
          AND ac_api_control.control_type = #{param.controlType}
        <if test="param.controlIdList != null and param.controlIdList.size > 0 ">
            AND ac_api_control.control_id IN
            <foreach collection="param.controlIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>

