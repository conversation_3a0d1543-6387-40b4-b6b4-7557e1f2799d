<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutUndergraduateMajorInfoDao">

    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduateMajorInfo" id="WhutUndergraduateMajorInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="zybm" column="zybm" jdbcType="VARCHAR"/>
        <result property="zymc" column="zymc" jdbcType="VARCHAR"/>
        <result property="zyqc" column="zyqc" jdbcType="VARCHAR"/>
        <result property="zyywmc" column="zyywmc" jdbcType="VARCHAR"/>
        <result property="zypymb" column="zypymb" jdbcType="VARCHAR"/>
        <result property="zypyyq" column="zypyyq" jdbcType="VARCHAR"/>
        <result property="zylb" column="zylb" jdbcType="VARCHAR"/>
        <result property="gsdlmc" column="gsdlmc" jdbcType="VARCHAR"/>
        <result property="zslb" column="zslb" jdbcType="VARCHAR"/>
        <result property="yxbm" column="yxbm" jdbcType="VARCHAR"/>
        <result property="xqbm" column="xqbm" jdbcType="VARCHAR"/>
        <result property="xz" column="xz" jdbcType="VARCHAR"/>
        <result property="pycc" column="pycc" jdbcType="VARCHAR"/>
        <result property="bzkzybm" column="bzkzybm" jdbcType="VARCHAR"/>
        <result property="xw" column="xw" jdbcType="VARCHAR"/>
        <result property="sffxzy" column="sffxzy" jdbcType="VARCHAR"/>
        <result property="qybz" column="qybz" jdbcType="VARCHAR"/>
        <result property="tstamp" column="tstamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_undergraduate_major_info(zybm, zymc, zyqc, zyywmc, zypymb, zypyyq, zylb, gsdlmc, zslb, yxbm, xqbm, xz, pycc, bzkzybm, xw, sffxzy, qybz, tstamp)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.zybm} , #{entity.zymc} , #{entity.zyqc} , #{entity.zyywmc} , #{entity.zypymb} , #{entity.zypyyq} , #{entity.zylb} , #{entity.gsdlmc} , #{entity.zslb} , #{entity.yxbm} , #{entity.xqbm} , #{entity.xz} , #{entity.pycc} , #{entity.bzkzybm} , #{entity.xw} , #{entity.sffxzy} , #{entity.qybz} , #{entity.tstamp})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_undergraduate_major_info(zybm, zymc, zyqc, zyywmc, zypymb, zypyyq, zylb, gsdlmc, zslb, yxbm, xqbm, xz, pycc, bzkzybm, xw, sffxzy, qybz, tstamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.zybm}, #{entity.zymc}, #{entity.zyqc}, #{entity.zyywmc}, #{entity.zypymb}, #{entity.zypyyq}, #{entity.zylb}, #{entity.gsdlmc}, #{entity.zslb}, #{entity.yxbm}, #{entity.xqbm}, #{entity.xz}, #{entity.pycc}, #{entity.bzkzybm}, #{entity.xw}, #{entity.sffxzy}, #{entity.qybz}, #{entity.tstamp})
        </foreach>
        ON CONFLICT(id) DO update set
zybm = EXCLUDED.zybm , zymc = EXCLUDED.zymc , zyqc = EXCLUDED.zyqc , zyywmc = EXCLUDED.zyywmc , zypymb = EXCLUDED.zypymb , zypyyq = EXCLUDED.zypyyq , zylb = EXCLUDED.zylb , gsdlmc = EXCLUDED.gsdlmc , zslb = EXCLUDED.zslb , yxbm = EXCLUDED.yxbm , xqbm = EXCLUDED.xqbm , xz = EXCLUDED.xz , pycc = EXCLUDED.pycc , bzkzybm = EXCLUDED.bzkzybm , xw = EXCLUDED.xw , sffxzy = EXCLUDED.sffxzy , qybz = EXCLUDED.qybz , tstamp = EXCLUDED.tstamp     </insert>

</mapper>

