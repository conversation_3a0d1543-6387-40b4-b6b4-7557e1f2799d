<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.whut.dao.WhutUndergraduateDao">

    <resultMap type="com.lanshan.base.commonservice.schooldata.whut.entity.WhutUndergraduate" id="WhutUndergraduateMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="qywxUserId" column="qywx_user_id" jdbcType="VARCHAR"/>
        <result property="xh" column="xh" jdbcType="VARCHAR"/>
        <result property="ksh" column="ksh" jdbcType="VARCHAR"/>
        <result property="xm" column="xm" jdbcType="VARCHAR"/>
        <result property="wwxm" column="wwxm" jdbcType="VARCHAR"/>
        <result property="xmpy" column="xmpy" jdbcType="VARCHAR"/>
        <result property="cym" column="cym" jdbcType="VARCHAR"/>
        <result property="xbm" column="xbm" jdbcType="VARCHAR"/>
        <result property="xbmdmmc" column="xbmdmmc" jdbcType="VARCHAR"/>
        <result property="csrq" column="csrq" jdbcType="TIMESTAMP"/>
        <result property="csd" column="csd" jdbcType="VARCHAR"/>
        <result property="jg" column="jg" jdbcType="VARCHAR"/>
        <result property="mz" column="mz" jdbcType="VARCHAR"/>
        <result property="gjdqm" column="gjdqm" jdbcType="VARCHAR"/>
        <result property="sfzjlx" column="sfzjlx" jdbcType="VARCHAR"/>
        <result property="sfzjh" column="sfzjh" jdbcType="VARCHAR"/>
        <result property="sfzjyxq" column="sfzjyxq" jdbcType="VARCHAR"/>
        <result property="hyzkm" column="hyzkm" jdbcType="VARCHAR"/>
        <result property="gatqwm" column="gatqwm" jdbcType="VARCHAR"/>
        <result property="zzmm" column="zzmm" jdbcType="VARCHAR"/>
        <result property="jkzk" column="jkzk" jdbcType="VARCHAR"/>
        <result property="xyzjm" column="xyzjm" jdbcType="VARCHAR"/>
        <result property="xxm" column="xxm" jdbcType="VARCHAR"/>
        <result property="sfdszn" column="sfdszn" jdbcType="VARCHAR"/>
        <result property="lydq" column="lydq" jdbcType="VARCHAR"/>
        <result property="xsly" column="xsly" jdbcType="VARCHAR"/>
        <result property="hkxz" column="hkxz" jdbcType="VARCHAR"/>
        <result property="hkszd" column="hkszd" jdbcType="VARCHAR"/>
        <result property="yzbm" column="yzbm" jdbcType="VARCHAR"/>
        <result property="txdz" column="txdz" jdbcType="VARCHAR"/>
        <result property="lxdh" column="lxdh" jdbcType="VARCHAR"/>
        <result property="dzyx" column="dzyx" jdbcType="VARCHAR"/>
        <result property="jstxh" column="jstxh" jdbcType="VARCHAR"/>
        <result property="yktzh" column="yktzh" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_undergraduate(qywx_user_id, xh, ksh, xm, wwxm, xmpy, cym, xbm, xbmdmmc, csrq, csd, jg, mz, gjdqm, sfzjlx, sfzjh, sfzjyxq, hyzkm, gatqwm, zzmm, jkzk, xyzjm, xxm, sfdszn, lydq, xsly, hkxz, hkszd, yzbm, txdz, lxdh, dzyx, jstxh, yktzh)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.qywxUserId} , #{entity.xh} , #{entity.ksh} , #{entity.xm} , #{entity.wwxm} , #{entity.xmpy} , #{entity.cym} , #{entity.xbm} , #{entity.xbmdmmc} , #{entity.csrq} , #{entity.csd} , #{entity.jg} , #{entity.mz} , #{entity.gjdqm} , #{entity.sfzjlx} , #{entity.sfzjh} , #{entity.sfzjyxq} , #{entity.hyzkm} , #{entity.gatqwm} , #{entity.zzmm} , #{entity.jkzk} , #{entity.xyzjm} , #{entity.xxm} , #{entity.sfdszn} , #{entity.lydq} , #{entity.xsly} , #{entity.hkxz} , #{entity.hkszd} , #{entity.yzbm} , #{entity.txdz} , #{entity.lxdh} , #{entity.dzyx} , #{entity.jstxh} , #{entity.yktzh})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into school_data.whut_undergraduate(qywx_user_id, xh, ksh, xm, wwxm, xmpy, cym, xbm, xbmdmmc, csrq, csd, jg, mz, gjdqm, sfzjlx, sfzjh, sfzjyxq, hyzkm, gatqwm, zzmm, jkzk, xyzjm, xxm, sfdszn, lydq, xsly, hkxz, hkszd, yzbm, txdz, lxdh, dzyx, jstxh, yktzh)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.qywxUserId}, #{entity.xh}, #{entity.ksh}, #{entity.xm}, #{entity.wwxm}, #{entity.xmpy}, #{entity.cym}, #{entity.xbm}, #{entity.xbmdmmc}, #{entity.csrq}, #{entity.csd}, #{entity.jg}, #{entity.mz}, #{entity.gjdqm}, #{entity.sfzjlx}, #{entity.sfzjh}, #{entity.sfzjyxq}, #{entity.hyzkm}, #{entity.gatqwm}, #{entity.zzmm}, #{entity.jkzk}, #{entity.xyzjm}, #{entity.xxm}, #{entity.sfdszn}, #{entity.lydq}, #{entity.xsly}, #{entity.hkxz}, #{entity.hkszd}, #{entity.yzbm}, #{entity.txdz}, #{entity.lxdh}, #{entity.dzyx}, #{entity.jstxh}, #{entity.yktzh})
        </foreach>
        ON CONFLICT(id) DO update set
qywx_user_id = EXCLUDED.qywx_user_id , xh = EXCLUDED.xh , ksh = EXCLUDED.ksh , xm = EXCLUDED.xm , wwxm = EXCLUDED.wwxm , xmpy = EXCLUDED.xmpy , cym = EXCLUDED.cym , xbm = EXCLUDED.xbm , xbmdmmc = EXCLUDED.xbmdmmc , csrq = EXCLUDED.csrq , csd = EXCLUDED.csd , jg = EXCLUDED.jg , mz = EXCLUDED.mz , gjdqm = EXCLUDED.gjdqm , sfzjlx = EXCLUDED.sfzjlx , sfzjh = EXCLUDED.sfzjh , sfzjyxq = EXCLUDED.sfzjyxq , hyzkm = EXCLUDED.hyzkm , gatqwm = EXCLUDED.gatqwm , zzmm = EXCLUDED.zzmm , jkzk = EXCLUDED.jkzk , xyzjm = EXCLUDED.xyzjm , xxm = EXCLUDED.xxm , sfdszn = EXCLUDED.sfdszn , lydq = EXCLUDED.lydq , xsly = EXCLUDED.xsly , hkxz = EXCLUDED.hkxz , hkszd = EXCLUDED.hkszd , yzbm = EXCLUDED.yzbm , txdz = EXCLUDED.txdz , lxdh = EXCLUDED.lxdh , dzyx = EXCLUDED.dzyx , jstxh = EXCLUDED.jstxh , yktzh = EXCLUDED.yktzh     </insert>

</mapper>

