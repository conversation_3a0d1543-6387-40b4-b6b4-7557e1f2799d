<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.csmz.dao.CsmzZzjgDao">
    <resultMap type="com.lanshan.base.commonservice.schooldata.csmz.entity.CsmzZzjg" id="CsmzZzjgMap">
        <result property="dwdm" column="dwdm" jdbcType="VARCHAR"/>
        <result property="dwmc" column="dwmc" jdbcType="VARCHAR"/>
        <result property="dwlbm" column="dwlbm" jdbcType="VARCHAR"/>
        <result property="dwjc" column="dwjc" jdbcType="VARCHAR"/>
        <result property="lsdwh" column="lsdwh" jdbcType="VARCHAR"/>
        <result property="dwyxbs" column="dwyxbs" jdbcType="VARCHAR"/>
        <result property="isGen" column="is_gen" jdbcType="INTEGER"/>
        <result property="pxh" column="pxh" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="dwdm" useGeneratedKeys="true">
        insert into school_data.csmz_zzjg(dwmc, dwlbm, dwjc, lsdwh, dwyxbs, is_gen, pxh)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dwmc}, #{entity.dwlbm}, #{entity.dwjc}, #{entity.lsdwh}, #{entity.dwyxbs}, #{entity.isGen},
             #{entity.pxh})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="dwdm" useGeneratedKeys="true">
        insert into school_data.csmz_zzjg(dwmc, dwlbm, dwjc, lsdwh, dwyxbs, is_gen, pxh)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dwmc}, #{entity.dwlbm}, #{entity.dwjc}, #{entity.lsdwh}, #{entity.dwyxbs}, #{entity.isGen},
             #{entity.pxh})
        </foreach>
        ON CONFLICT(id) DO update set dwmc   = EXCLUDED.dwmc,
                                      dwlbm  = EXCLUDED.dwlbm,
                                      dwjc   = EXCLUDED.dwjc,
                                      lsdwh  = EXCLUDED.lsdwh,
                                      dwyxbs = EXCLUDED.dwyxbs,
                                      is_gen = EXCLUDED.is_gen,
                                      pxh    = EXCLUDED.pxh
    </insert>
</mapper>

