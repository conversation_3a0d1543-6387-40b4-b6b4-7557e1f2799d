<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.mapper.CpDepartmentOperateMapper">

    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.CpDepartmentOperate" id="DepartmentOperateMap">
        <result property="tId" column="t_id"/>
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="nameEn" column="name_en"/>
        <result property="parentid" column="parentid"/>
        <result property="order" column="order"/>
        <result property="departmentLeader" column="department_leader" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="operateStatus" column="operate_status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

</mapper>

