<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.DepartmentOperateLogMapper">

    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.addressbook.entity.DepartmentOperateLog">
        <!--@Table department_operate_log-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="operateDetail" column="operate_detail" jdbcType="VARCHAR"/>
        <result property="operatePerson" column="operate_person" jdbcType="VARCHAR"/>
        <result property="operateTime" column="operate_time" jdbcType="TIMESTAMP"/>
        <result property="operateLog" column="operate_log" jdbcType="INTEGER"/>
        <result property="departmentPath" column="department_path" jdbcType="VARCHAR"/>
        <result property="operateType" column="operate_type" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
operate_detail,
operate_person,
operate_time,
operate_log,
department_path,
operate_type

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.department_operate_log(operate_detail, operate_person, operate_time, operate_log, department_path, operate_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.operateDetail}, #{entity.operatePerson}, #{entity.operateTime}, #{entity.operateLog}, #{entity.departmentPath}, #{entity.operateType})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.department_operate_log(operate_detail, operate_person, operate_time, operate_log, department_path, operate_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.operateDetail}, #{entity.operatePerson}, #{entity.operateTime}, #{entity.operateLog}, #{entity.departmentPath}, #{entity.operateType})
        </foreach>
        on duplicate key update
        operate_detail = values(operate_detail) , operate_person = values(operate_person) , operate_time = values(operate_time) , operate_log = values(operate_log) , department_path = values(department_path) , operate_type = values(operate_type)     </insert>

</mapper>

