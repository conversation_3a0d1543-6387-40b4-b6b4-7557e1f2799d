package com.lanshan.base.commonservice.excel.handler;

import com.alibaba.excel.EasyExcel;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 处理器编译测试
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
public class HandlerCompileTest {

    @Test
    public void testHandlersCompile() {
        // 测试处理器是否能正常实例化
        AutoColumnWidthHandler columnHandler = new AutoColumnWidthHandler();
        AutoRowHeightHandler rowHandler = new AutoRowHeightHandler();
        AutoSizeHandler sizeHandler = new AutoSizeHandler();
        
        // 测试静态工厂方法
        AutoColumnWidthHandler.create();
        AutoRowHeightHandler.create();
        AutoSizeHandler.create();
        
        System.out.println("所有处理器编译通过！");
    }
    
    @Test
    public void testBasicUsage() {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            List<TestData> data = new ArrayList<>();
            data.add(new TestData("测试", "Test", 123));
            
            // 测试基本使用
            EasyExcel.write(outputStream, TestData.class)
                    .registerWriteHandler(new AutoSizeHandler())
                    .sheet("测试")
                    .doWrite(data);
                    
            System.out.println("基本使用测试通过！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static class TestData {
        private String name;
        private String englishName;
        private Integer number;
        
        public TestData() {}
        
        public TestData(String name, String englishName, Integer number) {
            this.name = name;
            this.englishName = englishName;
            this.number = number;
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getEnglishName() { return englishName; }
        public void setEnglishName(String englishName) { this.englishName = englishName; }
        public Integer getNumber() { return number; }
        public void setNumber(Integer number) { this.number = number; }
    }
}
