package com.lanshan.base.api.dto.message;

import com.lanshan.base.api.enums.MsgTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "发送小程序通知类型消息体类")
public class MiniPnMsgBody extends BaseMsgBody implements Serializable {

    @ApiModelProperty(value = "消息类型。此处固定为miniprogram_notice", hidden = true)
    private String msgType = MsgTypeEnum.MINIPROGRAM_NOTICE.value();

    @ApiModelProperty(value = "小程序appid，必须是与当前应用关联的小程序，appid和pagepath必须同时填写，填写后会忽略url字段", required = true)
    private String appId;

    @ApiModelProperty(value = "标题，不超过128个字节，超过会自动截断", required = true)
    private String title;

    @ApiModelProperty(value = "点击消息卡片后的小程序页面，最长1024个字节，仅限本小程序内的页面。该字段不填则消息点击后不跳转。")
    private String page;

    @ApiModelProperty("是否放大第一个content_item")
    private Boolean emphasisFirstItem;

    @ApiModelProperty("消息内容键值对，最多允许10个item")
    private Map<String, String> contentItems;
}
