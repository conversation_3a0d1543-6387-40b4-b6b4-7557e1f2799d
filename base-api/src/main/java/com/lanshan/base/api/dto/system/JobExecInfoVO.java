package com.lanshan.base.api.dto.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;


/**
 * 任务执行信息(JobExecInfo)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class JobExecInfoVO {
    /**
     * 同步数据任务作业执行ID
     */
    @ApiModelProperty(value = "同步数据任务作业执行ID")
    private String id;
    /**
     * 任务类型。xxl_job里的jobId
     */
    @ApiModelProperty(value = "任务类型。xxl_job里的jobId")
    private String jobType;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String jobName;
    /**
     * 开始执行时间
     */
    @ApiModelProperty(value = "开始执行时间")
    private Date execStartTime;
    /**
     * 任务执行结束时间
     */
    @ApiModelProperty(value = "任务执行结束时间")
    private Date execEndTime;
    /**
     * 过期时间。任务锁释放时间。一般是开始时间后1小时
     */
    @ApiModelProperty(value = "过期时间。任务锁释放时间。一般是开始时间后1小时")
    private Date expiredTime;
}

