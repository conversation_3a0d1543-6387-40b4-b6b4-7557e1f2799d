package com.lanshan.base.api.enums;

import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@ApiModel(value = "消息发送渠道枚举")
public enum MsgChannelEnum {

    //企业微信
    WEIXIN_CORP_CHANNEL,

    //短信
    SMS_CHANNEL,

    //邮件
    EMAIL_CHANNEL,

    //应用消息
    APP_CHANNEL;

    /**
     * 发送所有消息渠道
     *
     * @return List<MsgChannelEnum>
     */
    public static List<MsgChannelEnum> sendAll() {
        return Arrays.stream(MsgChannelEnum.values()).collect(Collectors.toList());
    }

    /**
     * 仅发送企业微信
     *
     * @return List<MsgChannelEnum>
     */
    public static List<MsgChannelEnum> sendWxcorpOnly() {
        List<MsgChannelEnum> list = new ArrayList<>();
        list.add(MsgChannelEnum.WEIXIN_CORP_CHANNEL);
        return list;
    }

    /**
     * 发送指定指定渠道
     *
     * @param channel 指定渠道
     * @return List<MsgChannelEnum>
     */
    public static List<MsgChannelEnum> send(MsgChannelEnum... channel) {
        List<MsgChannelEnum> list = new ArrayList<>();
        Collections.addAll(list, channel);
        return list;
    }
}
