package com.lanshan.base.api.dto.user;


import com.lanshan.base.api.dto.ResponseSet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "导出成员详情 export/user")
public class ExportUserResponseSet extends ResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID，可通过获取导出结果接口查询任务结果")
    private String jobid;
}
