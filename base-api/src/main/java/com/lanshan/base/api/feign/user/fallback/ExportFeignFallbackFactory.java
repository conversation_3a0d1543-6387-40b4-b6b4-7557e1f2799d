package com.lanshan.base.api.feign.user.fallback;

import com.lanshan.base.api.dto.common.JobIdDTO;
import com.lanshan.base.api.feign.user.ExportFeign;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.export.WxCpExportRequest;
import me.chanjar.weixin.cp.bean.export.WxCpExportResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Description: 企业微信应用服务异常处理
 * @Author: GaoJian
 * @Date: 2023/10/13
 */
@Slf4j
@Component
public class ExportFeignFallbackFactory implements FallbackFactory<ExportFeign> {
    @Override
    public ExportFeign create(Throwable cause) {
        return new ExportFeign() {
            @Override
            public Result<JobIdDTO> exportSimpleUser(String corpId, String agentId, WxCpExportRequest request) {
                return new Result<JobIdDTO>().error();
            }

            @Override
            public Result<JobIdDTO> exportUser(String corpId, String agentId, WxCpExportRequest request) {
                return new Result<JobIdDTO>().error();
            }

            @Override
            public Result<JobIdDTO> exportDepartment(String corpId, String agentId, WxCpExportRequest request) {
                return new Result<JobIdDTO>().error();
            }

            @Override
            public Result<JobIdDTO> exportTaguser(String corpId, String agentId, WxCpExportRequest request) {
                return new Result<JobIdDTO>().error();
            }

            @Override
            public Result<WxCpExportResult> exportGetResult(String corpId, String agentId, String jobId) {
                return new Result<WxCpExportResult>().error();
            }
        };
    }
}
