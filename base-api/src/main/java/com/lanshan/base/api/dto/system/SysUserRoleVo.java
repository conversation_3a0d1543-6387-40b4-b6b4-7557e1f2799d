package com.lanshan.base.api.dto.system;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 用户和角色关联表(SysUserRole)表实体VO类
 *
 * <AUTHOR>
 * @since 2023-10-22 23:50:24
 */
@ApiModel(value = "用户和角色关联表VO")
@Data
@ToString
public class SysUserRoleVo implements Serializable {

    private static final long serialVersionUID = -7226440495030601964L;
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "角色ID")
    private Long roleId;
}

