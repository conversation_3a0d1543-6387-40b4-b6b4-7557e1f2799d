package com.lanshan.base.api.dto.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * token信息对象类
 */
@Data
@ApiModel(value = "token信息类")
@SuperBuilder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TokenInfo {

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "token的有效时长，单位秒")
    private Integer expiresIn;

    @ApiModelProperty(value = "refreshToken")
    private String refreshToken;

    @ApiModelProperty(value = "refreshToken的有效时长，单位秒")
    private Integer refreshTokenExpiresIn;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "成员票据")
    private String userTicket;
}
