package com.lanshan.base.api.dto.message;

import com.lanshan.base.api.enums.MsgChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "发送文本类型消息体类(Markdown类型同样适用)")
@ToString
public class TextMsgBody extends BaseMsgBody implements Serializable {
    private static final long serialVersionUID = -5063807718369676970L;

    @Getter
    @Setter
    @ApiModelProperty(value = "消息类型。此处可设置text 或 markdown", required = true)
    private String msgType;

    @Getter
    @Setter
    @ApiModelProperty(value = "消息内容,如果是根据模板发送消息，此处内容为内容参数JSON字符窜", required = true)
    private String content;

    @ApiModelProperty(value = "消息可发送渠道")
    private List<MsgChannelEnum> sendChannels;

    @Getter
    @Setter
    @ApiModelProperty(value = "手机号列表")
    private List<String> mobileList;

    @Getter
    @Setter
    @ApiModelProperty(value = "邮箱列表")
    private List<String> mailList;

    /**
     * 文本消息可发送渠道可多选
     *
     * @return List<MsgChannelEnum>
     */
    @Override
    public List<MsgChannelEnum> getSendChannels() {
        if (CollectionUtils.isEmpty(sendChannels)) {
            return MsgChannelEnum.sendWxcorpOnly();
        }
        return this.sendChannels;
    }

    @Override
    public void setSendChannels(List<MsgChannelEnum> sendChannels) {
        this.sendChannels = sendChannels;
    }
}
