package com.lanshan.base.api.qo.message;

import com.lanshan.base.api.qo.BaseRequest;
import com.lanshan.base.api.enums.MsgTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 只允许企业自建应用调用，且应用的可见范围必须是根部门。
 *
 * chatid所代表的群必须是该应用所创建。
 * 每企业消息发送量不可超过2万人次/分，不可超过30万人次/小时（若群有100人，每发一次消息算100人次）。
 * 每个成员在群中收到的应用消息不可超过200条/分，1万条/天，超过会被丢弃（接口不会报错）。
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "应用推送消息 appchat/send")
public class MessageAppchatSendQo extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息请求内容json字符串")
    private String messageJson;
    @ApiModelProperty(value = "消息类型")
    private MsgTypeEnum msgTypeEnum;

}
