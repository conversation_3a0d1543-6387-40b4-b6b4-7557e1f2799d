package com.lanshan.base.api.dto.user;


import com.lanshan.base.api.dto.system.SysUserVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "登录用户信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * tokenKey
     */
    @ApiModelProperty(value = "tokenKey")
    private String tokenKey;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 登录时间
     */
    @ApiModelProperty(value = "登录时间")
    private Long loginTime;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    private Long expireTime;

    /**
     * 登录IP地址
     */
    @ApiModelProperty(value = "登录IP地址")
    private String ipaddr;

    /**
     * 权限列表
     */
    @ApiModelProperty(value = "权限列表")
    private Set<String> permissions;

    /**
     * 角色列表
     */
    @ApiModelProperty(value = "角色列表")
    private Set<String> roles;

    /**
     * 系统用户信息
     */
    @ApiModelProperty(value = "系统用户信息")
    private SysUserVo sysUser;
}
