package com.lanshan.base.api.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "标签成员Dto")
public class TagUserDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标签名")
    private String tagname;
    @ApiModelProperty(value = "标签中包含的成员列表")
    private List<User> userlist;
    @ApiModelProperty(value = "标签中包含的部门id列表")
    private List<Long> partylist;

    @Data
    public static class User implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "成员账号")
        private String userid;
        @ApiModelProperty(value = "成员名称，代开发自建应用需要管理员授权才返回该字段；此字段从2019年12月30日起，对新创建第三方应用不再返回，2020年6月30日起，对所有历史第三方应用不再返回，后续第三方仅通讯录应用可获取，未返回名称的情况需要通过通讯录展示组件来展示名字")
        private String name;
    }
}
