package com.lanshan.base.api.qo.user;

import com.lanshan.base.api.qo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 要求对标签有读取权限
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "导出标签成员 export/taguser")
public class ExportTaguserQo extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "需要导出的标签")
    private Long tagid;
    @ApiModelProperty(value = "Base64编码后的加密密钥。长度固定为43，从a-z, A-Z, 0-9共62个字符中选取，是AESKey的Base64编码。解码后即为32字节长的AESKey。加密方式采用AES-256-CBC方式，数据采用PKCS#7填充至32字节的倍数；IV初始向量大小为16字节，取AESKey前16字节，详见：https://datatracker.ietf.org/doc/html/rfc2315")
    private String encoding_aeskey;
    @ApiModelProperty(value = "每块数据的人员数和部门数之和，支持范围[10^4,10^6]，默认值为10^6")
    private Integer block_size;
}
