package com.lanshan.base.api.dto.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;


@Data
@ApiModel(value = "文件信息")
@ToString
@NoArgsConstructor
public class FileInfo implements Serializable {

    private static final long serialVersionUID = -5828030107721216147L;
    @ApiModelProperty(value = "文件ID")
    private Long id;

    @ApiModelProperty(value = "文件访问路径")
    private String url;

    @ApiModelProperty(value = "文件原始名称")
    private String originalName;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型")
    private String fileType;
}
