package com.lanshan.base.api.dto.user;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "成员ID列表Dto")
public class UserIdListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分页游标，下次请求时填写以获取之后分页的记录。如果该字段返回空则表示已没有更多数据")
    @JsonProperty("next_cursor")
    private String nextCursor;
    @ApiModelProperty(value = "用户-部门关系列表")
    @JsonProperty("dept_user")
    private List<DeptUser> deptUser;

    @Data
    public static class DeptUser implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "用户userid，当用户在多个部门下时会有多条记录")
        private String userid;
        @ApiModelProperty(value = "用户所属部门")
        private Long department;
    }
}
