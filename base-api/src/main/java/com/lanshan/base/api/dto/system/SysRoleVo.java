package com.lanshan.base.api.dto.system;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 角色信息表(SysRole)表实体VO类
 *
 * <AUTHOR>
 * @since 2023-10-22 23:50:21
 */
@ApiModel(value = "角色信息表VO")
@Data
@ToString
public class SysRoleVo implements Serializable {

    private static final long serialVersionUID = -2727041503412274220L;
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    @ApiModelProperty(value = "角色名称", required = true)
    @NotNull(message = "角色名称不能为空")
    @Size(max = 30, message = "角色名称长度不能超过30个字符")
    private String roleName;

    @ApiModelProperty(value = "角色权限字符串", required = true)
    @Size(max = 100, message = "角色权限字符串长度不能超过100个字符")
    @NotNull(message = "角色权限字符串不能为空")
    private String roleKey;

    @ApiModelProperty(value = "显示顺序", required = true)
    @Digits(integer = 5, fraction = 0, message = "显示顺序必须是数字")
    @NotNull(message = "显示顺序不能为空")
    private Integer roleSort;

    @ApiModelProperty(value = "数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）")
    private String dataScope;

    @ApiModelProperty(value = "菜单树选择项是否关联显示")
    private Integer menuCheckStrictly;

    @ApiModelProperty(value = "部门树选择项是否关联显示")
    private Integer deptCheckStrictly;

    @ApiModelProperty(value = "角色状态（0正常 1停用）", required = true)
    @NotNull(message = "角色状态不能为空")
    private String status;

    @ApiModelProperty(value = "创建者", hidden = true)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新者", hidden = true)
    private String updateBy;

    @ApiModelProperty(value = "更新时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "菜单组", required = true)
    private Long[] menuIds;

    @ApiModelProperty(value = "部门组（数据权限）")
    private Long[] deptIds;

    @ApiModelProperty(value = "角色菜单权限")
    private Set<String> permissions;

    @ApiModelProperty(value = "用户是否存在此角色标识 默认不存在")
    private boolean flag = false;
}

