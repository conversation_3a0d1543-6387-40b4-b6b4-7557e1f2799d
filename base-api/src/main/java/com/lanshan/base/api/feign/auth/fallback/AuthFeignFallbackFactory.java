package com.lanshan.base.api.feign.auth.fallback;

import com.lanshan.base.api.feign.auth.AuthFeign;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUserDetail;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 身份认证服务异常处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AuthFeignFallbackFactory implements FallbackFactory<AuthFeign> {
    @Override
    public AuthFeign create(Throwable cause) {
        return new AuthFeign() {
            @Override
            public Result<WxCpOauth2UserInfo> authGetUserInfo(String corpId, String agentId, String code) {
                return Result.build(new WxCpOauth2UserInfo()).error();
            }

            @Override
            public Result<WxCpUserDetail> authGetUserDetail(String corpId, String agentId, String userTicket) {
                return Result.build(new WxCpUserDetail()).error();
            }

            @Override
            public Result<String> getJsapiTicket(String corpId, String agentId) {
                return new Result<String>().error();
            }

            @Override
            public Result<String> getTicket(String corpId, String agentId) {
                return new Result<String>().error();
            }
        };
    }
}
