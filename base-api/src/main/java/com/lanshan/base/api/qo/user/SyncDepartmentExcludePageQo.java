package com.lanshan.base.api.qo.user;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "同步部门排除PageQo")
public class SyncDepartmentExcludePageQo extends PageQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门id")
    private Long departmentid;

    @ApiModelProperty(value = "部门名称")
    private String name;
}
