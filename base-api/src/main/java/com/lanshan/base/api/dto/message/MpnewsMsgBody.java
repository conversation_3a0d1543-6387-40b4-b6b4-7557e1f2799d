package com.lanshan.base.api.dto.message;

import com.lanshan.base.api.enums.MsgTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "Mpnews类型消息体")
public class MpnewsMsgBody extends BaseMsgBody implements Serializable {

    private static final long serialVersionUID = -4330300906460836487L;

    @ApiModelProperty(value = "消息类型。此处固定为mpnews", hidden = true)
    private String msgType = MsgTypeEnum.MPNEWS.value();

    @ApiModelProperty(value = "图文消息，一个图文消息支持1到8条图文", required = true)
    private List<MpnewsArticleBody> mpnewsArticles = new ArrayList<>();
}
