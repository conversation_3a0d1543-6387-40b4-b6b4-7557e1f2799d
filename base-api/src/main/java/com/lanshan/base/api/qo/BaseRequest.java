package com.lanshan.base.api.qo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @auther: YYmmjj
 * @updateTime: 2023/02/22/10:41
 * @description:
 */
@Data
public class BaseRequest implements Serializable {
	public final static String[] IGNORE_PROPERTIES = {"agentid","accessToken"};
	/**
	 * 应用agentid
	 */
	private Integer agentid;
	/**
	 * access_token
	 */
	private String accessToken;
}
