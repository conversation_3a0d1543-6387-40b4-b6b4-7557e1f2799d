package com.lanshan.base.api.qo.user;

import com.lanshan.base.api.qo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "读取成员 user/get")
public class UserGetQo extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "成员UserID。对应管理端的账号，企业内必须唯一。不区分大小写，长度为1~64个字节")
    private String userid;

}
