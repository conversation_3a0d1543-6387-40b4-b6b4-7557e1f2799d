package com.lanshan.base.api.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstant {
    /**
     * 缓存有效期，默认 2 * 60 * 60（秒）
     */
    public static final long EXPIRATION = 2 * 60 * 60;

    /**
     * 缓存刷新时间，默认30 * 60（秒）
     */
    public static final long REFRESH_TIME = 30 * 60;

    /**
     * 密码最大错误次数
     */
    public static final int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认24（小时）
     */
    public static final long PASSWORD_LOCK_TIME = 24;

    /**
     * token缓存有效期，8小时
     */
    public static final Long TOKEN_EXPIRATION = 3600L * 8;
    /**
     * token无操作缓存有效期，30分钟
     */
    public static final Long TOKEN_OPERATION = 30L * 60;
    /**
     * refresh token缓存有效期，30天
     */
    public static final Long REFRESH_TOKEN_EXPIRATION = 30 * 60 * 60 * 24L;


    /**
     * 登录用户信息缓存前缀
     */
    public static final String LOGIN_USER_KEY = "login:user:";

    /**
     * TOKEN缓存前缀
     */
    public static final String ACCESS_TOKEN_KEY = "access:token:";

    /**
     * refresh token缓存前缀
     */
    public static final String REFRESH_TOKEN_KEY = "refresh:token:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 参数管理type cache key
     */
    public static final String SYS_CONFIG_TYPE_KEY = "sys_config_type:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /**
     * MINIO 的分享链接默认有效期 1天
     */
    public static final Integer MINIO_SHARE_TIME = 60 * 60 * 24;
}
