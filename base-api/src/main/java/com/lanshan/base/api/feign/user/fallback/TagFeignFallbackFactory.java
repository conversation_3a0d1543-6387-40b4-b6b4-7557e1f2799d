package com.lanshan.base.api.feign.user.fallback;

import com.lanshan.base.api.dto.common.TagIdDTO;
import com.lanshan.base.api.dto.user.TagDto;
import com.lanshan.base.api.dto.user.TagUserDto;
import com.lanshan.base.api.feign.user.TagFeign;
import com.lanshan.base.api.qo.user.TagSaveQo;
import com.lanshan.base.api.qo.user.TagUsersQo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.dto.user.TagAddOrRemoveUsersDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 企业微信应用服务异常处理
 * @Author: GaoJian
 * @Date: 2023/10/13
 */
@Slf4j
@Component
public class TagFeignFallbackFactory implements FallbackFactory<TagFeign> {

    @Override
    public TagFeign create(Throwable cause) {
        return new TagFeign() {
            @Override
            public Result<TagIdDTO> tagCreate(String corpId, String agentId, TagSaveQo qo) {
                return new Result<TagIdDTO>().error();
            }

            @Override
            public Result<Object> tagUpdate(String corpId, String agentId, TagSaveQo qo) {
                return new Result<>().error();
            }

            @Override
            public Result<Object> tagDelete(String corpId, String agentId, Long tagId) {
                return new Result<>().error();
            }

            @Override
            public Result<TagUserDto> tagGet(String corpId, String agentId, Long tagId) {
                return new Result<TagUserDto>().error();
            }

            @Override
            public Result<TagAddOrRemoveUsersDto> addTagUsers(String corpId, String agentId, TagUsersQo qo) {
                return new Result<TagAddOrRemoveUsersDto>().error();
            }

            @Override
            public Result<TagAddOrRemoveUsersDto> delTagUsers(String corpId, String agentId, TagUsersQo qo) {
                return new Result<TagAddOrRemoveUsersDto>().error();
            }

            @Override
            public Result<List<TagDto>> tagList(String corpId, String agentId) {
                return new Result<List<TagDto>>().error();
            }
        };
    }
}
