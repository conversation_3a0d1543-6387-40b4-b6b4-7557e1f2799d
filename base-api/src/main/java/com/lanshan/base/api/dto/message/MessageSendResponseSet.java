package com.lanshan.base.api.dto.message;

import com.lanshan.base.api.dto.ResponseSet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 如果部分接收人无权限或不存在，发送仍然执行，但会返回无效的部分（即invaliduser或invalidparty或invalidtag或unlicenseduser），常见的原因是接收人不在应用的可见范围内。
 * 权限包含应用可见范围和基础接口权限(基础账号、互通账号均可)，unlicenseduser中的用户在应用可见范围内但没有基础接口权限。
 * 如果全部接收人无权限或不存在，则本次调用返回失败，errcode为81013。
 * 返回包中的userid，不区分大小写，统一转为小写
 * */
@Data
@ApiModel(value = "企业微信发送应用消息响应结果  message/send")
public class MessageSendResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "不合法的userid，不区分大小写，统一转为小写")
    private String invaliduser;
    @ApiModelProperty(value = "不合法的partyid")
    private String invalidparty;
    @ApiModelProperty(value = "不合法的标签id")
    private String invalidtag;
    @ApiModelProperty(value = "没有基础接口许可(包含已过期)的userid")
    private String unlicenseduser;
    @ApiModelProperty(value = "消息id，用于撤回应用消息")
    private String msgid;
    @ApiModelProperty(value = "仅消息类型为“按钮交互型”，“投票选择型”和“多项选择型”的模板卡片消息返回，应用可使用response_code调用更新模版卡片消息接口，72小时内有效，且只能使用一次")
    private String response_code;

}
