package com.lanshan.app.repair.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 获取指定维修人任务统计信息
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "获取指定维修人任务统计信息")
public class WorkerTaskInfoVO {

    @ApiModelProperty(value = "已完成")
    private String completed;

    @ApiModelProperty(value = "未完成")
    private String uncompleted;

    @ApiModelProperty(value = "已完成24小时")
    private String completedIn24h;

    @ApiModelProperty(value = "任务总量")
    private String totalInTask;

}
