package com.lanshan.app.repair.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 报修操作记录表(RepairOperateLog)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "报修操作记录表VO")
@Data
@ToString
public class RepairOperateLogVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "记录id")
    private Long recordId;

    @ApiModelProperty(value = "操作类型 1：认领 2：确认 3：完成 4：转办")
    private Integer type;

    @ApiModelProperty(value = "操作人id")
    private String userid;

    @ApiModelProperty(value = "操作人名称")
    private String name;

    @ApiModelProperty(value = "是否管理员 0 否 1 是")
    private Integer isAdmin;

    @ApiModelProperty(value = "被转单人id")
    private String transferredUserid;

    @ApiModelProperty(value = "被转单人名称")
    private String transferredName;

    @ApiModelProperty(value = "创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "转单人 userid")
    private String transferUserid;

    @ApiModelProperty(value = "转单人名称")
    private String transferName;
}

