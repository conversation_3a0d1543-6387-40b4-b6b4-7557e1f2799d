package com.lanshan.app.wechatpay.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description: TODO
 * Author: jiacheng yang.
 * Date: 2025/02/26 15:52
 * Version: 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeChatPayResponseOrderDTO implements Serializable {

    private static final long serialVersionUID = -2831656752143553560L;

    private String return_code;

    private String return_msg;

    private String appid;

    private String mch_id;

    private String device_info;

    private String nonce_str;

    private String sign;

    private String result_code;

    private String err_code;

    private String err_code_des;

    /**
     * 以下字段在return_code 和result_code都为SUCCESS的时候有返回
     */
    private String trade_type;

    private String prepay_id;

    private String code_url;


}
