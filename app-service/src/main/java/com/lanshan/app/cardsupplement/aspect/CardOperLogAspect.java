package com.lanshan.app.cardsupplement.aspect;


import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.lanshan.app.cardsupplement.annotation.OperLog;
import com.lanshan.app.cardsupplement.enums.CardUserType;
import com.lanshan.app.cardsupplement.po.CardOperLog;
import com.lanshan.app.cardsupplement.service.CardOperLogService;

import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.wechatpay.util.XmlUtil;
import com.lanshan.app.common.utils.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 补卡申请操作日志切面
 * Author: jiacheng yang.
 * Date: 2025/02/28 17:30
 * Version: 1.0
 */

@Slf4j
@Component
@Aspect
public class CardOperLogAspect {

    // 使用 ThreadLocal 存储 userId
    private final ThreadLocal<String> userIdHolder = new ThreadLocal<>();

    @Resource
    private CardOperLogService cardOperLogService;

    @Before(value = "@annotation(operLog)")
    public void boBefore(OperLog operLog) {
        //ManagerAccessInterceptor.afterCompletion和下面的afterMethod执行顺序不确定，所以用线程隔离的ThreadLocal
        String userId =  SecurityContextHolder.getUserIdStr();
        userIdHolder.set(userId);
        log.info("补卡操作日志方法执行前，当前用户id：{}", userId);
    }


    /**
     * 后置通知：记录方法执行完后的日志
     * @param joinPoint 切点，获取目标方法的执行结果等信息
     */
    @AfterReturning( pointcut = "@annotation(operLog)", returning = "result")
    public void afterMethod(JoinPoint joinPoint,OperLog operLog, Object result) {
        handleCardOperLog(joinPoint, null,operLog, result);
    }

    /**
     * 异常通知：记录方法执行时的异常
     * @param joinPoint 切点，获取目标方法的执行信息
     * @param e 异常信息
     */
    @AfterThrowing(pointcut = "@annotation(operLog)" ,throwing = "e")
    public void afterThrowingMethod(JoinPoint joinPoint,OperLog operLog, Exception e){
        handleCardOperLog(joinPoint, e, operLog,null);
    }

    protected void handleCardOperLog(final JoinPoint joinPoint,final Exception e,OperLog operLog, Object jsonResult){
        try {
            // 处理补卡申请操作日志
            Object[] args = joinPoint.getArgs();
            //json请求体
            Object arg = args[0];
            String jsonBody;
            if (arg instanceof String) {
                jsonBody = (String) arg;
            } else {
                jsonBody = toJson(arg);
            }
            //返回结果
            String resultJson;
            if (jsonResult == null) {
                resultJson = e.getMessage();
            } else if (jsonResult instanceof String){
                resultJson = (String) jsonResult;
            }else {
                resultJson = toJson(jsonResult);
            }
            HttpServletRequest request = getRequest();
            /*
             * 客户端ip、请求uri、请求方法、模块标题、业务类型
             */
            String clientIP = ServletUtil.getClientIP(request);
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            String title = operLog.title();
            Integer businessType = operLog.businessType().type;
            //用户类型
            Integer userType = operLog.userType().type;

            //操作状态（0成功 1失败）
            int status = 0;
            if (e != null) {
                status = 1;
            }
            //操作结果
            CardOperLog cardOperLog = new CardOperLog(
                    title,businessType,userType,requestURI,method,jsonBody,clientIP,status,new Date(),resultJson
            );

            //用户类型是微信回调
            String userId = "";
            if (userType.equals(CardUserType.WECHAT_SERVER.type)){
                Map<String, Object> notifyData  = XmlUtil.getObj(jsonBody,new TypeReference<HashMap<String,Object>>(){});
                if (MapUtil.isNotEmpty(notifyData)){
                    //从通知回调拿到openid,set到userid,以便后续查询
                    userId = (String) notifyData.get("openid");
                }
            }else {
                userId = userIdHolder.get();
            }
            cardOperLog.setUserId(userId);
            log.info("补卡操作日志方法执行后，当前用户id：{}", userId);
            // 异步保存日志
            ThreadPoolUtil.execute(() ->{
                cardOperLogService.saveCardOperLog(cardOperLog);
            });
        }catch (Exception ex) {
            log.error("记录补卡申请日志异常", ex);
        }finally {
            // 清除 ThreadLocal 防止内存泄漏
            log.info("清除 ThreadLocal !!!");
            userIdHolder.remove();
        }
    }


    /**
     * Description: 获取request对象
     * Author: jiacheng yang.
     * Date: 2025/02/28 18:39
     * Param: []
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes servletAttributes = (ServletRequestAttributes) attributes;
        if (servletAttributes == null) {
            return null;
        }
        return servletAttributes.getRequest();
    }

    /**
     * Description: Object转json字符串
     * Author: jiacheng yang.
     * Date: 2025/02/28 18:39
     * Param: []
     */

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final ObjectMapper objectMapper;

    static{
        objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.setDateFormat(new SimpleDateFormat(DATE_FORMAT));
        // 序列化空值失败时不抛异常
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 反序列化不存在的字段失败时不抛异常
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJson(Object object) {
        if (object == null) {
            return "";
        } else {
            String json = "";

            try {
                json = objectMapper.writeValueAsString(object);
            } catch (JsonProcessingException var3) {
                log.error("json to string error", var3);
            }

            return json;
        }
    }


}
