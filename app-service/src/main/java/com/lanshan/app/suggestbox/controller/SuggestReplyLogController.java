package com.lanshan.app.suggestbox.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.suggestbox.converter.SuggestReplyLogConverter;
import com.lanshan.app.suggestbox.entity.SuggestReplyLog;
import com.lanshan.app.suggestbox.service.SuggestReplyLogService;
import com.lanshan.app.suggestbox.vo.SuggestReplyLogVO;
import com.lanshan.base.api.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 意见回复记录表(SuggestReplyLog)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("suggest/suggestReplyLog")
@Api(tags = "意见回复记录表(SuggestReplyLog)控制层", hidden = true)
public class SuggestReplyLogController {
    /**
     * 服务对象
     */
    @Resource
    private SuggestReplyLogService suggestReplyLogService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<SuggestReplyLogVO>> selectAll(Page<SuggestReplyLogVO> page, SuggestReplyLogVO vo) {
        QueryWrapper<SuggestReplyLog> queryWrapper = new QueryWrapper<>(SuggestReplyLogConverter.INSTANCE.toEntity(vo));
        IPage<SuggestReplyLog> pageData = this.suggestReplyLogService.page(page.convert(SuggestReplyLogConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(SuggestReplyLogConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<SuggestReplyLogVO> selectOne(@PathVariable Serializable id) {
        return Result.build(SuggestReplyLogConverter.INSTANCE.toVO(this.suggestReplyLogService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody SuggestReplyLogVO vo) {
        return Result.build(this.suggestReplyLogService.save(SuggestReplyLogConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update/put")
    public Result<Boolean> update(@RequestBody SuggestReplyLogVO vo) {
        return Result.build(this.suggestReplyLogService.updateById(SuggestReplyLogConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.suggestReplyLogService.removeByIds(idList));
    }
}

