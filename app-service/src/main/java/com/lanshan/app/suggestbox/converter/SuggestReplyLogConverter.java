package com.lanshan.app.suggestbox.converter;


import com.lanshan.app.suggestbox.entity.SuggestReplyLog;
import com.lanshan.app.suggestbox.vo.SuggestReplyLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 意见回复记录表(SuggestReplyLog)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface SuggestReplyLogConverter {

    SuggestReplyLogConverter INSTANCE = Mappers.getMapper(SuggestReplyLogConverter.class);

    SuggestReplyLogVO toVO(SuggestReplyLog entity);

    SuggestReplyLog toEntity(SuggestReplyLogVO vo);

    List<SuggestReplyLogVO> toVO(List<SuggestReplyLog> entityList);

    List<SuggestReplyLog> toEntity(List<SuggestReplyLogVO> voList);
}


