package com.lanshan.app.suggestbox.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 意见箱分类表(SuggestCategory)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@TableName(value = "suggest_category", schema = "suggest_box", autoResultMap = true)
public class SuggestCategory extends Model<SuggestCategory> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 类型 1 事务 2 应用
     */
    private Integer type;
    /**
     * 名称 类型为事务时为事务名 类型为应用时为企微应用主键
     */
    private String name;
    /**
     * 排序号
     */
    private Integer sort;
    /**
     * 负责人用户id
     */
    @TableField(value = "manager_userids", typeHandler = JacksonTypeHandler.class)
    private List<String> managerUserids;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 是否启用 true 启用 false 禁用
     */
    private Boolean enable;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

