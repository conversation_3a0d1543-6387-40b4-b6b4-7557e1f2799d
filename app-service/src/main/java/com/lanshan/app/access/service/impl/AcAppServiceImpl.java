package com.lanshan.app.access.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.access.converter.AcAppConverter;
import com.lanshan.app.access.dao.AcAppDao;
import com.lanshan.app.access.entity.AcApiControl;
import com.lanshan.app.access.entity.AcApp;
import com.lanshan.app.access.entity.AcDataScope;
import com.lanshan.app.access.qo.*;
import com.lanshan.app.access.service.AcApiControlService;
import com.lanshan.app.access.service.AcAppService;
import com.lanshan.app.access.service.AcDataScopeService;
import com.lanshan.app.access.vo.AcAppVO;
import com.lanshan.app.common.constant.AppServiceRedisConstant;
import com.lanshan.app.common.constant.CommonConstant;
import com.lanshan.app.common.enums.ApiControlTypeEnum;
import com.lanshan.app.common.enums.EnableStatusEnum;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.enums.YnEnum;
import com.lanshan.app.common.utils.RedisService;
import com.lanshan.app.common.utils.StpAppUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 接入方应用(AcApp)表服务实现类
 *
 * <AUTHOR>
 */
@Service("acAppService")
public class AcAppServiceImpl extends ServiceImpl<AcAppDao, AcApp> implements AcAppService {

    @Resource
    private AcApiControlService acApiControlService;

    @Resource
    private AcDataScopeService acDataScopeService;

    @Resource
    private RedisService redisService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    /**
     * 项目启动时，初始化APP列表到缓存
     */
    @PostConstruct
    public void initCache() {
        cacheApp();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AcAppQO qo) {
        LambdaQueryWrapper<AcApp> queryWrapper = Wrappers.lambdaQuery(AcApp.class);
        queryWrapper.eq(AcApp::getCompanyId, qo.getCompanyId())
                .eq(AcApp::getAppName, qo.getAppName());
        //查询应用是否存在
        boolean exists = super.exists(queryWrapper);
        if (exists) {
            throw ExceptionCodeEnum.APP_NAME_EXIST.toServiceException();
        }

        //生成keyId（长度24）和secret（长度32）
        String keyId = IdUtil.objectId();
        String secret = IdUtil.fastSimpleUUID();

        //新增应用
        AcApp acApp = AcAppConverter.INSTANCE.toEntity(qo);
        acApp.setKeyId(keyId);
        acApp.setSecret(secret);
        super.save(acApp);

        //新增API权限控制
        addApiControl(qo, acApp.getId());

        //新增数据权限控制
        List<AcDataScopeQO> dataScopeList = qo.getDataScopeList();
        if(CollUtil.isNotEmpty(dataScopeList)) {
            addDataScope(qo.getDataScopeList(), Collections.singletonList(acApp.getId()));
        }

        //异步更新缓存
        commonExecutor.execute(this::cacheApp);
    }

    /**
     * 新增数据权限控制
     */
    private void addDataScope(List<AcDataScopeQO> dataScopeList, List<Long> appIdList) {
        List<AcDataScope> entityList = new ArrayList<>();
        for (Long appId : appIdList) {
            List<AcDataScope> dataScopes = dataScopeList.stream().map(item ->
                    AcDataScope.builder()
                            .dataKey(item.getDataKey())
                            .dataName(item.getDataName())
                            .scopeValue(item.getScopeValue())
                            .appId(appId).build()).collect(Collectors.toList());
            entityList.addAll(dataScopes);
        }
        //批量新增数据权限控制
        acDataScopeService.saveBatch(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AcAppQO qo) {
        AcApp acApp = getById(qo.getId());
        //查询应用是否存在
        if (acApp == null) {
            throw ExceptionCodeEnum.APP_NOT_EXIST.toServiceException();
        }

        //更新应用
        super.updateById(AcAppConverter.INSTANCE.toEntity(qo));

        //处理API权限控制
        processApiControl(qo, acApp.getId());

        //处理数据权限控制
        processDataScope(qo.getDataScopeList(), Collections.singletonList(acApp.getId()));

        //异步更新缓存
        commonExecutor.execute(this::cacheApp);
    }

    @Override
    public IPage<AcAppVO> pageApp(AcAppPageQO pageQO) {
        Page<AcApp> page = new Page<>(pageQO.getPage(), pageQO.getSize());
        LambdaQueryWrapper<AcApp> queryWrapper = Wrappers.lambdaQuery(AcApp.class);

        //操作类型
        if (pageQO.getOperateType() != null) {
            queryWrapper.eq(AcApp::getOperateType, pageQO.getOperateType());
        }
        //应用名称
        if (StringUtils.isNotBlank(pageQO.getAppName())) {
            queryWrapper.like(AcApp::getAppName, pageQO.getAppName());
        }
        //接入方id
        if (pageQO.getCompanyId()!= null) {
            queryWrapper.eq(AcApp::getCompanyId, pageQO.getCompanyId());
        }
        //接入方名称
        if (StringUtils.isNotBlank(pageQO.getCompanyName())) {
            queryWrapper.like(AcApp::getCompanyName, pageQO.getCompanyName());
        }
        //id列表
        if (CollUtil.isNotEmpty(pageQO.getIdList())) {
            queryWrapper.in(AcApp::getId, pageQO.getIdList());
        }
        //apiId
        if (pageQO.getApiId() != null) {
            //查询API对应的应用
            List<AcApiControl> list = acApiControlService.list(Wrappers.lambdaQuery(AcApiControl.class)
                    .eq(AcApiControl::getApiId, pageQO.getApiId())
                    .eq(AcApiControl::getControlType, ApiControlTypeEnum.APP.getCode()));
            if (CollUtil.isEmpty(list)) {
                return new Page<>(pageQO.getPage(), pageQO.getSize());
            }

            queryWrapper.in(AcApp::getId, list.stream().map(AcApiControl::getControlId).collect(Collectors.toList()));
        }

        //按照创建时间倒序排序
        queryWrapper.orderByDesc(AcApp::getCreateTime);

        //分页查询
        IPage<AcApp> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQO.getPage(), pageQO.getSize());
        }

        //转换VO
        IPage<AcAppVO> pageVo = result.convert(AcAppConverter.INSTANCE::toVO);
        for (AcAppVO vo : pageVo.getRecords()) {
            //设置状态
            vo.setStatusDesc(EnumUtil.getFieldBy(EnableStatusEnum::getMsg, EnableStatusEnum::getCode, vo.getStatus()));
            //如果过期，则将状态设置为过期
            if (DateUtil.compare(vo.getEndTime(), DateUtil.date(), DatePattern.NORM_DATE_PATTERN) < 0) {
                vo.setStatusDesc(CommonConstant.EXPIRED);
            }
            //设置是否需要用户授权
            vo.setNeedUserAuthDesc(EnumUtil.getFieldBy(YnEnum::getDescription, YnEnum::getValue, vo.getNeedUserAuth()));
        }

        return pageVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchChangeStatus(BatchChangeStatusQO qo) {
        List<Long> idList = qo.getIdList();
        LambdaUpdateWrapper<AcApp> updateWrapper = Wrappers.lambdaUpdate(AcApp.class)
                .set(AcApp::getStatus, qo.getStatus())
                .in(AcApp::getId, idList);
        //批量改变状态
        super.update(updateWrapper);

        //如果是禁用，则将APP对应的token清除
        for (Long id : idList) {
            StpAppUtil.kickout(id);
        }
    }

    @Override
    public List<AcAppVO> listByDataKey(String dataKey) {
        LambdaQueryWrapper<AcDataScope> queryWrapper = Wrappers.lambdaQuery(AcDataScope.class);
        queryWrapper.eq(AcDataScope::getDataKey, dataKey);
        //根据dataKey查询appId
        List<AcDataScope> dataScopeList = acDataScopeService.list(queryWrapper);
        if(CollUtil.isEmpty(dataScopeList)) {
            return Collections.emptyList();
        }
        List<Long> appIdList =  dataScopeList.stream().map(AcDataScope::getAppId).distinct().collect(Collectors.toList());
        //根据appId不分页查询应用
        AcAppPageQO appPageQO = AcAppPageQO.builder().idList(appIdList).build();
        appPageQO.setSize(Long.MAX_VALUE);
        IPage<AcAppVO> acAppVOIPage = pageApp(appPageQO);

        return acAppVOIPage.getRecords();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSetData(BatchSetDataQO qo) {
        //删除旧的指定数据权限控制
        LambdaQueryWrapper<AcDataScope> queryWrapper = Wrappers.lambdaQuery(AcDataScope.class);
        queryWrapper.in(AcDataScope::getAppId, qo.getAppIdList());
        queryWrapper.eq(AcDataScope::getDataKey, qo.getAcDataScope().getDataKey());
        acDataScopeService.remove(queryWrapper);

        //批量新增数据权限控制
        addDataScope(Collections.singletonList(qo.getAcDataScope()), qo.getAppIdList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delByIdList(List<Long> idList) {
        super.removeByIds(idList);

        //异步更新缓存
        commonExecutor.execute(this::cacheApp);
    }

    @Override
    public List<AcApp> cacheApp() {
        List<AcApp> list = super.list();
        if(CollUtil.isNotEmpty(list)) {
            //缓存到redis
            Map<String, AcApp> apiMap = CollUtil.toMap(list, null, item -> String.valueOf(item.getId()), item -> item);
            redisService.setCacheMap(AppServiceRedisConstant.ACCESS_APP_MAP, apiMap);
        }
        return list;
    }

    @Override
    public List<AcApp> listByCache() {
        List<AcApp> list;
        //判断缓存中是否存数据
        if(Boolean.TRUE.equals(redisService.hasKey(AppServiceRedisConstant.ACCESS_APP_MAP))) {
            //从缓存中获取数据
            Map<String, AcApp> apiMap = redisService.getCacheMap(AppServiceRedisConstant.ACCESS_APP_MAP);
            list = new ArrayList<>(apiMap.values());
        } else {
            //如果缓存中不存在，则从数据库中查询并放入缓存
            list = cacheApp();
        }
        return list;
    }
    /**
     * 批量处理数据权限控制 删除旧数据、新增新数据
     */
    private void processDataScope(List<AcDataScopeQO> dataScopeList, List<Long> appIdList) {
        //删除旧的数据权限控制
        LambdaQueryWrapper<AcDataScope> queryWrapper = Wrappers.lambdaQuery(AcDataScope.class);
        queryWrapper.in(AcDataScope::getAppId, appIdList);
        acDataScopeService.remove(queryWrapper);

        //新增数据权限控制
        if (CollUtil.isNotEmpty(dataScopeList)) {
            addDataScope(dataScopeList, appIdList);
        }
    }

    /**
     * 处理API权限控制 删除旧数据、新增新数据
     */
    private void processApiControl(AcAppQO qo, Long acAppId) {
        LambdaQueryWrapper<AcApiControl> queryWrapper = Wrappers.lambdaQuery(AcApiControl.class)
                .eq(AcApiControl::getControlType, ApiControlTypeEnum.APP.getCode())
                .eq(AcApiControl::getControlId, acAppId);
        //删除旧的API权限控制
        acApiControlService.remove(queryWrapper);

        //新增API权限控制
        addApiControl(qo, acAppId);
    }

    /**
     * 新增API权限控制
     */
    private void addApiControl(AcAppQO qo, Long appId) {
        List<Long> newApiIdList = qo.getApiIdList();
        if (CollUtil.isNotEmpty(newApiIdList)) {
            List<AcApiControl> apiControlList = newApiIdList.stream().map(item ->
                    AcApiControl.builder()
                            .apiId(item)
                            .controlType(ApiControlTypeEnum.APP.getCode())
                            .controlId(appId)
                            .build()).collect(Collectors.toList());
            acApiControlService.saveBatch(apiControlList);
        }
    }
}

