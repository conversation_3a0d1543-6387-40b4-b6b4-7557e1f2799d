package com.lanshan.app.access.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.access.entity.AcApi;
import com.lanshan.app.access.qo.AcApiControlQO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统开放的API(AcApi)表数据库访问层
 *
 * <AUTHOR>
 */
public interface AcApiDao extends BaseMapper<AcApi> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcApi> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AcApi> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcApi> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<AcApi> entities);

    /**
     * 通过控制id、控制类型查询API
     *
     * @param param 查询条件
     * @return List<AcApi>
     */
    List<AcApi> listByControl(@Param("param") AcApiControlQO param);
}

