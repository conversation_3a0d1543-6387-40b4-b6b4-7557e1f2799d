package com.lanshan.app.access.service.impl;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.app.access.dto.ApiAccessInfoDto;
import com.lanshan.app.access.dto.ImgDataScope;
import com.lanshan.app.access.entity.AcDataScope;
import com.lanshan.app.access.qo.AcDataScopeQO;
import com.lanshan.app.access.service.AcDataScopeService;
import com.lanshan.app.access.service.OpenApiService;
import com.lanshan.app.common.constant.DataScopeKeyConstant;
import com.lanshan.app.common.constant.SecurityConstant;
import com.lanshan.app.common.utils.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 开放API接口实现
 */

@Slf4j
@Service
public class OpenApiServiceImpl implements OpenApiService {

    @Resource
    private AcDataScopeService acDataScopeService;

    @Override
    public String test() {
        //从当前线程获取API访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);

        //查询人脸数据源
        AcDataScopeQO scopeQO = AcDataScopeQO.builder()
                .dataKey(DataScopeKeyConstant.IMG)
                .appId(apiAccessInfoDto.getAppId()).build();
        AcDataScope dataScope = acDataScopeService.getDataScope(scopeQO);
        if (dataScope != null) {
            //将数据源转为人脸数据源对象
            List<ImgDataScope> imgDataScope = JacksonUtils.toObj(JacksonUtils.toJson(dataScope.getScopeValue()), new TypeReference<>() {
            });

            return JacksonUtils.toJson(imgDataScope);
        } else {
            return null;
        }
    }
}
