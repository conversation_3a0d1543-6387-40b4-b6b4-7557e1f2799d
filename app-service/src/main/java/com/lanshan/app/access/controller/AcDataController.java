package com.lanshan.app.access.controller;


import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.access.converter.AcDataConverter;
import com.lanshan.app.access.converter.AcDataFieldConverter;
import com.lanshan.app.access.qo.AcDataPageQO;
import com.lanshan.app.access.service.AcDataFieldService;
import com.lanshan.app.access.service.AcDataService;
import com.lanshan.app.access.vo.AcDataFieldVO;
import com.lanshan.app.access.vo.AcDataVO;
import com.lanshan.app.common.bo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权限数据集(AcData)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("acData")
@Api(tags = "数据源控制层", hidden = true)
public class AcDataController {
    /**
     * 服务对象
     */
    @Resource
    private AcDataService acDataService;

    @Resource
    private AcDataFieldService acDataFieldService;

    @GetMapping("pageAcData")
    @ApiOperation("分页查询权限数据集")
    public Result<IPage<AcDataVO>> pageAcData(AcDataPageQO pageQO) {
        return Result.build(acDataService.pageAcData(pageQO));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody AcDataVO vo) {
        return Result.build(this.acDataService.updateById(AcDataConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.acDataService.removeByIds(idList));
    }

    @ApiOperation("通过唯一标识查询数据源")
    @GetMapping("getByDataKey")
    public Result<AcDataVO> getByDataKey(@RequestParam("dataKey") String dataKey) {
        return Result.build(acDataService.getByDataKey(dataKey));
    }

    @ApiOperation("获取学生部门")
    @GetMapping("getStudentDeptTree")
    public Result<Tree<String>> getStudentDeptTree() {
        return Result.build(acDataService.getStudentDeptTree());
    }

    @ApiOperation("根据部门树类型获取树")
    @GetMapping("getDeptTreeByTreeType")
    public Result<Tree<Long>> getDeptTreeByTreeType(@RequestParam("treeType") Integer treeType) {
        return Result.build(acDataService.getDeptTreeByTreeType(treeType));
    }

    @ApiOperation("通过唯一标识查询数据源字段")
    @GetMapping("getDataFiledByDataKey")
    public Result<List<AcDataFieldVO>> getDataFiledByDataKey(@RequestParam("dataKey") String dataKey) {
        return Result.build(AcDataFieldConverter.INSTANCE.toVO(acDataFieldService.listByDataKey(dataKey)));
    }
}

