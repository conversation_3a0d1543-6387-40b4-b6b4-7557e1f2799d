package com.lanshan.app.access.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.access.dto.AcApiCallLogGroupCountDto;
import com.lanshan.app.access.entity.AcApiCallLog;
import com.lanshan.app.access.qo.AcApiCallLogCountQO;
import com.lanshan.app.access.vo.AcApiCallLogTodayCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 系统开放API调用日志(AcApiCallLog)表数据库访问层
 *
 * <AUTHOR>
 */
public interface AcApiCallLogDao extends BaseMapper<AcApiCallLog> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcApiCallLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AcApiCallLog> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcApiCallLog> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<AcApiCallLog> entities);

    /**
     * 查询当天调用日志次数
     * @return 当天调用日志次数
     */
    List<AcApiCallLogTodayCountVO> listTodayCallCount(@Param("param") AcApiCallLogCountQO param);

    /**
     * 查询调用日志分组次数
     * @return 调用日志分组次数
     */
    List<AcApiCallLogGroupCountDto> listCallLogGroupCount(@Param("date") Date date);
}

