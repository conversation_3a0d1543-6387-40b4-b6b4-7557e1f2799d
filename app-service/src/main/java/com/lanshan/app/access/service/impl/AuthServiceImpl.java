package com.lanshan.app.access.service.impl;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.app.access.entity.AcApp;
import com.lanshan.app.access.service.AcAppService;
import com.lanshan.app.access.service.AuthService;
import com.lanshan.app.common.enums.EnableStatusEnum;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.utils.StpAppUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 认证服务
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Resource
    private AcAppService acAppService;

    @Override
    public SaTokenInfo getApiAccessToken(String keyId, String appSecret) {
        //验证keyId和appSecret是否正确
        LambdaQueryWrapper<AcApp> queryWrapper = Wrappers.lambdaQuery(AcApp.class);
        queryWrapper.eq(AcApp::getKeyId, keyId)
                .eq(AcApp::getSecret, appSecret);
        AcApp acApp = acAppService.getOne(queryWrapper);
        if (acApp == null) {
            throw ExceptionCodeEnum.APP_SECRET_ERROR.toServiceException();
        }

        //应用不可用则抛异常
        if(EnableStatusEnum.DISABLE.getCode() == acApp.getStatus()) {
            throw ExceptionCodeEnum.APP_DISABLE.toServiceException();
        }
        //应用已过期则抛异常
        if(DateUtil.compare(acApp.getEndTime(), DateUtil.date(), DatePattern.NORM_DATE_PATTERN) < 0) {
            throw ExceptionCodeEnum.APP_EXPIRED.toServiceException();
        }

        //用当前appId登录
        StpAppUtil.login(acApp.getId());
        //返回token信息
        return StpAppUtil.getTokenInfo();
    }

    @Override
    public String getAppIdFromToken(String token) {
        return (String) StpAppUtil.getLoginIdByToken(token);
    }
}
