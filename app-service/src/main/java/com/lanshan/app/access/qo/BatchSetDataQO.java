package com.lanshan.app.access.qo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "批量设置数据源QO")
@Data
@ToString
public class BatchSetDataQO implements Serializable {
    private static final long serialVersionUID = 3203174907553084171L;

    @ApiModelProperty(value = "appId列表")
    private List<Long> appIdList;

    private AcDataScopeQO acDataScope;
}

