package com.lanshan.app.access.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 接入方应用(AcApp)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "接入方应用VO")
@Data
@ToString
public class AcAppVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "操作类型 1：调用类型 2：调用、推送类型")
    private Integer operateType;

    @ApiModelProperty(value = "应用类型 0：内部应用 1：外部应用")
    private Integer appType;

    @ApiModelProperty(value = "应用所属接入方ID")
    private Long companyId;

    @ApiModelProperty(value = "应用所属接入方名称")
    private String companyName;

    @ApiModelProperty(value = "应用keyId")
    private String keyId;

    @ApiModelProperty(value = "应用访问密钥")
    private String secret;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否需要用户授权 0:否 1:是")
    private Integer needUserAuth;

    @ApiModelProperty(value = "是否需要用户授权 0:否 1:是")
    private String needUserAuthDesc;

    @ApiModelProperty(value = "申请原因")
    private String applicationReason;

    @ApiModelProperty(value = "状态。0：禁用；1：正常")
    private Integer status;

    @ApiModelProperty(value = "状态。0：禁用；1：正常")
    private String statusDesc;

    @ApiModelProperty(value = "有效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "有效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "开放文档地址")
    private String openDocUrl;
}

