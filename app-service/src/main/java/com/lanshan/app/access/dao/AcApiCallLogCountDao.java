package com.lanshan.app.access.dao;

import com.lanshan.app.access.qo.AcApiCallLogCountQO;
import com.lanshan.app.access.vo.AcApiCallLogCountVO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.access.entity.AcApiCallLogCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 系统开放API调用日志次数(AcApiCallLogCount)数据库访问层
 */

@Mapper
public interface AcApiCallLogCountDao extends BaseMapper<AcApiCallLogCount> {

    /**
     * 查询调用日志次数
     * @param param 查询参数
     * @return 调用日志次数
     */
    List<AcApiCallLogCountVO> listCallCount(@Param("param") AcApiCallLogCountQO param);
}

