package com.lanshan.app.access.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.access.entity.AcAppPushLog;
import com.lanshan.app.access.qo.AcAppPushLogCountQO;
import com.lanshan.app.access.qo.AcAppPushLogPageQO;
import com.lanshan.app.access.vo.AcPushLogCountVO;
import com.lanshan.app.access.vo.AcAppPushLogVO;
import com.lanshan.app.access.vo.AcPushLogTodayCountVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 应用数据推送日志(AcAppPushLog)服务接口
 */
public interface AcAppPushLogService extends IService<AcAppPushLog> {

    /**
     * 分页查询推送日志
     *
     * @param pageQO 分页查询参数
     * @return 分页查询结果
     */
    IPage<AcAppPushLogVO> pageAppPushLog(AcAppPushLogPageQO pageQO);

    /**
     * 导出推送日志
     *
     * @param pageQO 导出参数
     * @param response 响应
     * @throws IOException 异常
     */
    void exportAppPushLog(AcAppPushLogPageQO pageQO, HttpServletResponse response) throws IOException;

    /**
     * 查询推送日志次数
     * @param qo 查询参数
     * @return 推送日志次数列表
     */
    List<AcPushLogCountVO> listPushCount(AcAppPushLogCountQO qo);

    /**
     * 查询当天推送日志次数
     * @param qo 查询参数
     * @return 推送日志次数列表
     */
    List<AcPushLogTodayCountVO> listTodayPushCount(AcAppPushLogCountQO qo);

    /**
     * 生成推送日志次数
     * @param dateStr 日期
     */
    void genAppPushLogCount(String dateStr);
}
