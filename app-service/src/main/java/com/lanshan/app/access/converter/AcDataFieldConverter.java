package com.lanshan.app.access.converter;


import com.lanshan.app.access.entity.AcDataField;
import com.lanshan.app.access.vo.AcDataFieldVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 权限数据集字段(AcDataField)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface AcDataFieldConverter {

    AcDataFieldConverter INSTANCE = Mappers.getMapper(AcDataFieldConverter.class);

    AcDataFieldVO toVO(AcDataField entity);

    AcDataField toEntity(AcDataFieldVO vo);

    List<AcDataFieldVO> toVO(List<AcDataField> entityList);

    List<AcDataField> toEntity(List<AcDataFieldVO> voList);
}


