package com.lanshan.app.access.converter;


import com.lanshan.app.access.entity.AcApi;
import com.lanshan.app.access.qo.AcApiQO;
import com.lanshan.app.access.vo.AcApiVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 系统开放的API(AcApi)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface AcApiConverter {

    AcApiConverter INSTANCE = Mappers.getMapper(AcApiConverter.class);

    AcApiVO toVO(AcApi entity);

    AcApi toEntity(AcApiVO vo);

    List<AcApiVO> toVO(List<AcApi> entityList);

    List<AcApi> toEntity(List<AcApiVO> voList);

    AcApi toEntity(AcApiQO vo);
}


