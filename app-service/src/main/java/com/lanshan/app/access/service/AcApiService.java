package com.lanshan.app.access.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.access.entity.AcApi;
import com.lanshan.app.access.qo.AcApiControlQO;
import com.lanshan.app.access.qo.AcApiPageQO;
import com.lanshan.app.access.qo.AcApiQO;
import com.lanshan.app.access.vo.AcApiVO;

import java.util.List;

/**
 * 系统开放的API(AcApi)表服务接口
 *
 * <AUTHOR>
 */
public interface AcApiService extends IService<AcApi> {

    /**
     * 新增API
     *
     * @param qo 新增API入参
     */
    void save(AcApiQO qo);

    /**
     * 修改API
     *
     * @param qo 修改API入参
     */
    void update(AcApiQO qo);

    /**
     * 根据分组id查询
     * @param groupId 分组id
     * @return API列表
     */
    List<AcApiVO> listByGroupId(Long groupId);

    /**
     * 分页查询API
     * @param pageQO 分页查询入参
     * @return API列表
     */
    IPage<AcApiVO> pageApi(AcApiPageQO pageQO);

    /**
     * 通过控制id、控制类型查询API
     * @param qo 查询条件
     * @return API列表
     */
    List<AcApiVO> listByControl(AcApiControlQO qo);

    /**
     * 缓存API
     */
    List<AcApi> cacheApi();

    /**
     * 通过缓存查询所有API
     * @return API列表
     */
    List<AcApi> listByCache();

    /**
     * 删除API
     * @param idList API id列表
     */
    void delByIds(List<Long> idList);
}

