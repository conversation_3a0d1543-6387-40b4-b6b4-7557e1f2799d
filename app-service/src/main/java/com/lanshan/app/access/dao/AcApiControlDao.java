package com.lanshan.app.access.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.access.dto.ApiAccessInfoDto;
import com.lanshan.app.access.entity.AcApiControl;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统开放API权限控制(AcApiControl)表数据库访问层
 *
 * <AUTHOR>
 */
public interface AcApiControlDao extends BaseMapper<AcApiControl> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcApiControl> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AcApiControl> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<AcApiControl> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<AcApiControl> entities);

    /**
     * 根据控制类型、控制id、路径查询API访问信息
     *
     * @param controlType 控制类型
     * @param controlId   控制ID
     * @param path        路径
     * @return 访问信息
     */
    ApiAccessInfoDto getApiAccessInfoByControlAndPath(Integer controlType, Long controlId, String path);
}

