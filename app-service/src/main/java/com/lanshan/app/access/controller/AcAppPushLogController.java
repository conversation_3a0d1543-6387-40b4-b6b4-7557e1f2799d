package com.lanshan.app.access.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.access.converter.AcAppPushOperateTypeConverter;
import com.lanshan.app.access.qo.AcAppPushLogCountQO;
import com.lanshan.app.access.qo.AcAppPushLogPageQO;
import com.lanshan.app.access.service.AcAppPushLogService;
import com.lanshan.app.access.service.AcAppPushOperateTypeService;
import com.lanshan.app.access.vo.AcAppPushLogVO;
import com.lanshan.app.access.vo.AcAppPushOperateTypeVO;
import com.lanshan.app.access.vo.AcPushLogCountVO;
import com.lanshan.app.access.vo.AcPushLogTodayCountVO;
import com.lanshan.app.common.bo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.simpleframework.xml.core.Validate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("acAppPushLog")
@Api(tags = "推送日志控制层", hidden = true)
public class AcAppPushLogController {

    @Resource
    private AcAppPushLogService acAppPushLogService;

    @Resource
    private AcAppPushOperateTypeService acPushOperateTypeService;

    @GetMapping("pageAppPushLog")
    @ApiOperation("分页查询推送日志")
    public Result<IPage<AcAppPushLogVO>> pageAppPushLog(AcAppPushLogPageQO pageQO) {
        return Result.build(acAppPushLogService.pageAppPushLog(pageQO));
    }

    @ApiOperation("导出推送日志")
    @PostMapping(value = "exportAppPushLog")
    public void exportUser(@RequestBody AcAppPushLogPageQO pageQO, HttpServletResponse response) throws IOException {
        acAppPushLogService.exportAppPushLog(pageQO, response);
    }

    @GetMapping("listPushCount")
    @ApiOperation("查询推送日志次数")
    public Result<List<AcPushLogCountVO>> listPushCount(AcAppPushLogCountQO qo) {
        return Result.build(acAppPushLogService.listPushCount(qo));
    }

    @GetMapping("listOperateTypeByAppId")
    @ApiOperation("根据appId查询操作类型")
    public Result<List<AcAppPushOperateTypeVO>> listOperateTypeByAppId(@Validate @RequestParam @NotBlank(message = "appId不能为空") Long appId) {
        return Result.build(AcAppPushOperateTypeConverter.INSTANCE.toVO(acPushOperateTypeService.listOperateTypeByAppId(appId)));
    }

    @GetMapping("listTodayCallCount")
    @ApiOperation("查询当天推送日志次数")
    public Result<List<AcPushLogTodayCountVO>> listTodayPushCount(AcAppPushLogCountQO qo) {
        return Result.build(acAppPushLogService.listTodayPushCount(qo));
    }
}

