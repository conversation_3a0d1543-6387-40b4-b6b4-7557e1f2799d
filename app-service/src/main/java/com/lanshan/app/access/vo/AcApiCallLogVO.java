package com.lanshan.app.access.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统开放API调用日志(AcApiCallLog)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "系统开放API调用日志VO")
@Data
@ToString
public class AcApiCallLogVO implements Serializable {

    private static final long serialVersionUID = -215428800556704430L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "接口ID")
    private Long apiId;

    @ApiModelProperty(value = "接口名称")
    private String apiName;

    @ApiModelProperty(value = "接口调用接入方ID")
    private Long companyId;

    @ApiModelProperty(value = "接口调用应用ID")
    private Long appId;

    @ApiModelProperty(value = "接口调用请求参数")
    private String requestParam;

    @ApiModelProperty(value = "接口调用结果状态")
    private Integer responseStatus;

    @ApiModelProperty("接口调用结果状态")
    private String responseStatusDesc;

    @ApiModelProperty(value = "接口调用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("接口调用结果")
    private String result;
}

