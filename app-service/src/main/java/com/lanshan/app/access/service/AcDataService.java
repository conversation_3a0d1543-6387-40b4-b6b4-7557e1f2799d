package com.lanshan.app.access.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.access.entity.AcData;
import com.lanshan.app.access.qo.AcDataPageQO;
import com.lanshan.app.access.vo.AcDataVO;

/**
 * 权限数据集(AcData)表服务接口
 *
 * <AUTHOR>
 */
public interface AcDataService extends IService<AcData> {

    /**
     * 通过唯一标识获取数据权限
     *
     * @param dataKey 限唯一标识
     * @return 数据权限
     */
    AcDataVO getByDataKey(String dataKey);

    /**
     * 获取学生部门树
     *
     * @return 学生部门树
     */
    Tree<String> getStudentDeptTree();


    /**
     * 分页查询权限数据集
     * @param pageQO 分页查询条件
     * @return 权限数据集
     */
    IPage<AcDataVO> pageAcData(AcDataPageQO pageQO);

    /**
     * 根据部门树类型获取树
     * @param treeType 树类型
     * @return 部门树
     */
    Tree<Long> getDeptTreeByTreeType(Integer treeType);

    /**
     * 生成学生部门到缓存
     * @return 学生部门树
     */
    Tree<String> cacheStudentDept();
}

