package com.lanshan.app.news.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 新闻类型(NewsType)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class NewsType extends Model<NewsType> {
    private static final long serialVersionUID = 420502773531974299L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 类型名称
     */
    private String name;
    /**
     * 排序号
     */
    private Integer sort;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 是否删除。true:删除
     */
    private Boolean isDeleted;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

