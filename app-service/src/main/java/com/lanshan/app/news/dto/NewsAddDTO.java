package com.lanshan.app.news.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "新闻信息新增DTO")
public class NewsAddDTO implements Serializable {
    private static final long serialVersionUID = 6741972008206881285L;

    @ApiModelProperty(value = "新闻来源地址")
    private String srcUrl;

    @ApiModelProperty(value = "新闻来源分类")
    private String srcType;

    @ApiModelProperty(value = "新闻标题")
    private String title;

    @ApiModelProperty(value = "新闻内容")
    private String content;

    @ApiModelProperty(value = "新闻发布时间。格式：yyyy-MM-dd HH:mm")
    private String publishTime;
}
