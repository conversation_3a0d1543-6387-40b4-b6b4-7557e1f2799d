package com.lanshan.app.news.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.news.converter.NewsInformationConverter;
import com.lanshan.app.news.dto.NewsAddDTO;
import com.lanshan.app.news.entity.NewsInformation;
import com.lanshan.app.news.service.NewsInformationService;
import com.lanshan.app.news.vo.NewsInformationVO;
import com.lanshan.base.api.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 新闻信息表(NewsInformation)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("manage/news")
@Api(tags = "新闻API", hidden = true)
public class NewsManageController {
    /**
     * 服务对象
     */
    @Resource
    private NewsInformationService newsInformationService;

    @PostMapping("add")
    @ApiOperation("新增新闻信息")
    public Result<Boolean> addNews(NewsAddDTO news) {
        return Result.build(newsInformationService.addNews(List.of(news)));
    }

    @PostMapping("updateSummary")
    @ApiOperation("更新新闻的摘要")
    public Result<Boolean> updateSummary() {
        return Result.build(newsInformationService.updateSummary());
    }

    @PostMapping("createIndex")
    @ApiOperation("创建索引")
    public Result<Boolean> createIndex() {
        return Result.build(newsInformationService.createIndex());
    }

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<NewsInformationVO>> selectAll(Page<NewsInformationVO> page, NewsInformationVO vo) {
        QueryWrapper<NewsInformation> queryWrapper = new QueryWrapper<>(NewsInformationConverter.INSTANCE.toEntity(vo));
        IPage<NewsInformation> pageData = this.newsInformationService.page(page.convert(NewsInformationConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(NewsInformationConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<NewsInformationVO> selectOne(@PathVariable Serializable id) {
        return Result.build(NewsInformationConverter.INSTANCE.toVO(this.newsInformationService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> insert(@RequestBody NewsInformationVO vo) {
        return Result.build(this.newsInformationService.save(NewsInformationConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PostMapping("/put")
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody NewsInformationVO vo) {
        return Result.build(this.newsInformationService.updateById(NewsInformationConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.newsInformationService.removeByIds(idList));
    }
}

