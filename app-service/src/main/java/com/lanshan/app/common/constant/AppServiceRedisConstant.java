package com.lanshan.app.common.constant;

public class AppServiceRedisConstant {

    private AppServiceRedisConstant() {
    }

    /**
     * 学生部门 redis key
     */
    public static final String APP_SERVICE_STUDENT_DEPT = "APP_SERVICE:STUDENT_DEPT";

    /**
     * API map redis key
     */
    public static final String ACCESS_API_MAP = "ACCESS:API_MAP";

    /**
     * APP map redis key
     */
    public static final String ACCESS_APP_MAP = "ACCESS:APP_MAP";

    /**
     * API权限控制 根据类型和接口id分组 map redis key
     */
    public static final String ACCESS_API_CONTROL_MAP_BY_TYPE_AND_API_ID = "ACCESS:API_CONTROL_MAP_BY_TYPE_AND_API_ID";

    /**
     * API权限控制 根据类型和控制id分组 map redis key
     */
    public static final String ACCESS_API_CONTROL_MAP_BY_TYPE_AND_CONTROL_ID = "ACCESS:API_CONTROL_MAP_BY_TYPE_AND_CONTROL_ID";

    /**
     * 跳蚤市场商品类型 map redis key
     */
    public static final String FLEA_COMMODITY_TYPE_MAP = "FLEA:COMMODITY_TYPE_MAP";


}
