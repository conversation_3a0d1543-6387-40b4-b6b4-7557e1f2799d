package com.lanshan.app.common.constant;

/**
 * 权限相关通用常量
 *
 * <AUTHOR>
 */
public class SecurityConstant {
    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "userId";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "fromSource";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 用户标识
     */
    public static final String TOKEN_KEY = "tokenKey";

    /**
     * 登录用户
     */
    public static final String LOGIN_USER = "loginUser";

    /**
     * 用户角色
     */
    public static final String ROLES = "roles";

    /**
     * token存在头部的KEY
     */
    public static final String TOKEN_HEADER = "Authorization";

    /**
     * token的前缀
     */
    public static final String BEARER = "Bearer ";

    /**
     * token返回的值中的key
     */
    public static final String RES_TOKEN_KEY = "token";

    /**
     * expiresIn返回的值中的key
     */
    public static final String EXPIRED_TIME_KEY = "expiresIn";

    /**
     * refresh token在返回值中的key
     */
    public static final String RES_REFRESH_TOKEN_KEY = "refreshToken";

    /**
     * 请求开始时间
     */
    public static final String REQUEST_START_TIME = "requestStartTime";

    /**
     * api访问信息 包含接入方、应用
     */
    public static final String API_ACCESS_INFO = "apiAccessInfo";
}
