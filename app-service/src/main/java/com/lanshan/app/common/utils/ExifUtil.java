package com.lanshan.app.common.utils;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.imaging.Imaging;
import org.apache.commons.imaging.common.ImageMetadata;
import org.apache.commons.imaging.formats.jpeg.JpegImageMetadata;
import org.apache.commons.imaging.formats.jpeg.exif.ExifRewriter;
import org.apache.commons.imaging.formats.tiff.TiffImageMetadata;
import org.apache.commons.imaging.formats.tiff.constants.ExifTagConstants;
import org.apache.commons.imaging.formats.tiff.write.TiffOutputDirectory;
import org.apache.commons.imaging.formats.tiff.write.TiffOutputSet;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

@Slf4j
public class ExifUtil {
    // 签名码
    private static final String SIGN_CODE = "lanshan-wx";


    public static BufferedImage readImage(String imagePath) throws IOException {
        return ImageIO.read(new File(imagePath));
    }

    /**
     * 给图片添加水印
     *
     * @param srcFile   源文件
     * @param watermark 水印内容
     * @return 目标文件
     */
    public static File addWatermark(File srcFile, String watermark) {
        File targetFile = FileUtil.touch(FileUtil.createTempFile().getParent() + File.separator + IdUtil.simpleUUID() + File.separator + srcFile.getName());
        try (BufferedOutputStream outputStream = new BufferedOutputStream(new FileOutputStream(targetFile))) {
            //获取ImageMetadata对象实例
            ImageMetadata metadata = Imaging.getMetadata(srcFile);
            TiffOutputSet tiffOutputSet;
            if (metadata != null) {
                //强转为JpegImageMetadata
                JpegImageMetadata jpegMetadata = (JpegImageMetadata) metadata;
                //获取TiffImageMetadata
                TiffImageMetadata exif = jpegMetadata.getExif();

                //转换为流
                tiffOutputSet = exif.getOutputSet();
            } else {
                tiffOutputSet = new TiffOutputSet();
            }
            //获取TiffOutputDirectory
            TiffOutputDirectory exifDirectory = tiffOutputSet.getOrCreateExifDirectory();

            // 完整暗水印信息： 当前人信息 + 当前时间
            String fullWatermark = watermark + "@" + DateUtil.now();
            //加入暗水印信息
            exifDirectory.removeField(ExifTagConstants.EXIF_TAG_USER_COMMENT);
            exifDirectory.removeField(ExifTagConstants.EXIF_TAG_OWNER_NAME);
            exifDirectory.add(ExifTagConstants.EXIF_TAG_USER_COMMENT, fullWatermark);
            exifDirectory.add(ExifTagConstants.EXIF_TAG_OWNER_NAME, DigestUtil.md5Hex(fullWatermark + SIGN_CODE));

            //写入新的图片
            new ExifRewriter().updateExifMetadataLossless(srcFile, outputStream, tiffOutputSet);

            return targetFile;

        } catch (Exception e) {
            //很多图片可能读取exif出现异常为正常现象 通常无需处理
            log.error("添加图片水印失败", e);
        }
        return srcFile;
    }

    /**
     * 获取图片暗水印信息
     *
     * @param file 图片文件
     * @return 按水印信息
     */
    public static String getImageWatermarkInfo(File file) {
        //获取ImageMetadata对象实例
        ImageMetadata metadata;
        try {
            metadata = Imaging.getMetadata(file);
            //强转为JpegImageMetadata
            JpegImageMetadata jpegMetadata = (JpegImageMetadata) metadata;
            //获取TiffImageMetadata
            TiffImageMetadata exif = jpegMetadata.getExif();
            // 读取和验证签名
            String waterMark = exif.findField(ExifTagConstants.EXIF_TAG_USER_COMMENT).getStringValue();
            String sign = exif.findField(ExifTagConstants.EXIF_TAG_OWNER_NAME).getStringValue();
            if (StringUtils.equals(sign, DigestUtil.md5Hex(waterMark + SIGN_CODE))) {
                return waterMark;
            }
        } catch (Exception e) {
            log.error("获取图片水印信息失败", e);
        }
        return StringUtils.EMPTY;
    }
}
