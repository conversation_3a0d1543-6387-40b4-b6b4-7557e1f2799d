package com.lanshan.app.message.interfaces;

import com.lanshan.app.common.bo.Result;
import com.lanshan.app.message.constant.ServiceConstant;
import com.lanshan.app.message.interfaces.fallback.IFeignMessageInterfaceFallbackFactory;
import com.lanshan.app.message.qo.SendMessageQo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description: Message应用feign调用
 * @ProjectName: capital-platform-base
 * @Package: com.lanshan.feign.interfaces
 * @ClassName: IFeignMessageInterface
 * @Author: lskj
 * @Date: 2024/01/05 10:26
 * @Version: 1.0
 */
@FeignClient(name = ServiceConstant.MESSAGE_SERVICE, contextId = "IFeignMessageInterfaceFeignClient"
        , fallbackFactory = IFeignMessageInterfaceFallbackFactory.class)
public interface IFeignMessageInterface {

    /** 发送应用消息接口 */
    @PostMapping(value = ServiceConstant.MESSAGE_SERVICE_CONTEXT_PATH + ServiceConstant.MESSAGE_SERVICE_FEIGN_PATH + "/sendMessage")
    Result<Void> sendMessage(@RequestBody SendMessageQo sendMessageQo);
}
