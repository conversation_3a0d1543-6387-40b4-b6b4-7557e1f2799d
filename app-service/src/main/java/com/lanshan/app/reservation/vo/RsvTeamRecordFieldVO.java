package com.lanshan.app.reservation.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 团体预约的场地信息(RsvTeamRecordField)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "团体预约的场地信息VO")
@Data
@ToString
public class RsvTeamRecordFieldVO implements Serializable {

    private static final long serialVersionUID = -6025877295716181468L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "团体预约Id")
    private Long teamRecordId;

    @ApiModelProperty(value = "场地ID")
    private Long fieldId;

    @ApiModelProperty(value = "预约时的场地名称")
    private String fieldName;
}

