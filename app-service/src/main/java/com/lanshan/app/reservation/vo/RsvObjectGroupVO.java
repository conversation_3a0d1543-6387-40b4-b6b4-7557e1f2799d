package com.lanshan.app.reservation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预约对象分组信息VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "预约对象分组信息VO")
public class RsvObjectGroupVO implements Serializable {
    private static final long serialVersionUID = 3241352427095779832L;

    @ApiModelProperty(value = "场地名称")
    private String fieldName;

    @ApiModelProperty(value = "预约对象列表")
    private List<RsvReserveObjectVO> reserveObjectList;
}
