package com.lanshan.app.reservation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.reservation.dto.ReserveObjectSearchDTO;
import com.lanshan.app.reservation.dto.RsvObjectEnableDTO;
import com.lanshan.app.reservation.entity.RsvReserveObject;
import com.lanshan.app.reservation.vo.ReserveTypeVO;
import com.lanshan.app.reservation.vo.RsvObjectGroupVO;
import com.lanshan.app.reservation.vo.RsvReserveObjectVO;
import com.lanshan.app.reservation.vo.UserAvailableDateVO;

import java.io.Serializable;
import java.util.List;

/**
 * 预约对象信息表(RsvReserveObject)表服务接口
 *
 * <AUTHOR>
 */
public interface RsvReserveObjectService extends IService<RsvReserveObject> {

    /**
     * 获取可用预约对象列表
     *
     * @param id   场馆ID
     * @param type 场地类型。
     * @param date 日期
     * @return 预约对象列表
     */
    List<RsvObjectGroupVO> getAvailableObjectList(Serializable id, Long type, String date);

    /**
     * 获取可用预约对象数量
     *
     * @param id 场馆ID
     * @return 可用预约对象数量
     */
    List<ReserveTypeVO> getAvailableCountByType(Serializable id);

    /**
     * 分页获取预约对象列表
     *
     * @param dto 查询条件
     * @return 预约对象列表
     */
    IPage<RsvReserveObjectVO> getPage(ReserveObjectSearchDTO dto);

    /**
     * 启用/禁用预约对象
     *
     * @param id 预约对象ID
     * @return 是否成功
     */
    Boolean switchEnable(Serializable id);

    /**
     * 获取用户可预约场馆日期
     *
     * @param id 场馆ID
     * @return 可用日期列表
     */
    List<UserAvailableDateVO> getUserAvailableDate(Serializable id);

    /**
     * 批量启用/禁用预约对象
     *
     * @param dto 预约对象ID列表
     */
    void switchEnable(RsvObjectEnableDTO dto);

    /**
     * 从通讯录，根据用户ID获取用户姓名
     *
     * @param userId 用户ID
     * @return 用户姓名
     */
    String getUserNameFromAddressBook(String userId);

    /**
     * 获取用户类型
     *
     * @param userId 用户ID
     * @return 用户类型 1-教职工，2-学生
     */
    Integer getUserType(String userId);
}

