package com.lanshan.app.reservation.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.reservation.converter.RsvTeamRecordApprovalConverter;
import com.lanshan.app.reservation.dao.RsvTeamRecordApprovalDao;
import com.lanshan.app.reservation.dto.TeamRsvSpDTO;
import com.lanshan.app.reservation.entity.RsvTeamRecord;
import com.lanshan.app.reservation.entity.RsvTeamRecordApproval;
import com.lanshan.app.reservation.entity.RsvTeamRecordLesson;
import com.lanshan.app.reservation.service.*;
import com.lanshan.app.reservation.util.ReserveUtil;
import com.lanshan.app.reservation.vo.RsvTeamRecordApprovalVO;
import com.lanshan.base.api.dto.message.TextMsgBody;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.feign.message.MessageFeign;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 团体预约审批信息(RsvTeamRecordApproval)表服务实现类
 *
 * <AUTHOR>
 */
@Service("rsvTeamRecordApprovalService")
@RequiredArgsConstructor
@Slf4j
public class RsvTeamRecordApprovalServiceImpl extends ServiceImpl<RsvTeamRecordApprovalDao, RsvTeamRecordApproval> implements RsvTeamRecordApprovalService {

    final private RsvReserveObjectService rsvReserveObjectService;
    final private ISysConfigService sysConfigService;
    final private MessageFeign messageFeign;
    final private RsvReserveRecordService rsvReserveRecordService;

    private final RsvTeamRecordLessonService rsvTeamRecordLessonService;

    /**
     * 审批预约
     *
     * @param dto                   审批信息
     * @param rsvTeamRecordService 预约记录服务
     */
    @Override
    public void spTeamRecord(TeamRsvSpDTO dto, RsvTeamRecordService rsvTeamRecordService) {
        String currentUserId = SecurityContextHolder.getUserIdStr();
        Long id = dto.getId();
        Integer status = dto.getStatus();
        //查询当前审批人的审批信息
        RsvTeamRecordApproval approval = super.getOne(
                Wrappers.<RsvTeamRecordApproval>lambdaQuery()
                        .eq(RsvTeamRecordApproval::getTeamRecordId, id)
                        .eq(RsvTeamRecordApproval::getUserId, currentUserId)
                        .eq(RsvTeamRecordApproval::getSpStatus, 1)
        );

        if (approval == null) {
            throw ExceptionCodeEnum.BAD_REQUEST.toServiceException();
        }
        //更新当前审批人的审批状态
        approval.setSpStatus(status);
        approval.setSpTime(new Date());
        approval.setSpRemark(dto.getReason());
        super.updateById(approval);

        RsvTeamRecord teamRecord = rsvTeamRecordService.getById(id);
        // 如果审批通过，则进行下个审批节点
        if (status == 2) {
            List<RsvTeamRecordApproval> list = super.list(Wrappers.<RsvTeamRecordApproval>lambdaQuery()
                    .eq(RsvTeamRecordApproval::getTeamRecordId, id)
                    .eq(RsvTeamRecordApproval::getSpSeq, approval.getSpSeq() + 1));
            // 如果没有下个审批节点，则更新预约状态为已通过
            if (list == null || list.isEmpty()) {
                rsvTeamRecordService.update(Wrappers.<RsvTeamRecord>lambdaUpdate()
                        .eq(RsvTeamRecord::getId, id)
                        .set(RsvTeamRecord::getStatus, 2)
                        .set(RsvTeamRecord::getUpdateTime, new Date()));
                sendWxMessage(teamRecord.getUserId(), "您的场馆预约申请已通过！");
                List<Pair<Date, Date>> periodList = new ArrayList<>();
                List<RsvTeamRecordLesson> lessonList = rsvTeamRecordLessonService.list(Wrappers.<RsvTeamRecordLesson>lambdaQuery().eq(RsvTeamRecordLesson::getTeamRecordId, id));
                if (CollUtil.isNotEmpty(lessonList)) {
                    for (RsvTeamRecordLesson rsvTeamRecordLesson : lessonList) {
                        periodList.addAll(ReserveUtil.getPeriodList(teamRecord.getType(), teamRecord.getStartDate(), teamRecord.getEndDate(), teamRecord.getDayOfWeek(), rsvTeamRecordLesson.getStartTime(), rsvTeamRecordLesson.getEndTime(), true));
                    }
                } else {
                    periodList = ReserveUtil.getPeriodList(teamRecord.getType(), teamRecord.getStartDate(), teamRecord.getEndDate(), teamRecord.getDayOfWeek(), teamRecord.getStartTime(), teamRecord.getEndTime(), false);
                }
                // 处理个人预约冲突记录
                Set<String> userIdSet = rsvReserveRecordService.handleTeamConflictRecord(teamRecord.getVenueId(), periodList);
                if (!userIdSet.isEmpty()) {
                    sendWxMessage(StringUtils.join(userIdSet, "|"), "因学校活动安排，您的场馆预约被取消。");
                }
            } else {
                // 将下个节点的状态改为待审批，并发送消息通知下个审批人
                super.update(Wrappers.<RsvTeamRecordApproval>lambdaUpdate()
                        .eq(RsvTeamRecordApproval::getTeamRecordId, id)
                        .eq(RsvTeamRecordApproval::getSpSeq, approval.getSpSeq() + 1)
                        .set(RsvTeamRecordApproval::getSpStatus, 1));
                // 发通知给下个审批人
                for (RsvTeamRecordApproval apr : list) {
                    sendWxMessage(apr.getUserId(), "您有1条场馆预约审批，请及时处理！");
                }
            }
        } else {
            // 审批不通过，则更新预约状态为已驳回
            teamRecord.setStatus(3);
            teamRecord.setUpdateTime(new Date());
            rsvTeamRecordService.updateById(teamRecord);
            sendWxMessage(teamRecord.getUserId(), "您的场馆预约申请已驳回！");
        }
    }

    /**
     * 初始化预约审批信息
     *
     * @param teamRecordId 预约流水号
     * @param userId       审批人ID
     */
    @Override
    public void initTeamRecordApproval(Long teamRecordId, String userId) {
        //4.1提交人的上级领导审批
        List<RsvTeamRecordApproval> approvals = new ArrayList<>();
        RsvTeamRecordApproval approval1 = new RsvTeamRecordApproval();
        approval1.setTeamRecordId(teamRecordId);
        approval1.setSpSeq(1);
        approval1.setSpType(1);
        approval1.setSpStatus(1);
        approval1.setUserId(userId);
        approval1.setUserName(rsvReserveObjectService.getUserNameFromAddressBook(userId));
        approvals.add(approval1);

        // 4.2场馆管理员审批
        String managerUserId = sysConfigService.selectConfigByKey("reservation.manager.id");
        if (StringUtils.isNotBlank(managerUserId)) {
            RsvTeamRecordApproval approval2 = new RsvTeamRecordApproval();
            approval2.setTeamRecordId(teamRecordId);
            approval2.setSpSeq(2);
            approval2.setSpType(1);
            approval2.setSpStatus(0);
            approval2.setUserId(managerUserId);
            approval2.setUserName(rsvReserveObjectService.getUserNameFromAddressBook(managerUserId));
            approvals.add(approval2);
        }
        super.saveBatch(approvals);

        // 5 发送消息通知审批人
        sendWxMessage(userId, "您有1条场馆预约审批，请及时处理！");
    }

    /**
     * 取消预约审批
     *
     * @param id 预约流水号
     */
    @Override
    public void cancelApproval(Long id) {
        super.update(Wrappers.<RsvTeamRecordApproval>lambdaUpdate()
                .eq(RsvTeamRecordApproval::getTeamRecordId, id)
                .in(RsvTeamRecordApproval::getSpStatus, 0, 1)
                .set(RsvTeamRecordApproval::getSpStatus, 9)
                .set(RsvTeamRecordApproval::getSpRemark, "预约已取消"));
    }

    /**
     * 获取审批状态为指定状态的预约流水号列表
     *
     * @param status 审批状态。1.待审批 2.已审批
     * @return 预约流水号列表
     */
    @Override
    public Set<Long> getRecordIdListByStatus(Integer status) {

        List<RsvTeamRecordApproval> list;
        if (status == 1) {
            list = super.list(Wrappers.<RsvTeamRecordApproval>lambdaQuery()
                    .eq(RsvTeamRecordApproval::getUserId, SecurityContextHolder.getUserIdStr())
                    .eq(RsvTeamRecordApproval::getSpStatus, 1)
                    .select(RsvTeamRecordApproval::getTeamRecordId));
        } else {
            list = super.list(Wrappers.<RsvTeamRecordApproval>lambdaQuery()
                    .eq(RsvTeamRecordApproval::getUserId, SecurityContextHolder.getUserIdStr())
                    .in(RsvTeamRecordApproval::getSpStatus, 2, 3, 4)
                    .select(RsvTeamRecordApproval::getTeamRecordId));
        }

        return list.stream().map(RsvTeamRecordApproval::getTeamRecordId).collect(Collectors.toSet());
    }

    /**
     * 发送微信消息
     *
     * @param userId  用户ID
     * @param content 消息内容
     */
    @Override
    public void sendWxMessage(String userId, String content) {
        TextMsgBody textMsgBody = new TextMsgBody();
        textMsgBody.setMsgType("text");
        textMsgBody.setContent(content);
        textMsgBody.setToUser(userId);
        String corpId = sysConfigService.selectConfigByKey("corpId");
        String agentId = sysConfigService.selectConfigByKey("reservationAgentId");
        if (StringUtils.isNotBlank(corpId) && StringUtils.isNotBlank(agentId)) {
            textMsgBody.setAgentId(Integer.valueOf(agentId));
            try {
                messageFeign.sendText(corpId, agentId, textMsgBody);
            } catch (Exception e) {
                log.info("发送企业微信消息失败，corpId:{},agentId:{},msgBody:{}", corpId, agentId, textMsgBody);
                log.error("发送企业微信消息失败", e);
            }
        }
    }

    /**
     * 根据预约流水号获取审批信息
     *
     * @param recordId 预约流水号
     */
    @Override
    public List<RsvTeamRecordApprovalVO> getByRecordId(Serializable recordId) {
        List<RsvTeamRecordApproval> list = super.list(Wrappers.<RsvTeamRecordApproval>lambdaQuery()
                .eq(RsvTeamRecordApproval::getTeamRecordId, recordId)
                .orderByAsc(RsvTeamRecordApproval::getSpSeq));
        return RsvTeamRecordApprovalConverter.INSTANCE.toVO(list);
    }

    /**
     * 根据预约流水号获取审批信息映射
     *
     * @param recordIds 团体预约主键集合
     * @return Map<Long, List < RsvTeamRecordApprovalVO>>
     */
    @Override
    public Map<Long, List<RsvTeamRecordApprovalVO>> getMapByRecordIds(Collection<Long> recordIds) {
        List<RsvTeamRecordApproval> list = super.list(Wrappers.<RsvTeamRecordApproval>lambdaQuery()
                .in(RsvTeamRecordApproval::getTeamRecordId, recordIds)
                .orderByAsc(RsvTeamRecordApproval::getTeamRecordId, RsvTeamRecordApproval::getSpSeq));
        return Optional.ofNullable(list).orElseGet(ArrayList::new).stream()
                .map(RsvTeamRecordApprovalConverter.INSTANCE::toVO)
                .collect(Collectors.groupingBy(RsvTeamRecordApprovalVO::getTeamRecordId));
    }
}

