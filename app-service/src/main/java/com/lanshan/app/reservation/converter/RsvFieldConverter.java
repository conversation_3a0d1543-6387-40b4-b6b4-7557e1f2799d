package com.lanshan.app.reservation.converter;


import com.lanshan.app.reservation.entity.RsvField;
import com.lanshan.app.reservation.vo.RsvFieldVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 场地信息(RsvField)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface RsvFieldConverter {

    RsvFieldConverter INSTANCE = Mappers.getMapper(RsvFieldConverter.class);

    RsvFieldVO toVO(RsvField entity);

    RsvField toEntity(RsvFieldVO vo);

    List<RsvFieldVO> toVO(List<RsvField> entityList);

    List<RsvField> toEntity(List<RsvFieldVO> voList);
}


