package com.lanshan.app.reservation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 可预约场馆类型信息VO
 */
@Data
@ApiModel(value = "可预约场馆类型信息VO")
@Builder
public class ReserveTypeVO implements Serializable {

    private static final long serialVersionUID = 8308268262795855664L;

    @ApiModelProperty(value = "预约类型")
    private Long reserveType;

    @ApiModelProperty(value = "预约类型名称")
    private String reserveTypeName;

    @ApiModelProperty(value = "可预约开始时间")
    private String reserveDateStart;

    @ApiModelProperty(value = "可预约结束时间")
    private String reserveDateEnd;

    @ApiModelProperty(value = "可预约余位")
    private Integer remainCount;

    @ApiModelProperty(value = "场地类型图标")
    private String fieldTypeIconUrl;
}
