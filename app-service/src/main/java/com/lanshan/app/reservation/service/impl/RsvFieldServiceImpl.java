package com.lanshan.app.reservation.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.reservation.converter.RsvFieldConverter;
import com.lanshan.app.reservation.dao.RsvFieldDao;
import com.lanshan.app.reservation.entity.RsvField;
import com.lanshan.app.reservation.service.RsvFieldService;
import com.lanshan.app.reservation.vo.RsvFieldVO;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 场地信息(RsvField)表服务实现类
 *
 * <AUTHOR>
 */
@Service("rsvFieldService")
public class RsvFieldServiceImpl extends ServiceImpl<RsvFieldDao, RsvField> implements RsvFieldService {

    /**
     * 查询场馆内的场地信息列表
     *
     * @param id 场馆ID
     * @return List<RsvFieldVO>
     */
    @Override
    public List<RsvFieldVO> getByVenueId(Serializable id) {
        LambdaUpdateWrapper<RsvField> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(RsvField::getVenueId, id);
        wrapper.eq(RsvField::getIsDeleted, Boolean.FALSE);
        wrapper.orderByAsc(RsvField::getSort);
        wrapper.orderByDesc(RsvField::getCreateTime);
        return RsvFieldConverter.INSTANCE.toVO(this.list(wrapper));
    }

    /**
     * 创建场地信息
     *
     * @param vo 场地信息
     * @return Boolean
     */
    @Override
    public Boolean create(RsvFieldVO vo) {
        RsvField rsvField = this.getOne(Wrappers.lambdaQuery(RsvField.class).eq(RsvField::getName, vo.getName()).last("limit 1"));
        if (Objects.nonNull(rsvField)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("场地名称已存在：" + vo.getName());
        }
        RsvField field = RsvFieldConverter.INSTANCE.toEntity(vo);
        field.setCreateBy(SecurityContextHolder.getUserIdStr());
        field.setCreateTime(new Date());
        return super.save(field);
    }

    /**
     * 更新场地信息
     *
     * @param vo 场地信息
     * @return Boolean
     */
    @Override
    public Boolean update(RsvFieldVO vo) {
        RsvField rsvField = this.getOne(Wrappers.lambdaQuery(RsvField.class).eq(RsvField::getName, vo.getName()).last("limit 1"));
        if (Objects.nonNull(rsvField) && !Objects.equals(rsvField.getId(), vo.getId())) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("场地名称已存在：" + vo.getName());
        }
        RsvField field = RsvFieldConverter.INSTANCE.toEntity(vo);
        field.setUpdateBy(SecurityContextHolder.getUserIdStr());
        field.setUpdateTime(new Date());
        return super.updateById(field);
    }
}

