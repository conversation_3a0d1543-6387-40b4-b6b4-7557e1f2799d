package com.lanshan.app.reservation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.reservation.converter.RsvAreaLessonTimeConverter;
import com.lanshan.app.reservation.dao.RsvAreaLessonTimeDao;
import com.lanshan.app.reservation.entity.RsvAreaLessonTime;
import com.lanshan.app.reservation.service.RsvAreaLessonTimeService;
import com.lanshan.app.reservation.vo.RsvAreaLessonTimeVO;
import com.lanshan.app.reservation.vo.SchoolAreaVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 校区预约节次时间表(RsvAreaLessonTime)表服务实现类
 *
 * <AUTHOR>
 */
@Service("rsvAreaLessonTimeService")
public class RsvAreaLessonTimeServiceImpl extends ServiceImpl<RsvAreaLessonTimeDao, RsvAreaLessonTime> implements RsvAreaLessonTimeService {

    @Override
    public List<RsvAreaLessonTimeVO> listByAreaCode(String areaCode) {
        List<RsvAreaLessonTime> rsvAreaLessonTimes = this.list(Wrappers.lambdaQuery(RsvAreaLessonTime.class).eq(RsvAreaLessonTime::getAreaCode, areaCode));
        return RsvAreaLessonTimeConverter.INSTANCE.toVO(rsvAreaLessonTimes);
    }

    @Override
    public List<SchoolAreaVO> listSchoolArea() {
        return this.baseMapper.listSchoolArea();
    }
}

