package com.lanshan.app.reservation.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约对象信息表(RsvReserveObject)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "预约对象信息表VO")
@Data
@ToString
public class RsvReserveObjectVO implements Serializable {

    private static final long serialVersionUID = 7022375421212407634L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "场地ID")
    private Long fieldId;

    @ApiModelProperty(value = "场地类型;1: 羽毛球；2：网球；3：篮球；4：乒乓球")
    private Integer fieldType;

    @ApiModelProperty(value = "场地名称")
    private String fieldName;

    @ApiModelProperty(value = "场馆名称")
    private String venueName;

    @ApiModelProperty(value = "开放预约日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openDay;

    @ApiModelProperty(value = "开放预约时间段")
    private String openTime;

    @ApiModelProperty(value = "已预约数量")
    private Integer usedCount;

    @ApiModelProperty(value = "剩余可预约数量")
    private Integer remainCount;

    @ApiModelProperty(value = "场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnable;
}

