package com.lanshan.app.reservation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.common.bo.PageQo;
import com.lanshan.app.reservation.vo.*;

import java.io.Serializable;
import java.util.List;

/**
 * 用户移动端预约服务API实现
 *
 * <AUTHOR>
 */
public interface ReservationH5Service {

    /**
     * 获取头部横幅信息
     *
     * @return 头部横幅信息
     */
    List<RsvBannerVO> getBannerInfo();

    /**
     * 获取场馆信息
     *
     * @return 场馆信息
     */
    List<RsvVenueVO> getVenueInfo();

    /**
     * 团队预约获取场馆信息
     *
     * @return 场馆信息
     */
    List<RsvVenueVO> getVenueInfoForTeam();

    /**
     * 获取公告信息
     *
     * @return 公告信息
     */
    List<RsvNoticeVO> getNoticeInfo();

    /**
     * 获取分页公告信息
     *
     * @param page 分页参数
     * @return 分页公告信息
     */
    IPage<RsvNoticeVO> getNoticeInfo(PageQo page);

    /**
     * 获取我的最近预约记录
     *
     * @return 我的最近预约记录
     */
    List<RsvReserveRecordVO> getMyRecentReserveRecord();

    /**
     * 获取分页预约记录
     *
     * @param page 分页参数
     * @return 分页预约记录
     */
    IPage<RsvReserveRecordVO> getMyReserveRecord(PageQo page);

    /**
     * 获取可预约场馆类型列表
     *
     * @param id 场馆ID
     * @return 可预约场馆类型列表
     */
    List<ReserveTypeVO> getReserveType(Serializable id);


    /**
     * 获取可预约场地列表
     *
     * @param id   场馆ID
     * @param type 场地类型
     * @param date 日期
     * @return 可预约场地列表
     */
    List<RsvObjectGroupVO> getReserveObjectList(Serializable id, Long type, String date);

    /**
     * 用户创建预约。主要流程如下：
     * 检查预约场地信息，预约场地是否当天时间段开放；
     * 检查预约场地余位是否有剩余。如果没有剩余，则提示用户预约失败，请选择其他时间段；
     * 检查当前时间是否在可预约时间范围内。如果不在，则提示用户预约失败，请选择其他时间段；
     * 检查当前用户是否在黑名单。如果在，则提示用户在黑名单中，不允许预约；
     * 检查用户当日预约次数是否达到上限。如果达到，则提示用户已达到当日预约次数上限，不允许预约；
     * 检查当前用户是否达到本周预约次数上限。如果达到，则提示用户已达到本周预约次数上限，不允许预约；
     * 为该用户锁定预约对象库存，创建预约记录，预约对象库存减 1。
     *
     * @param id 预约对象ID
     * @return 是否成功
     */
    Boolean createReservation(Long id);

    /**
     * 取消预约
     * 1. 检查取消时间是否在可取消时间范围内。如果不在，则提示用户无法取消预约；
     * 2. 检查当前用户是否是预约记录的创建者。如果不同，则提示用户无法取消预约；
     * 3. 为该用户解锁预约对象库存，删除预约记录，预约对象库存加 1。
     *
     * @param id 预约记录ID
     * @return 是否成功
     */
    Boolean cancelReservation(Long id);

    /**
     * 获取用户可预约日期列表
     *
     * @param id 场馆ID
     * @return 用户可预约日期列表
     */
    List<UserAvailableDateVO> getUserAvailableDate(Serializable id);

    /**
     * 获取服务器时间ID
     *
     * @return 服务器时间ID
     */
    String getServerTimeId();

    /**
     * 签到记录
     *
     * @param serverTimeId 服务器时间ID
     * @return 签到记录
     */
    RsvReserveRecordVO signRecord(String serverTimeId);

    /**
     * 获取需要签到的预约记录
     *
     * @return 预约记录列表
     */
    List<RsvReserveRecordVO> listNeedSign();

    /**
     * 签到记录V2
     *
     * @param recordId 预约记录ID
     * @return 是否成功
     */
    Boolean signRecordV2(Long recordId);
}
