package com.lanshan.app.reservation.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * 场馆预约场地类型表(RsvVenueFieldType)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class RsvVenueFieldType extends Model<RsvVenueFieldType> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 场地类型名称
     */
    private String name;
    /**
     * 图标地址
     */
    private String iconUrl;
    /**
     * 状态。true表示有效，false表示无效
     */
    private Boolean status;
    /**
     * 删除标记。true表示已删除，false表示未删除
     */
    private Boolean deleteFlag;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

