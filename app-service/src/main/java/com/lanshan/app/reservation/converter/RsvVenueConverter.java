package com.lanshan.app.reservation.converter;


import com.lanshan.app.reservation.entity.RsvVenue;
import com.lanshan.app.reservation.vo.RsvVenueVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 场馆信息表(RsvVenue)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface RsvVenueConverter {

    RsvVenueConverter INSTANCE = Mappers.getMapper(RsvVenueConverter.class);

    RsvVenueVO toVO(RsvVenue entity);

    RsvVenue toEntity(RsvVenueVO vo);

    List<RsvVenueVO> toVO(List<RsvVenue> entityList);

    List<RsvVenue> toEntity(List<RsvVenueVO> voList);
}


