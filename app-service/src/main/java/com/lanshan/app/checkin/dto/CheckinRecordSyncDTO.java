package com.lanshan.app.checkin.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class CheckinRecordSyncDTO implements Serializable {

    private static final long serialVersionUID = -4971978926358087152L;

//    @NotNull(message = "userIds不能为空")
//    @Size(min = 1, message = "userIds不能为空")
    private List<String> userIds;

    @NotNull(message = "startTime不能为空")
    private String startTime;

    @NotNull(message = "endTime不能为空")
    private String endTime;


}
