package com.lanshan.app.checkin.controller;

import cn.hutool.core.date.DateUtil;
import com.lanshan.app.checkin.dto.CheckinGradeStatisticsVO;
import com.lanshan.app.checkin.dto.CheckinRecordSyncDTO;
import com.lanshan.app.checkin.dto.CheckinStatisticsMonthDTO;
import com.lanshan.app.checkin.enums.CheckinStatus;
import com.lanshan.app.checkin.enums.CheckinTaskStatus;
import com.lanshan.app.checkin.enums.CheckinUserType;
import com.lanshan.app.checkin.po.CheckinRuleGroup;
import com.lanshan.app.checkin.service.*;
import com.lanshan.app.checkin.util.CheckinUtil;
import com.lanshan.app.checkin.vo.CheckinConfigVO;
import com.lanshan.app.checkin.vo.CheckinRuleGroupVO;
import com.lanshan.app.checkin.vo.CheckinRuleWithStatisticsVO;
import com.lanshan.base.api.utils.Result;
import com.lanshan.app.common.utils.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * 打卡规则
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/checkin/rule")
public class CheckinRuleController {

    @Resource
    private CheckinRuleGroupService checkinRuleGroupService;
    @Resource
    private CheckinAuditUserService checkinAuditUserService;
    @Resource
    private StdCounselorDepartmentRelateService counselorDepartmentRelateService;
    @Resource
    private CheckinGradeDailyService checkinGradeDailyService;
    @Resource
    private CheckinUserCadresService checkinUserCadresService;

    /**
     * 同步打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/25 10:24
     */
    @PostMapping(value = "/syncCheckinRuleGroup",produces = "application/json;charset=UTF-8")
    public Result<Object> syncCheckinRuleGroup() {
        checkinRuleGroupService.syncCheckinRuleGroup();
        return Result.build();
    }

    /**
     * 打卡配置
     * <AUTHOR> yang.
     * @since 2025/4/29 14:10
     */
    @PostMapping(value = "/getConfigInfo" ,produces = "application/json;charset=UTF-8")
    public Result<CheckinConfigVO> getConfigInfo() {
        CheckinConfigVO checkinConfigVO = new CheckinConfigVO(
                CheckinTaskStatus.getAllCheckinTaskStatus(),
                CheckinStatus.getAllCheckinStatus(),
                CheckinUserType.getAllCheckinUserType()
        );
        return Result.build(checkinConfigVO);
    }

    /**
     * 当前登录用户身份
     * <AUTHOR> yang.
     * @since 2025/4/29 14:10
     */
    @PostMapping(value = "/getUserType" ,produces = "application/json;charset=UTF-8")
    public Result<List<Integer>> getUserType() {
        String userId =  SecurityContextHolder.getUserIdStr();
        List<Integer> userType = checkinAuditUserService.getUserType(userId);
        return Result.build(userType);
    }

    /**
     * 获取某天 辅导员管理的班级统计信息
     * <AUTHOR> yang.
     * @since 2025/4/29 14:46
     * @param belongDay 日期 yyyy-MM-dd
     */
    @PostMapping(value = "/getCounselorGradeByDay" ,produces = "application/json;charset=UTF-8")
    public Result<Map<String, Object>> getCounselorGradeByDay(@RequestParam("belongDay")String belongDay) {
        String userId = SecurityContextHolder.getUserIdStr();
        List<Long> deptIds = counselorDepartmentRelateService.getCounselorDeptIds(userId);
        List<CheckinRuleWithStatisticsVO> list = checkinGradeDailyService.getCounselorGradeByDay(deptIds, belongDay);
        Map<String, Object> map = new HashMap<>();
        map.put("records", list);
        return Result.build(map);
    }

    /**
     * 获取某天 学生干部管理的班级统计信息
     * <AUTHOR> yang.
     * @since 2025/4/29 14:46
     * @param belongDay 日期 yyyy-MM-dd
     */
    @PostMapping(value = "/getUserCadresGrade" ,produces = "application/json;charset=UTF-8")
    public Result<Map<String, Object>> getUserCadresGrade(@RequestParam("belongDay")String belongDay) {
        String userId = SecurityContextHolder.getUserIdStr();
        String counselorId = checkinUserCadresService.getCounselorByCadres(userId);
        //获取辅导员管理的班级
        List<Long> deptIds = counselorDepartmentRelateService.getCounselorDeptIds(counselorId);
        List<CheckinRuleWithStatisticsVO> list = checkinGradeDailyService.getCounselorGradeByDay(deptIds, belongDay);
        Map<String, Object> map = new HashMap<>();
        map.put("records", list);
        return Result.build(map);
    }



}
