package com.lanshan.app.checkin.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class CheckinContrastRangeVO implements Serializable {

    private static final long serialVersionUID = -3097523847210719671L;

    private Long deptId;

    private String deptName;

    //应打卡人数
    private Long needCount;

    //已打卡人数
    private Long checkinCount;

    //打卡率
    @JsonIgnore
    private BigDecimal checkinRate;

    //打卡率
    private String checkinRateStr;

    //全勤人数
    private Long fullCount ;
    private Long fullCountMan ;
    private Long fullCountWoman ;

    private Long userCount;
    private Long userCountMan;
    private Long userCountWoman;

    //全勤率
    @JsonIgnore
    private BigDecimal fullRate;

    //全勤率
    private String fullRateStr;

    //男生全勤率
    @JsonIgnore
    private BigDecimal fullRateMan;

    //男生全勤率
    private String fullRateManStr;

    //女生全勤率
    @JsonIgnore
    private BigDecimal fullRateWoman;

    //女生全勤率
    private String fullRateWomanStr;

    //对比上次
    private String beforeAttendanceRateStr;


    public void setCheckinRate(BigDecimal checkinRate) {
        this.checkinRate = checkinRate;
        this.checkinRateStr = checkinRate + "%";
    }

    public void setFullRate(BigDecimal fullRate) {
        this.fullRate = fullRate;
        this.fullRateStr = fullRate + "%";
    }

    public void setFullRateMan(BigDecimal fullRateMan) {
        this.fullRateMan = fullRateMan;
        this.fullRateManStr = fullRateMan + "%";
    }

    public void setFullRateWoman(BigDecimal fullRateWoman) {
        this.fullRateWoman = fullRateWoman;
        this.fullRateWomanStr = fullRateWoman + "%";
    }
}
