package com.lanshan.app.checkin.service;

import com.lanshan.app.checkin.po.StdDepartmentRelate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.checkin.vo.StdDepartmentVO;

import java.util.List;

/**
 * <p>
 * 学校院系-年级-班级关联表 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-04-29
 */
public interface StdDepartmentRelateService extends IService<StdDepartmentRelate> {

    /**
     * 获取所有学院信息
     * <AUTHOR> yang.
     * @since 2025/4/29 10:27
     */
    List<StdDepartmentVO> getAllCollege();

    /**
     * 获取学院 下的 班级信息
     * <AUTHOR> yang.
     * @since 2025/4/29 11:50
     */
    List<StdDepartmentVO> getGradeByCollegeId(Long collegeId);

    /**
     * 获取所有班级
     * <AUTHOR> yang.
     * @since 2025/4/29 11:50
     */
    List<StdDepartmentVO> getAllGrade();

    StdDepartmentVO getGradeById(Long id);

}
