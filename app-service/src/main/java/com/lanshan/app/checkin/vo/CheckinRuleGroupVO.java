package com.lanshan.app.checkin.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lanshan.app.checkin.po.CheckinRuleGroup;
import com.lanshan.app.common.utils.JsonUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckinRuleGroupVO extends CheckinRuleGroup {
    private static final long serialVersionUID = -1749693567763832406L;

    private String deptId;

    //打卡地点
    @JsonIgnore
    private String locationInfos;

    private List<LocationInfo> locationInfosList;

    //工作日打卡时间
    @JsonIgnore
    private String checkinDate;

    private List<CheckinDate> checkinDateList;

    //必须打卡日期
    @JsonIgnore
    private String speWorkdays;

    private List<SpeDay> speWorkdaysList;

    //不用打卡日期
    @JsonIgnore
    private String speOffdays;

    private List<SpeDay> speOffdaysList;

    public void setLocationInfos(String locationInfos) {
        this.locationInfos = locationInfos;
        this.locationInfosList = JsonUtil.getListType(locationInfos, LocationInfo.class);
    }

    public void setCheckinDate(String checkinDate) {
        this.checkinDate = checkinDate;
        this.checkinDateList = JsonUtil.getListType(checkinDate, CheckinDate.class);
    }

    public void setSpeWorkdays(String speWorkdays) {
        this.speWorkdays = speWorkdays;
        this.speWorkdaysList = JsonUtil.getListType(speWorkdays, SpeDay.class);
    }

    public void setSpeOffdays(String speOffdays) {
        this.speOffdays = speOffdays;
        this.speOffdaysList = JsonUtil.getListType(speOffdays, SpeDay.class);
    }

    @Data
    public static class CheckinDate implements Serializable {

        private static final long serialVersionUID = 1070601284142469679L;

        private List<Integer> workdays;

        private List<CheckinTime> checkintime;
    }

    @Data
    public static class CheckinTime implements Serializable {

        private static final long serialVersionUID = -5816152259401919581L;

        @JsonProperty("time_id")
        private Integer timeId;

        @JsonProperty("work_sec")
        private Long workSec;

        @JsonProperty("off_work_sec")
        private Long offWorkSec;

        //上班时间转换为字符串 HH:mm
        private String workSecStr;

        //下班时间，转换为字符串 HH:mm
        private String offWorkSecStr;

    }




    @Data
    public static class SpeDay implements Serializable {
        private static final long serialVersionUID = -4754023535518424770L;

        private String notes;

        //0 单个日期 1 日期区间
        private Integer type;

        @JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
        private Date begTime;

        @JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
        private Date endTime;

        @JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
        private Date time;

        private List<CheckinTime> checkintime;
    }

    @Data
    public static class LocationInfo implements Serializable {
        private static final long serialVersionUID = -5816152259401919581L;

        private String title;

        private String detail;

        //纬度
        private Long lat;

        //经度
        private Long lng;

        //	允许打卡范围（米）
        private Integer distance;

    }
}
