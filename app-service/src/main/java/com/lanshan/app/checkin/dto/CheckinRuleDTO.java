package com.lanshan.app.checkin.dto;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.lanshan.app.common.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */
@Data
public class CheckinRuleDTO implements Serializable {
    private static final long serialVersionUID = -2276915389536382675L;

    @JsonProperty("groupid")
    private Long groupId;

    //打卡规则名称
    @JsonProperty("groupname")
    private String groupName;

    //打卡规则类型，1：固定时间上下班；2：按班次上下班；3：自由上下班
    @JsonProperty("grouptype")
    private Integer groupType;

    //打卡方式，0:手机；2:智慧考勤机；3:手机+智慧考勤机
    private Integer type;

    //打卡规则创建者
    @JsonProperty("create_userid")
    private String createUserId;

    //打卡规则最近创建者
    @JsonProperty("update_userid")
    private String updateUserId;

    private Range range;

    private List<String> partyIds;

    private List<String> userIds;

    //规则创建时间
    private Long create_time;

    private Date createTime;

    private Date updateTime = new Date();

    //打卡地点
    private List<LocationInfo> loc_infos;

    private String locationInfos;

    private Boolean sync_holidays;

    //是否同步法定节假日，1为同步，0为不同步
    private Integer syncHolidays;

    //工作日打卡时间
    private List<CheckinDate> checkindate;

    private List<SpeDay> spe_workdays;
    //必须打卡日期
    private String speWorkdays;

    private List<SpeDay> spe_offdays;
    //不用打卡日期
    private String speOffdays;

    public void setLoc_infos(List<LocationInfo> loc_infos) {
        this.loc_infos = loc_infos;
        this.locationInfos = JsonUtil.toJson(loc_infos);
    }

    public void setSpe_workdays(List<SpeDay> spe_workdays) {
        this.spe_workdays = spe_workdays;
        this.speWorkdays = JsonUtil.toJson(spe_workdays);
    }

    public void setSpe_offdays(List<SpeDay> spe_offdays) {
        this.spe_offdays = spe_offdays;
        this.speOffdays = JsonUtil.toJson(spe_offdays);
    }

    public void setSync_holidays(Boolean sync_holidays) {
        this.sync_holidays = sync_holidays;
        if (sync_holidays != null) {
            this.syncHolidays = sync_holidays ? 1 : 0;
        }
    }

    public void setRange(Range range) {
        this.range = range;
        if (range != null) {
            this.partyIds = range.getParty_id();
            this.userIds = range.getUserid();
        }
    }

    public void setCreate_time(Long create_time) {
        this.create_time = create_time;
        if (create_time != null && create_time > 0) {
            this.createTime = new Date(create_time * 1000);
        }
    }

    @Data
    public static class SpeDay implements Serializable {
        private static final long serialVersionUID = -4754023535518424770L;

        private String notes;

        //0 单个日期 1 日期区间
        private Integer type;

        private Date begTime;

        private Date endTime;

        private Date time;

        private List<CheckinTime> checkintime;

        private Long timestamp;

        private Long begtime;

        private Long endtime;

        public void setBegtime(Long begtime) {
            this.begtime = begtime;
            if (begtime != null && begtime > 0) {
                this.begTime = new Date(begtime * 1000);
            }
        }

        public void setEndtime(Long endtime) {
            this.endtime = endtime;
            if (endtime != null && endtime > 0) {
                this.endTime = new Date(endtime * 1000);
            }
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
            if (timestamp != null && timestamp > 0) {
                this.time = new Date(timestamp * 1000);
            }
        }
    }

    @Data
    public static class Range implements Serializable {
        private static final long serialVersionUID = -4754023535518424770L;

        private List<String> party_id;

        private List<String> userid;

    }

    @Data
    public static class LocationInfo implements Serializable {
        private static final long serialVersionUID = -5816152259401919581L;

        @JsonProperty("loc_title")
        private String title;

        @JsonProperty("loc_detail")
        private String detail;

        //纬度
        private Long lat;

        //经度
        private Long lng;

        //	允许打卡范围（米）
        private Integer distance;

    }

    @Data
    @JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
    public static class CheckinDate implements Serializable {

        private static final long serialVersionUID = 1070601284142469679L;

        private List<Integer> workdays;

        private List<CheckinTime> checkintime;
    }

    @Data
    public static class CheckinTime implements Serializable {

        private static final long serialVersionUID = -5816152259401919581L;

        @JsonProperty("time_id")
        private Integer timeId;

        @JsonProperty("work_sec")
        private Long workSec;

        @JsonProperty("off_work_sec")
        private Long offWorkSec;

        //上班时间转换为字符串 HH:mm
        private String workSecStr;

        //下班时间，转换为字符串 HH:mm
        private String offWorkSecStr;

        public void setWorkSec(Long workSec) {
            this.workSec = workSec;
            this.workSecStr = DateUtil.format(DateUtil.offsetSecond(DateUtil.beginOfDay(DateUtil.date()), workSec.intValue()), "HH:mm");
        }

        public void setOffWorkSec(Long offWorkSec) {
            this.offWorkSec = offWorkSec;
            this.offWorkSecStr = DateUtil.format(DateUtil.offsetSecond(DateUtil.beginOfDay(DateUtil.date()), offWorkSec.intValue()), "HH:mm");
        }
    }


}
