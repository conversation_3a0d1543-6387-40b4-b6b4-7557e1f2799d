package com.lanshan.app.checkin.dao;

import com.lanshan.app.checkin.po.CheckinRuleGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.checkin.vo.CheckinRuleGroupVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 打卡规则组 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-04-24
 */
@Mapper
public interface CheckinRuleGroupMapper extends BaseMapper<CheckinRuleGroup> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CheckinRuleGroup> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CheckinRuleGroup> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<CheckinRuleGroup> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<CheckinRuleGroup> entities);

    List<CheckinRuleGroupVO> selectCheckinRuleGroup(@Param("deptId") String deptId,@Param("userId") String userId);

    List<CheckinRuleGroupVO> getCheckinRuleGroupByDeptIds(@Param("deptIds") Collection<String> deptIds);

    @Update("truncate table standard_app.checkin_rule_group")
    void  truncationCheckinRuleGroup();
}
