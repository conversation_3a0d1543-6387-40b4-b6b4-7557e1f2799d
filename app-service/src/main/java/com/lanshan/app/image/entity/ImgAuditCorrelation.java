package com.lanshan.app.image.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 审核关联表(ImgAuditCorrelation)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ImgAuditCorrelation extends Model<ImgAuditCorrelation> {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 被审核人 id
     */
    private String auditeeUserId;
    /**
     * 审核人 id
     */
    private String auditorUserId;
    /**
     * 被审核人类型 1：学生；2：教师；3：校外人员
     */
    private Integer auditeeType;
    /**
     * 审核人类型 1：学生；2：教师；3：校外人员
     */
    private Integer auditorType;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 催办次数
     */
    private Integer numberOfRemind;

}

