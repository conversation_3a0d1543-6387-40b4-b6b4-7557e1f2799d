package com.lanshan.app.image.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.image.dto.ExportUploadRecordDto;
import com.lanshan.app.image.dto.ImgVerifySearchDTO;
import com.lanshan.app.image.dto.ImgVerifyStatusDTO;
import com.lanshan.app.image.entity.ImgUserImage;
import com.lanshan.app.image.enums.UserTypeEnum;
import com.lanshan.app.image.vo.AuditImgUserImageVO;
import com.lanshan.app.image.vo.ImgUserImageCountVO;
import com.lanshan.app.image.vo.ImgUserImageVO;
import com.lanshan.app.image.vo.ImgVerifyOverviewVO;

import java.util.List;
import java.util.Map;

/**
 * 用户照片信息(ImgUserImage)表服务接口
 *
 * <AUTHOR>
 */
public interface ImgUserImageService extends IService<ImgUserImage> {

    /**
     * 获取认证照片审核概况
     *
     * @param userId   用户ID
     * @param userType 用户类型 1：学生；2：教师；3：校外人员
     * @return 认证照片审核概况
     */
    List<ImgVerifyOverviewVO> getVerifyOverview(String userId, Integer userType);

    /**
     * 获取认证照片审核列表
     *
     * @param page   分页对象
     * @param param  查询参数
     * @param userId 用户ID
     * @return 认证照片审核列表
     */
    IPage<AuditImgUserImageVO> pageVerifyList(IPage<ImgUserImageVO> page, ImgVerifySearchDTO param, String userId);

    /**
     * 根据条件获取审核人下的待审核人的照片信息
     *
     * @param dto       查询参数
     * @param userIdStr 审核人ID
     * @return 审核人下的待审核人照片信息集合
     */
    List<ImgUserImage> listByAuditor(ImgVerifyStatusDTO dto, String userIdStr);

    /**
     * 根据类型和用户userId找到对应类型最新的图片
     *
     * @param imageTypes 图片类型
     * @param userIds    用户ids
     * @return 对应类型最新的图片
     */
    List<ImgUserImage> listMainImage(List<Integer> imageTypes, List<String> userIds);

    /**
     * 根据用户userId找到对应类型最新的图片
     *
     * @param userId 用户id
     * @return 对应类型最新的图片
     */
    List<ImgUserImage> getUserImageListByUserId(String userId);

    /**
     * 根据用户userId找到对应图片
     *
     * @param userIds 用户ids
     * @param imgType 图片类型
     * @return 对应用户图片
     */
    List<ImgUserImage> listUserImageByUserIds(List<String> userIds, Integer imgType);

    /**
     * 获取用户指定照片类型的已上传图片数量
     *
     * @param userId  用户id
     * @param imgType 照片类型
     * @return 上传图片数量
     */
    long getUserUploads(String userId, Integer imgType);

    /**
     * 获取用户指定照片类型的已上传图片数量
     *
     * @param userIds 用户ids
     * @param imgType 照片类型
     * @return 用户指定类型图片上传数量 key: 用户学工号 value: 上传图片数量
     */
    Map<String, Long> getUserUploadCount(List<String> userIds, Integer imgType);

    /**
     * 获取用户指定照片类型的已上传图片数量
     *
     * @param imgType 照片类型
     * @return 用户指定类型图片上传数量 key: 用户学工号 value: 上传图片数量
     */
    List<ImgUserImageCountVO> getAllUserUploadCount(Integer imgType);

    /**
     * 获取用户各种照片类型的已上传图片数量
     *
     * @param userId 用户id
     * @return 用户指定类型图片上传数量 key: 照片类型 value: 上传图片数量
     */
    Map<Integer, Long> getUserUploadCount(String userId);

    /**
     * 根据用户id找到所有类型主图图片
     *
     * @param userId 用户id
     * @return 对应类型最新的图片
     */
    List<ImgUserImage> listUserImageByUserId(String userId);

    /**
     * 导出用户照片上传记录
     *
     * @param exportUploadRecordDto 导出参数
     * @return 导出结果
     */
    List<ImgUserImageVO> exportUploadRecord(ExportUploadRecordDto exportUploadRecordDto);

    /**
     * 根据具体用户类型下的用户id和图片类型找到所有用户下指定类型的图片
     *
     * @param userId   用户id
     * @param imgType  图片类型
     * @param userType 用户类型
     * @return 该用户下对应类型的图片集合
     */
    List<ImgUserImageVO> listByUserAndImgType(String userId, Integer imgType, UserTypeEnum userType);

    /**
     * 获取审核通过的图片
     *
     * @param userId  用户id
     * @param imgType 图片类型
     * @return 审核通过的图片
     */
    ImgUserImage getPassImg(String userId, Integer imgType);
}

