package com.lanshan.app.image.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.image.dao.ImgUserImageDao;
import com.lanshan.app.image.dto.ExportUploadRecordDto;
import com.lanshan.app.image.dto.ImgVerifySearchDTO;
import com.lanshan.app.image.dto.ImgVerifyStatusDTO;
import com.lanshan.app.image.entity.ImgUserImage;
import com.lanshan.app.image.enums.AuditStatusEnum;
import com.lanshan.app.image.enums.PhotoTypeEnum;
import com.lanshan.app.image.enums.UserTypeEnum;
import com.lanshan.app.image.service.ImgUserImageService;
import com.lanshan.app.image.vo.AuditImgUserImageVO;
import com.lanshan.app.image.vo.ImgUserImageCountVO;
import com.lanshan.app.image.vo.ImgUserImageVO;
import com.lanshan.app.image.vo.ImgVerifyOverviewVO;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户照片信息(ImgUserImage)表服务实现类
 *
 * <AUTHOR>
 */
@Service("imgUserImageService")
public class ImgUserImageServiceImpl extends ServiceImpl<ImgUserImageDao, ImgUserImage> implements ImgUserImageService {

    @Override
    public List<ImgVerifyOverviewVO> getVerifyOverview(String userId, Integer userType) {
        return this.baseMapper.getVerifyOverview(userId, userType);
    }

    @Override
    public IPage<AuditImgUserImageVO> pageVerifyList(IPage<ImgUserImageVO> page, ImgVerifySearchDTO param, String userId) {
        return this.baseMapper.pageVerifyList(page, param, userId);
    }

    @Override
    public List<ImgUserImage> listByAuditor(ImgVerifyStatusDTO dto, String userIdStr) {
        return this.baseMapper.listByAuditor(dto, userIdStr);
    }

    @Override
    public List<ImgUserImage> listMainImage(List<Integer> imageTypes, List<String> userIds) {
        return this.baseMapper.listMainImage(imageTypes, userIds);
    }

    @Override
    public List<ImgUserImage> getUserImageListByUserId(String userId) {
        return this.baseMapper.getUserImageListByUserId(userId);
    }

    @Override
    public List<ImgUserImage> listUserImageByUserIds(List<String> userIds, Integer imgType) {
        LambdaQueryWrapper<ImgUserImage> qw = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(userIds)) {
            qw.in(ImgUserImage::getUserId, userIds);
        }
        if (imgType != null) {
            qw.eq(ImgUserImage::getImgType, imgType);
        }
        qw.eq(ImgUserImage::getIsDeleted, false);
        return this.list(qw);
    }

    @Override
    public long getUserUploads(String userId, Integer imgType) {
        //根据产品需求，已删除的数据参与次数统计，记录一下方便理解。
        return this.count(Wrappers.lambdaQuery(ImgUserImage.class)
                .eq(ImgUserImage::getUserId, userId)
                .eq(ImgUserImage::getImgType, imgType)
                .eq(ImgUserImage::getIsCount, true)
        );
    }

    @Override
    public Map<String, Long> getUserUploadCount(List<String> userIds, Integer imgType) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        Map<String, Long> result = userIds.stream().map(userId -> Pair.create(userId, 0L)).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        List<ImgUserImage> list = this.list(Wrappers.lambdaQuery(ImgUserImage.class)
                .select(ImgUserImage::getUserId, ImgUserImage::getId)
                .in(ImgUserImage::getUserId, userIds)
                .eq(ImgUserImage::getImgType, imgType)
                .eq(ImgUserImage::getIsDeleted, false)
                .in(ImgUserImage::getStatus, AuditStatusEnum.AUDIT_PASS.getCode()));
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(ImgUserImage::getUserId, Collectors.counting()));
        result.putAll(map);
        return result;
    }

    @Override
    public List<ImgUserImageCountVO> getAllUserUploadCount(Integer imgType) {
        return this.baseMapper.getAllUserUploadCount(imgType);
    }

    @Override
    public Map<Integer, Long> getUserUploadCount(String userId) {
        Map<Integer, Long> result = Arrays.stream(PhotoTypeEnum.values()).map(en -> Pair.create(en.getCode(), 0L)).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        List<ImgUserImage> list = this.list(Wrappers.lambdaQuery(ImgUserImage.class)
                .select(ImgUserImage::getUserId, ImgUserImage::getId, ImgUserImage::getImgType)
                .eq(ImgUserImage::getUserId, userId)
                .eq(ImgUserImage::getIsCount, true)
                .eq(ImgUserImage::getIsDeleted, false)
                .in(ImgUserImage::getStatus, AuditStatusEnum.AUDITING.getCode(), AuditStatusEnum.AUDIT_PASS.getCode()));
        Map<Integer, Long> map = list.stream().collect(Collectors.groupingBy(ImgUserImage::getImgType, Collectors.counting()));
        result.putAll(map);
        return result;
    }

    @Override
    public List<ImgUserImage> listUserImageByUserId(String userId) {
        return this.list(Wrappers.lambdaQuery(ImgUserImage.class)
                .eq(ImgUserImage::getUserId, SecurityContextHolder.getUserIdStr())
                .eq(ImgUserImage::getStatus, AuditStatusEnum.AUDIT_PASS.getCode())
                .eq(ImgUserImage::getIsMain, true)
                .eq(ImgUserImage::getIsDeleted, false)
        );
    }

    @Override
    public List<ImgUserImageVO> exportUploadRecord(ExportUploadRecordDto exportUploadRecordDto) {
        return this.baseMapper.exportUploadRecord(exportUploadRecordDto);
    }

    @Override
    public List<ImgUserImageVO> listByUserAndImgType(String userId, Integer imgType, UserTypeEnum userType) {
        return this.baseMapper.listByUserAndImgType(userId, imgType, userType.getType());
    }

    @Override
    public ImgUserImage getPassImg(String userId, Integer imgType) {
        return this.getOne(Wrappers.lambdaQuery(ImgUserImage.class)
                .eq(ImgUserImage::getUserId, userId)
                .eq(ImgUserImage::getImgType, imgType)
                .eq(ImgUserImage::getStatus, AuditStatusEnum.AUDIT_PASS.getCode())
                .eq(ImgUserImage::getIsMain, true)
                .eq(ImgUserImage::getIsDeleted, false)
        );
    }
}

