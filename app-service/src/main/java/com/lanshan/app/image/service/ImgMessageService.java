package com.lanshan.app.image.service;

import java.util.List;

public interface ImgMessageService {

    /**
     * 发送照片审核通知
     *
     * @param userId  图片已上传的用户ID
     * @param imgType 照片类型
     */
    void sendNoticeForImgToVerify(String userId, Integer imgType);

    /**
     * 发送催办上传照片通知
     *
     * @param userIdList 催办用户ID列表
     * @param imgType    照片类型
     */
    void sendNoticeForRemindToUploadImg(List<String> userIdList, Integer imgType);

    /**
     * 发送照片审核结果通知
     *
     * @param userIds 图片ID
     * @param imgType 照片类型
     * @param status  审核状态
     */
    void sendNoticeForImgVerified(List<String> userIds, Integer imgType, Integer status);
}
