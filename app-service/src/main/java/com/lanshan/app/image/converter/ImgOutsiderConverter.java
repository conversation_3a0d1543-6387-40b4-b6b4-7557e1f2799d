package com.lanshan.app.image.converter;


import cn.hutool.core.util.DesensitizedUtil;
import com.lanshan.app.image.entity.ImgOutsider;
import com.lanshan.app.image.entity.ImgStudent;
import com.lanshan.app.image.vo.ImgOutsiderVO;
import com.lanshan.app.image.vo.ImgStudentVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 照片库校外人员信息(ImgOutsider)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface ImgOutsiderConverter {

    ImgOutsiderConverter INSTANCE = Mappers.getMapper(ImgOutsiderConverter.class);
    @Mapping(target = "phoneNumber", qualifiedByName = "desensitizedPhoneNumber")
    ImgOutsiderVO toVO(ImgOutsider entity);

    ImgOutsider toEntity(ImgOutsiderVO vo);

    List<ImgOutsiderVO> toVO(List<ImgOutsider> entityList);

    List<ImgOutsider> toEntity(List<ImgOutsiderVO> voList);

    /**
     * 手机号脱敏
     * @param phoneNumber 手机号
     * @return 脱敏的手机号
     */
    @Named("desensitizedPhoneNumber")
    default String desensitizedPhoneNumber(String phoneNumber) {
        return DesensitizedUtil.mobilePhone(phoneNumber);
    }
}


