package com.lanshan.app.image.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 设置返回 VO
 */
@ApiModel(value = "设置返回 VO")
@Data
public class SettingsVO {

    @ApiModelProperty(value = "照片设置")
    private ImgSettingsVO imgSettingsVO;

    @ApiModelProperty(value = "客户端设置")
    private ClientSettingsVO clientSettingsVO;

    @ApiModelProperty(value = "水印设置")
    private WatermarkSettingsVO watermarkSettingsVO;



}
