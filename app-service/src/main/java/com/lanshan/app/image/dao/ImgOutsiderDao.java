package com.lanshan.app.image.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.image.dto.OutsiderSearchDTO;
import com.lanshan.app.image.entity.ImgOutsider;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 照片库校外人员信息(ImgOutsider)表数据库访问层
 *
 * <AUTHOR>
 */
public interface ImgOutsiderDao extends BaseMapper<ImgOutsider> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ImgOutsider> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ImgOutsider> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ImgOutsider> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ImgOutsider> entities);

    /**
     * 分页查询校外人员照片库信息
     *
     * @param page 分页对象
     * @param outsiderSearchDTO  查询条件
     * @return IPage<ImgOutsiderVO>
     */
    IPage<ImgOutsider> pageOutsiderImgInfo(@Param("page") Page<ImgOutsider> page,@Param("outsiderSearchDTO") OutsiderSearchDTO outsiderSearchDTO);
}

