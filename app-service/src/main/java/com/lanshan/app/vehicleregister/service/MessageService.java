package com.lanshan.app.vehicleregister.service;


import cn.hutool.core.util.StrUtil;
import com.lanshan.app.aliyun.util.SmsUtil;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.vehicleregister.enums.VehicleUserType;
import com.lanshan.app.vehicleregister.po.VehicleRegisterRecord;
import com.lanshan.app.vehicleregister.po.VehicleViolationType;
import com.lanshan.app.vehicleregister.vo.VehicleViolationSmsVO;
import com.lanshan.base.api.dto.message.TextcardMsgBody;
import com.lanshan.base.api.feign.message.MessageFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static com.lanshan.app.vehicleregister.constant.VehicleRegisterConstant.*;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/11 16:44
 */
@Slf4j
@Service
public class MessageService {

    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private SmsUtil smsUtil;

    /**
     * 第三四类人员：走短信通知渠道   其他走企微通知渠道
     * <AUTHOR> yang.
     * @since 2025/03/11 16:50
     * @param type 1 违规通知 2 注销通知
     */
    public void pushMessage(VehicleRegisterRecord registerRecord, VehicleViolationType violationType, int type) {
        String content = "";
        String title = "";
        if (type == 1){
            if (violationType != null){
                title = violationType.getName()+"通知";
                content = StrUtil.format("({})"+violationType.getViolationExplain(), registerRecord.getLicensePlate());
            }
        }else if (type == 2){
            title = "注销通知";
            content = StrUtil.format("您的电动自行车{}已注销！", registerRecord.getLicensePlate());
        }
        Integer userType = registerRecord.getUserType();

        if (userType.equals(VehicleUserType.OTHER_FOUR.value) ||userType.equals(VehicleUserType.OTHER_THREE.value)) {
            //发短信
            if (violationType != null && type == 1){
                //根据模板id发送短信
                String smsCode = violationType.getSmsCode();
                String personStr = sysConfigService.selectConfigByKey(VEHICLEREGISTER_PERSON);
                String contactStr = sysConfigService.selectConfigByKey(VEHICLEREGISTER_CONTACT);
                VehicleViolationSmsVO smsVO = new VehicleViolationSmsVO(registerRecord.getLicensePlate(), personStr, contactStr);
                smsUtil.sendSmsByTemplate(smsCode,registerRecord.getPhone(), smsVO);
            }
        }else {
            //企微消息
            sendTextCard(title, content, registerRecord.getUserId());
        }
    }



    /**
     * 发送企微消息
     * <AUTHOR> yang.
     * @since 2025/03/11 15:34
     */
    public void sendTextCard(String title, String content, String toUser) {
        final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
        final String agentId = sysConfigService.selectConfigByKey(VEHICLE_REGISTER_AGENT_ID_KEY);
        final String messageUrl = sysConfigService.selectConfigByKey(VEHICLEREGISTER_LIST_URL);

        TextcardMsgBody message = new TextcardMsgBody();
        message.setTitle(title);
        message.setDescription(content);
        message.setUrl(messageUrl);
        message.setToUser(toUser);
        message.setAgentId(Integer.parseInt(agentId));

        try {
            messageFeign.sendTextCard(corpId, agentId, message);
        } catch (Exception e) {
            String msg = StrUtil.format("发送企微消息失败 content: {}, toUser: {}",
                    content, toUser);
            log.error(msg, e);
        }

    }

}
