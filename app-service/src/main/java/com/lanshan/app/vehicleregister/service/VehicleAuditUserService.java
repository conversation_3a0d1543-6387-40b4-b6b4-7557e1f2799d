package com.lanshan.app.vehicleregister.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.vehicleregister.dto.VehicleAuditUserPageDTO;
import com.lanshan.app.vehicleregister.po.VehicleAuditUser;
import com.lanshan.app.vehicleregister.vo.VehicleAuditUserVO;
import com.lanshan.app.vehicleregister.vo.VehicleIndexVO;
import com.lanshan.base.api.page.PageResult;

/**
 * <p>
 * 电动车登记审核人 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-03-10
 */
public interface VehicleAuditUserService extends IService<VehicleAuditUser> {

    /**
     * 分页查询电动车登记审核人列表
     * <AUTHOR> yang.
     * @since 2025/03/10 20:09
     */
    PageResult<VehicleAuditUserVO> getVehicleAuditUserPageList(VehicleAuditUserPageDTO dto);

    VehicleIndexVO getVehicleIndex(String userId);
}
