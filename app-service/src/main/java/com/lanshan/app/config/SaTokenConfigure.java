package com.lanshan.app.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.stp.StpUtil;
import com.lanshan.app.common.utils.StpAppUtil;
import com.lanshan.app.common.utils.StpUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class SaTokenConfigure {

    @Bean
    public void setSaTokenConfig() {
        // 后台管理员 设定 StpUtil 使用的 SaTokenConfig 配置参数对象
        SaTokenConfig config1 = new SaTokenConfig();
        config1.setTokenName("sa-token");
        StpUtil.stpLogic.setConfig(config1);

        // 接入方 设定 StpCompanyUtil 使用的 SaTokenConfig 配置参数对象
        SaTokenConfig config2 = new SaTokenConfig();
        config2.setTokenName("sa-token-app");
        StpAppUtil.stpLogic.setConfig(config2);

        // 普通用户 设定 StpUserUtil 使用的 SaTokenConfig 配置参数对象
        SaTokenConfig config3 = new SaTokenConfig();
        config3.setTokenName("sa-token-user");
        StpUserUtil.stpLogic.setConfig(config3);

        log.info("Sa-Token 配置完成");
    }
}
