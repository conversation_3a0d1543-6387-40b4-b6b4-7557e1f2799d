package com.lanshan.app.config.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.reservation.entity.RsvAreaLessonTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户权限查询 Mapper
 *
 * <AUTHOR>
 */
public interface UserPermissionDao {

    /**
     * 根据用户ID查询用户拥有的所有权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectUserPermissions(@Param("userId") Long userId);
}