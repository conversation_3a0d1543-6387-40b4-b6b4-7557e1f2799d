package com.lanshan.app.busreserve.dto;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveInfoDTO implements Serializable {

    private static final long serialVersionUID = 1915844242413774537L;
    //始发站 武昌校区 | 嘉鱼校区
    private String startLocation;

    //终点站 武昌校区 | 嘉鱼校区
    private String endLocation;

    //发车日期  yyyy-MM-dd
    private String startDay;

    //1 2 3 4 5 6 7   1代表周日  2代表周一
    private Integer week;

    //当前时间 距离当天0点的秒数
    private Integer seconds;

    //根据发车日期计算星期，计算现在距离当天0点的秒数
    public void setStartDay(String startDay) {
        this.startDay = startDay;
        if (StrUtil.isNotEmpty(startDay)){
            DateTime time = DateUtil.parseDate(startDay);
            this.week = DateUtil.dayOfWeekEnum(time).getValue();
            Date now = new Date();
            if (DateUtil.isSameDay(time, now)) {
                this.seconds = (int) ((now.getTime() - DateUtil.beginOfDay(now).getTime()) / 1000);
            }
        }
    }
}
