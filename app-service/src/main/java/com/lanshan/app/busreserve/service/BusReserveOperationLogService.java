package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveOperationLogPageDTO;
import com.lanshan.app.busreserve.po.BusReserveOperationLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 班车预约-操作日志 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-21
 */
public interface BusReserveOperationLogService extends IService<BusReserveOperationLog> {

    /**
     * 获取班车操作日志分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    Page<BusReserveOperationLog> getBusReserveOperationLogPageList(BusReserveOperationLogPageDTO dto);

    void saveBatchBusReserveOperationLog(List<BusReserveOperationLog> records);
}
