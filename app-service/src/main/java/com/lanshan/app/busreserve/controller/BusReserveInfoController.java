package com.lanshan.app.busreserve.controller;

import com.aliyun.dysmsapi20170525.models.QuerySmsTemplateListResponseBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.aliyun.util.SmsUtil;
import com.lanshan.app.busreserve.dto.BusReserveInfoPageDTO;
import com.lanshan.app.busreserve.dto.BusReserveShowStatusDTO;
import com.lanshan.app.busreserve.dto.BusReserveUserAuthDTO;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.service.*;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.List;

import static com.lanshan.app.busreserve.constant.BusReserveConstant.DETAIL_URI;
import static com.lanshan.app.busreserve.constant.BusReserveConstant.OUTSIDE_RESERVE_CODE;

/**
 * 班车配置
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/info")
public class BusReserveInfoController {

    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Resource
    private BusReserveMessageService busReserveMessageService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private BusReserveService busReserveService;




    /**
     * 获取班车配置列表
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    @PostMapping(value = "/getBusReserveInfoPageList" ,produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveInfo>> getBusReserveInfoPageList(@RequestBody BusReserveInfoPageDTO dto){
        Page<BusReserveInfo> pg = busReserveInfoService.getBusReserveInfoPageList(dto);
        return Result.build(pg);
    }

    /**
     * 新增班车配置
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    @PostMapping(value = "/insertBusReserveInfo" ,produces = "application/json;charset=UTF-8")
    public Result<Object> insertBusReserveInfo(@RequestBody BusReserveInfo busReserveInfo){
        busReserveInfoService.insertBusReserveInfo(busReserveInfo);
        return Result.build();
    }

    /**
     * 修改班车配置
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    @PostMapping(value = "/updateBusReserveInfo" ,produces = "application/json;charset=UTF-8")
    public Result<Object> updateBusReserveInfo(@RequestBody BusReserveInfo busReserveInfo){
        busReserveInfoService.updateBusReserveInfo(busReserveInfo);
        return Result.build();
    }

    /**
     * 批量 上线|下线
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    @PostMapping(value = "/changeBusReserveInfoShowStatus" ,produces = "application/json;charset=UTF-8")
    public Result<Object> changeBusReserveInfoShowStatus(@RequestBody BusReserveShowStatusDTO dto){
        List<BusReserveInfo> records = busReserveInfoService.changeBusReserveInfoShowStatus(dto);
        Integer showStatus = dto.getShowStatus();
        if (showStatus == 0){
            //发送 企微消息 | 短信消息
            busReserveMessageService.sendShowStatusMsg(dto.getIds(),records);
            //下线
            busReserveService.changeBusReserveOffline(dto.getIds());
        }
        return Result.build();
    }

    /**
     * 批量删除班车配置
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    @PostMapping(value = "/removeBatchBusReserveInfo" ,produces = "application/json;charset=UTF-8")
    public Result<Object> removeBatchBusReserveInfo(@RequestBody @NotEmpty List<Long> ids){
        return busReserveInfoService.removeBatchBusReserveInfo(ids);
    }

    /**
     * 外部用户预约码地址
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    @PostMapping(value = "/getOutsideReserveCode" ,produces = "application/json;charset=UTF-8")
    public Result<Object> getOutsideReserveCode(){
        String outsideReserveCode = sysConfigService.selectConfigByKey(OUTSIDE_RESERVE_CODE);
        return Result.build(outsideReserveCode);
    }

}
