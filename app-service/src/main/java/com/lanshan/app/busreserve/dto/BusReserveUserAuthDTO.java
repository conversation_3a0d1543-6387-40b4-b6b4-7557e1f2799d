package com.lanshan.app.busreserve.dto;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class BusReserveUserAuthDTO implements Serializable {
    private static final long serialVersionUID = -5972403614502841664L;

    //是否外部用户  1是 0否
    @NotNull
    private Integer outside;

    //企微用户为userId 外部用户为填写的手机号
    @NotNull
    private List<String> userIds;

    //班次id
    @NotNull
    private Long infoId;

    //发车日期  yyyy-MM-dd
    @NotNull
    private String startDay;

}
