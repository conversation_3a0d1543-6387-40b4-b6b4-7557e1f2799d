package com.lanshan.app.busreserve.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.app.aliyun.util.SmsUtil;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveDepartureRecord;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.service.*;
import com.lanshan.app.busreserve.util.BusReserveUtil;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO;
import com.lanshan.app.busreserve.vo.BusReserveSmsVO;
import com.lanshan.app.busreserve.vo.BusReserveUserRecordVO;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.IdGenerator;
import com.lanshan.app.common.utils.RedisService;
import com.lanshan.app.common.utils.ThreadPoolUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lanshan.app.busreserve.constant.BusReserveConstant.*;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@Component
public class BusReserveTask {

    private static final String REMIND_FIVE_MINUTE = "remind_bus_five";

    @Resource
    private BusReserveUserRecordService busReserveUserRecordService;
    @Resource
    private BusReserveOutsideUserRecordService busReserveOutsideUserRecordService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private RedisService redisService;
    @Resource
    private BusReserveMessageService busReserveMessageService;
    @Resource
    private SmsUtil smsUtil;
    @Resource
    private BusReserveInfoService busReserveInfoService;

    @Resource
    private BusReserveService busReserveService;

    /**
     * 发车前一小时提醒
     * <AUTHOR> yang.
     * @since 2025/6/3 10:24
     */
    @XxlJob("busStartRemindFiveMinute")
    public void busStartRemindFiveMinute() {
        log.info("发车前一小时提醒开始");
        TimeInterval timer = DateUtil.timer();
        Date now = new Date();
        String startTime = DateUtil.format(DateUtil.offsetMinute(now, 55), DatePattern.NORM_DATETIME_PATTERN);
        String endTime = DateUtil.format(DateUtil.offsetMinute(now, 65), DatePattern.NORM_DATETIME_PATTERN);
        List<BusReserveUserRecordVO> records = busReserveUserRecordService.getWaitRecordByDate(startTime, endTime, BusReserveStatus.WAIT.value);
        List<BusReserveOutsideUserRecordVO> outsideRecords = busReserveOutsideUserRecordService.getWaitRecordByDate(startTime, endTime, BusReserveStatus.WAIT.value);
        this.sendBusStartRemind(records, outsideRecords,true);
        log.info("发车前一小时提醒耗时：{}", DateUtil.formatBetween(timer.intervalMs()));
    }

    /**
     * 发车前一天 19：00 提醒
     * <AUTHOR> yang.
     * @since 2025/6/3 10:24
     */
    @XxlJob("busStartRemindDay")
    public void busStartRemindDay(){
        log.info("发车前一天 19：00 提醒开始");
        String tomorrow = DateUtil.formatDate(DateUtil.tomorrow());
        TimeInterval timer = DateUtil.timer();
        List<BusReserveUserRecordVO> records = busReserveUserRecordService.getTomorrowWaitRecord(tomorrow, BusReserveStatus.WAIT.value);
        List<BusReserveOutsideUserRecordVO> outsideRecords = busReserveOutsideUserRecordService.getTomorrowWaitRecord(tomorrow, BusReserveStatus.WAIT.value);
        this.sendBusStartRemind(records, outsideRecords,false);
        log.info("发车前一天 19：00 提醒耗时：{}", DateUtil.formatBetween(timer.intervalMs()));
    }

    /**
     * 每天 00:00:30 生成昨天发车记录、修改未乘车记录、修改班车配置
     * <AUTHOR> yang.
     * @since 2025/6/3 11:28
     */
    @XxlJob("busReserveRecordDay")
    public void busReserveRecordDay(){
        log.info("生成昨天发车记录、修改未乘车记录、修改班车配置开始");
        TimeInterval timer = DateUtil.timer();
        Date now = new Date();
        Date yesterday = DateUtil.yesterday();
        String yesterdayStr = DateUtil.formatDate(yesterday);

        //昨天乘坐记录
        List<BusReserveUserRecord> records = busReserveUserRecordService.getBusReserveUserRecordByDay(yesterdayStr);
        List<BusReserveOutsideUserRecord> outsideRecords = busReserveOutsideUserRecordService.getOutsideUserRecordByDay(yesterdayStr);
        //要修改的记录
        List<Long> updateRecords = new ArrayList<>();
        List<Long> updateOutsideRecords = new ArrayList<>();
        //统计数据,班次id：人数
        //正常乘车人数
        Map<Long, Integer> normalMap = new HashMap<>();
        //约而不坐人数
        Map<Long, Integer> reserveNotSitMap = new HashMap<>();
        //坐而不约人数
        Map<Long, Integer> sitNotReserveMap = new HashMap<>();
        //找出昨天约而不坐的记录修改
        for (BusReserveUserRecord record : records) {
            Long infoId = record.getInfoId();
            Integer reserveStatus = record.getReserveStatus();
            if (reserveStatus.equals(BusReserveStatus.NORMAL.value) ) {
                normalMap.put(infoId, normalMap.getOrDefault(infoId, 0) + 1);
            } else if (reserveStatus.equals(BusReserveStatus.WAIT.value)) {
                //未乘车修改为约而不坐
                reserveNotSitMap.put(infoId, reserveNotSitMap.getOrDefault(infoId, 0) + 1);
                record.setReserveStatus(BusReserveStatus.RESERVE_NOT_SIT.value);
                updateRecords.add(record.getId());
            } else if (reserveStatus.equals(BusReserveStatus.SIT_NOT_RESERVE.value)) {
                sitNotReserveMap.put(infoId, sitNotReserveMap.getOrDefault(infoId, 0) + 1);
            }else if (reserveStatus.equals(BusReserveStatus.RESERVE_NOT_SIT.value)){
                reserveNotSitMap.put(infoId, reserveNotSitMap.getOrDefault(infoId, 0) + 1);
            }
        }
        for (BusReserveOutsideUserRecord outsideRecord : outsideRecords) {
            Long infoId = outsideRecord.getInfoId();
            Integer reserveStatus = outsideRecord.getReserveStatus();
            if (reserveStatus.equals(BusReserveStatus.NORMAL.value) ) {
                normalMap.put(infoId, normalMap.getOrDefault(infoId, 0) + 1);
            } else if (reserveStatus.equals(BusReserveStatus.WAIT.value)) {
                //未乘车修改为约而不坐
                reserveNotSitMap.put(infoId, reserveNotSitMap.getOrDefault(infoId, 0) + 1);
                outsideRecord.setReserveStatus(BusReserveStatus.RESERVE_NOT_SIT.value);
                updateOutsideRecords.add(outsideRecord.getId());
            } else if (reserveStatus.equals(BusReserveStatus.SIT_NOT_RESERVE.value)) {
                sitNotReserveMap.put(infoId, sitNotReserveMap.getOrDefault(infoId, 0) + 1);
            }else if (reserveStatus.equals(BusReserveStatus.RESERVE_NOT_SIT.value)){
                reserveNotSitMap.put(infoId, reserveNotSitMap.getOrDefault(infoId, 0) + 1);
            }
        }
        //昨天班车配置
        List<BusReserveInfoVO> infoList = busReserveInfoService.getBusReserveInfoByDay(yesterdayStr);
        //昨天发车记录
        List<BusReserveDepartureRecord> departureRecords = new ArrayList<>(infoList.size());
        BusReserveDepartureRecord departureRecord = null;
        for (BusReserveInfoVO busInfo : infoList) {
            //生成发车记录
            departureRecord  = new BusReserveDepartureRecord();
            Long id = busInfo.getId();
            departureRecord.setId(IdGenerator.generateId());
            departureRecord.setInfoId(busInfo.getId());
            departureRecord.setInfoName(busInfo.getName());
            departureRecord.setDriverUserId(busInfo.getDriverUserId());
            departureRecord.setDriverName(busInfo.getDriverName());
            departureRecord.setDriverPhone(busInfo.getDriverPhone());
            departureRecord.setLicensePlate(busInfo.getLicensePlate());
            departureRecord.setLicensePlateToday(busInfo.getLicensePlateToday());
            departureRecord.setDriverUserIdToday(busInfo.getDriverUserIdToday());
            departureRecord.setDriverNameToday(busInfo.getDriverNameToday());
            departureRecord.setDriverPhoneToday(busInfo.getDriverPhoneToday());
            Date startTime = BusReserveUtil.calculateStartTime(busInfo,yesterdayStr);
            departureRecord.setStartTime(startTime);
            departureRecord.setUserCount(busInfo.getUserCount());
            departureRecord.setNormalCount(normalMap.getOrDefault(id, 0));
            departureRecord.setReserveNotSitCount(reserveNotSitMap.getOrDefault(id, 0));
            departureRecord.setSitNotReserveCount(sitNotReserveMap.getOrDefault(id, 0));
            departureRecords.add(departureRecord);
        }
        //查询所有重复，未下线的班车，设置今日发车时间，今日司机,车牌号
        LambdaQueryWrapper<BusReserveInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusReserveInfo::getShowStatus, 1).eq(BusReserveInfo::getRepeatStatus, 1);
        List<BusReserveInfo> list = busReserveInfoService.list(wrapper);
        for (BusReserveInfo busInfo : list) {
            Date startTimeToday = BusReserveUtil.calculateStartTime(busInfo, DateUtil.formatDate(now));
            busInfo.setStartTimeToday(startTimeToday);
            String driverUserId = busInfo.getDriverUserId();
            String driverUserIdToday = busInfo.getDriverUserIdToday();
            if (!Objects.equals(driverUserId, driverUserIdToday)) {
                busInfo.setDriverUserIdToday(driverUserId);
                busInfo.setDriverNameToday(busInfo.getDriverName());
                busInfo.setDriverPhoneToday(busInfo.getDriverPhone());
            }
            String licensePlate = busInfo.getLicensePlate();
            String licensePlateToday = busInfo.getLicensePlateToday();
            if (!Objects.equals(licensePlate, licensePlateToday)) {
                busInfo.setLicensePlateToday(licensePlate);
            }
        }

        //批量修改
        busReserveService.updateBatchRecord(updateRecords,updateOutsideRecords,departureRecords,list);
        log.info("生成昨天发车记录、修改未乘车记录、修改班车配置耗时：{}", DateUtil.formatBetween(timer.intervalMs()));
    }



    private void sendBusStartRemind(List<BusReserveUserRecordVO> records, List<BusReserveOutsideUserRecordVO> outsideRecords,boolean check) {
        final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
        final String agentId = sysConfigService.selectConfigByKey(AGENT_ID_KEY);
        final String detailUrl = sysConfigService.selectConfigByKey(DETAIL_URI);
        final String smsCode = sysConfigService.selectConfigByKey(SMS_START);
        //企微消息
        for (BusReserveUserRecordVO record : records) {
            Long id = record.getId();
            final String title = "发车提醒";
            String key = StrUtil.format("{}:outside_0:recordId_{}",REMIND_FIVE_MINUTE,id);
            if (check){
                boolean hasKey = redisService.hasKey(key);
                // 已提醒过，跳过
                if (hasKey) {
                    continue;
                }
            }
            String infoName = record.getInfoName();
            String time = DateUtil.format(record.getStartTime(), DatePattern.NORM_DATETIME_PATTERN);
            String location = record.getHalfwayLocation();
            if (StrUtil.isEmpty(location)){
                location = record.getStartLocation();
            }
            String content ;
            if (check){
                content = StrUtil.format("您预约的班车{}还有1小时发车，请到{}准时扫码乘车",infoName,location);
            }else {
                content = StrUtil.format("您预约的班车{}将于{}发车，请到{}准时扫码乘车",infoName,time,location);
            }
            final String uri = String.format(detailUrl, agentId, corpId,id);
            ThreadPoolUtil.execute(() -> {
                busReserveMessageService.sendTextCard(uri, title, content, record.getUserId());
            });
            if (check){
                redisService.setCacheObject(key,"2",2L, TimeUnit.HOURS);
            }

        }
        //阿里云短信
        for (BusReserveOutsideUserRecordVO record : outsideRecords) {
            Long id = record.getId();
            String key = StrUtil.format("{}:outside_1:recordId_{}",REMIND_FIVE_MINUTE,id);
            if (check){
                boolean hasKey = redisService.hasKey(key);
                // 已提醒过，跳过
                if (hasKey) {
                    continue;
                }
            }

            String infoName = record.getInfoName();
            String time = DateUtil.format(record.getStartTime(), DatePattern.NORM_DATETIME_PATTERN);
            String location = record.getStartLocation();
            ThreadPoolUtil.execute(() -> {
                BusReserveSmsVO smsVO = new BusReserveSmsVO(time,infoName,location);
                smsUtil.sendSmsByTemplate(smsCode, record.getPhone(),smsVO);
            });
            if (check){
                redisService.setCacheObject(key,"2",2L, TimeUnit.HOURS);
            }
        }
    }
}
