package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveDispatcher;
import com.lanshan.app.busreserve.service.BusReserveDispatcherService;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 调度员
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/dispatcher")
public class BusReserveDispatcherController {

    @Resource
    private BusReserveDispatcherService busReserveDispatcherService;

    /**
     * 获取调度员分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/getBusReserveDispatcherPageList" ,produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveDispatcher>> getBusReserveDispatcherPageList(@RequestBody BusReserveAuditPageDTO dto){
        Page<BusReserveDispatcher> pg = busReserveDispatcherService.getBusReserveDispatcherPageList(dto);
        return Result.build(pg);
    }

    /**
     * 批量新增调度员
     * <AUTHOR> yang.
     * @since 2025/5/21 14:31
     */
    @PostMapping(value = "/insertBusReserveDispatcher" ,produces = "application/json;charset=UTF-8")
    public Result<Object> insertBusReserveDispatcher(@RequestBody List<BusReserveDispatcher> list){
        Date now = new Date();
        list.forEach(b -> b.setCreateTime(now));
        busReserveDispatcherService.saveBatch(list);
        return Result.build();
    }

    /**
     * 编辑调度员
     * <AUTHOR> yang.
     * @since 2025/5/21 14:32
     */
    @PostMapping(value = "/updateBusReserveDispatcher" ,produces = "application/json;charset=UTF-8")
    public Result<Object> updateBusReserveDispatcher(@RequestBody BusReserveDispatcher busReserveDispatcher){
        busReserveDispatcherService.updateById(busReserveDispatcher);
        return Result.build();
    }

    /**
     * 批量删除调度员
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/removeBusReserveDispatcherByIds" ,produces = "application/json;charset=UTF-8")
    public Result<Object> removeBusReserveDispatcherByIds(@RequestBody Collection<String> ids){
        busReserveDispatcherService.removeByIds(ids);
        return Result.build();
    }

}
