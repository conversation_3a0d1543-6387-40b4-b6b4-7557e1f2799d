package com.lanshan.app.busreserve.controller;

import cn.hutool.core.collection.CollUtil;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.service.BusReserveInfoService;
import com.lanshan.app.busreserve.service.BusReserveOutsideUserRecordService;
import com.lanshan.app.busreserve.service.BusReserveService;
import com.lanshan.app.busreserve.service.BusReserveUserRecordService;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.common.utils.RedisService;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.qo.CollResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 班车预约
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/reserve")
public class BusReserveController {

    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Resource
    private BusReserveUserRecordService userRecordService;
    @Resource
    private BusReserveOutsideUserRecordService outsideUserRecordService;
    @Resource
    private BusReserveService busReserveService;
    @Resource
    private RedisService redisService;

    /**
     * 判断是否能预约班次
     * <AUTHOR> yang.
     * @since 2025/5/23 17:20
     */
    @PostMapping(value = "/checkBusReserveUserAuth",produces = "application/json;charset=UTF-8")
    public Result<Object> checkBusReserveUserAuth(@RequestBody BusReserveUserAuthDTO dto){
        BusReserveInfoDetailDTO detailDTO = new BusReserveInfoDetailDTO();
        detailDTO.setId(dto.getInfoId());
        detailDTO.setStartDay(dto.getStartDay());
        BusReserveInfoVO info = busReserveInfoService.getBusReserveInfoByIdAndDay(detailDTO);
        return busReserveService.checkBusReserveUserAuth(dto,info);
    }

    /**
     * 获取指定日期的班车列表
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    @PostMapping(value = "/getBusReserveInfoByLocationAndDay" ,produces = "application/json;charset=UTF-8")
    public Result<CollResult<BusReserveInfoVO>> getBusReserveInfoByLocationAndDay(@RequestBody BusReserveInfoDTO dto){
        List<BusReserveInfoVO> list = busReserveService.getBusReserveInfoByLocationAndDay(dto);
        return Result.build(new CollResult<>(list));
    }

    /**
     * 根据班次id和日期获取班次详情
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    @PostMapping(value = "/getBusReserveInfoByIdAndDay" ,produces = "application/json;charset=UTF-8")
    public Result<BusReserveInfoVO> getBusReserveInfoById(@RequestBody BusReserveInfoDetailDTO dto){
        BusReserveInfoVO detail = busReserveService.getBusReserveInfoByIdAndDay(dto);
        return Result.build(detail);
    }

    /**
     * 预约班车
     * <AUTHOR> yang.
     * @since 2025/5/26 13:52
     */
    @PostMapping(value = "/busReserve" ,produces = "application/json;charset=UTF-8")
    public Result<Object> busReserve(@RequestBody BusReserveDTO dto){
        return busReserveService.busReserve(dto);
    }


}
