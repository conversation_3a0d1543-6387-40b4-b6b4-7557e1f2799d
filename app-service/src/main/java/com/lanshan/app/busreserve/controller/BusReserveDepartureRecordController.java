package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveDepartureRecordPageDTO;
import com.lanshan.app.busreserve.po.BusReserveDepartureRecord;
import com.lanshan.app.busreserve.service.BusReserveDepartureRecordService;
import com.lanshan.app.busreserve.vo.BusReserveDepartureRecordExcelVO;
import com.lanshan.app.common.utils.ExcelUtils;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 班车记录
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/departureRecord")
public class BusReserveDepartureRecordController {

    @Resource
    private BusReserveDepartureRecordService busReserveDepartureRecordService;

    /**
     * 获取班车记录分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/getBusReserveDepartureRecordPageList" ,produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveDepartureRecord>> getBusReserveDepartureRecordPageList(@RequestBody BusReserveDepartureRecordPageDTO dto){
        Page<BusReserveDepartureRecord> pg = busReserveDepartureRecordService.getBusReserveDepartureRecordPageList(dto);
        return Result.build(pg);
    }

    /**
     * 导出班车记录
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/exportBusReserveDepartureRecord" ,produces = "application/json;charset=UTF-8")
    public void exportBusReserveDepartureRecord(@RequestBody BusReserveDepartureRecordPageDTO dto, HttpServletResponse response){
        List<BusReserveDepartureRecordExcelVO> records = busReserveDepartureRecordService.exportBusReserveDepartureRecord(dto);
        ExcelUtils.exportExcelByRecords("班车记录",records,BusReserveDepartureRecordExcelVO.class,response);
    }

}
