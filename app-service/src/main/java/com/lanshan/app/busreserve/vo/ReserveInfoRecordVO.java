package com.lanshan.app.busreserve.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class ReserveInfoRecordVO implements Serializable {

    private static final long serialVersionUID = -2780255969985024966L;

    private Long id;

    //班次id
    private Long infoId;

    //班次名称
    private String infoName;

    //始发站 武昌校区 | 嘉鱼校区
    private String startLocation;

    //终点站 武昌校区 | 嘉鱼校区
    private String endLocation;

    //学工号 | 手机号
    private String userId;

    //姓名
    private String userName;

    //部门id
    private Long deptId;

    //部门名称
    private String deptName;

    //是否代预约人 0否 1是
    private Integer replaceStatus;

    //代预约人id
    private String replaceUserId;

    //代预约人姓名
    private String replaceUserName;

    //状态 1待乘车 2正常乘车 3约而不坐 4坐而不约 5已取消
    private Integer reserveStatus;

    //发车时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startTime;

    //核验时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date checkTime;

    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //性别
    private String userGender;

    //中途上车点
    private String halfwayLocation;

    //发车日期
    @JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
    private Date startDay;

    //乘车人数
    private Integer userCount;

    //改签次数
    private Integer changeCount;

    //职务
    private String position;

    //审核人id
    private String auditUserId;

    //审核人姓名
    private String auditName;

    //理由
    private String reason;

    // 1未核验 2已核验 3坐而不约
    private Integer status;

    //是否外部用户 1是 0否
    private Integer outside;

    public void setReserveStatus(Integer reserveStatus) {
        this.reserveStatus = reserveStatus;
        if (reserveStatus != null) {
            switch (reserveStatus) {
                case 1: // 待乘车
                case 3: // 约而不坐
                    this.status = 1; // 未核验
                    break;
                case 2: // 正常乘车
                    this.status = 2; // 已核验
                    break;
                case 4: // 坐而不约
                    this.status = 3; // 坐而不约
                    break;
                default:
                    this.status = null; // 未知状态不设置
            }
        }
    }
}
