package com.lanshan.app.busreserve.vo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lanshan.app.common.annotation.ExcelHeader;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveDepartureRecordExcelVO implements Serializable {
    private static final long serialVersionUID = 5114912351539961261L;

    @ExcelHeader(title = "班次名称")
    private String infoName;

    @ExcelHeader(title = "司机姓名")
    private String driverName;

    @ExcelHeader(title = "司机手机号")
    private String driverPhone;

    @ExcelHeader(title = "今日司机姓名")
    private String driverNameToday;

    @ExcelHeader(title = "今日司机手机号")
    private String driverPhoneToday;

    @ExcelHeader(title = "车牌号")
    private String licensePlate;

    @ExcelHeader(title = "今日车牌号")
    private String licensePlateToday;

    //发车时间
    @ExcelHeader(required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startTime;

    @ExcelHeader(title = "发车时间")
    private String startTimeStr;

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
        this.startTimeStr = DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN);
    }


    @ExcelHeader(title = "核载人数")
    private Long userCount;

    @ExcelHeader(title = "正常乘车人数")
    private Long normalCount;

    @ExcelHeader(title = "约而不坐人数")
    private Long reserveNotSitCount;

    @ExcelHeader(title = "坐而不约人数")
    private Long sitNotReserveCount;

}
