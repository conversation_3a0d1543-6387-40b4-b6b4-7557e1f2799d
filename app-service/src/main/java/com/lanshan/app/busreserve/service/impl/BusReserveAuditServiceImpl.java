package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveAudit;
import com.lanshan.app.busreserve.dao.BusReserveAuditMapper;
import com.lanshan.app.busreserve.service.BusReserveAuditService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 班车预约-审核人 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveAuditServiceImpl extends ServiceImpl<BusReserveAuditMapper, BusReserveAudit> implements BusReserveAuditService {

    @Override
    public List<BusReserveAudit> getBusReserveAuditList(BusReserveAuditPageDTO dto) {
        LambdaQueryWrapper<BusReserveAudit> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveAudit::getCreateTime, BusReserveAudit::getId);

        if (StrUtil.isNotEmpty(dto.getUserId())){
            wrapper.like(BusReserveAudit::getUserId, dto.getUserId());
        }
        if (StrUtil.isNotEmpty(dto.getName())){
            wrapper.like(BusReserveAudit::getUserName, dto.getName());
        }

        List<BusReserveAudit> list = this.list(wrapper);
        return list;
    }
}
