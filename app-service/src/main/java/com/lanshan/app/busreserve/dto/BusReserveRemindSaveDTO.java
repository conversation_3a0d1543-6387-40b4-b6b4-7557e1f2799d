package com.lanshan.app.busreserve.dto;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveRemindSaveDTO implements Serializable {

    private static final long serialVersionUID = -5930463778177682954L;

    //用户id  | 手机号
    private String userId;

    //1企微用户 2外部用户
    private Integer userType;

    //班次id
    private Long infoId;

    private String userName;

    //发车日期 yyyy-MM-dd
    private String startDayStr;

    private Date startDay;

    public void setStartDayStr(String startDayStr) {
        this.startDayStr = startDayStr;
        this.startDay = DateUtil.parseDate(startDayStr);
    }
}
