package com.lanshan.app.busreserve.dao;

import com.lanshan.app.busreserve.dto.BusReserveDriverDTO;
import com.lanshan.app.busreserve.dto.BusReserveInfoDTO;
import com.lanshan.app.busreserve.dto.BusReserveInfoDetailDTO;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 班车预约-班次信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Mapper
public interface BusReserveInfoMapper extends BaseMapper<BusReserveInfo> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BusReserveInfo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveInfo> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BusReserveInfo> entities);

    int updateBusReserveInfoShowStatus(@Param("ids") Collection<Long> ids, @Param("showStatus") Integer showStatus);

    List<BusReserveInfoVO> getBusReserveInfoByLocationAndDay(
            @Param("startLocation") String startLocation,@Param("endLocation") String endLocation,
            @Param("startDay") String startDay,@Param("week") Integer week,@Param("seconds") Integer seconds
    );

    BusReserveInfoVO getBusReserveInfoByIdAndDay(@Param("dto") BusReserveInfoDetailDTO dto);

    List<BusReserveInfoVO> getBusReserveInfoByDriver(@Param("dto") BusReserveDriverDTO dto);

    void updateCheckCode(@Param("infoId") Long infoId,@Param("code") String code,@Param("codeUri") String codeUri);
}
