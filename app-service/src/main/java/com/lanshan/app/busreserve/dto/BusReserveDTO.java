package com.lanshan.app.busreserve.dto;

import com.lanshan.base.api.vo.user.UserInfoPartVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveDTO implements Serializable {

    private static final long serialVersionUID = 8870183195615169420L;

    //是否外部用户  1是 0否
    @NotNull
    private Integer outside;

    //职务
    private String position;

    //班次id
    @NotNull
    private Long infoId;

    //发车日期  yyyy-MM-dd
    @NotNull
    private String startDay;

    //审核人id
    private String auditUserId;

    //审核人姓名
    private String auditName;

    //审核人手机号
    private String auditPhone;

    //理由
    private String reason;

    //中途上车点
    private String halfwayLocation;

    //是否代预约人 0否 1是
    private Integer replaceStatus;

    //代预约人id
    private String replaceUserId;

    //代预约人姓名
    private String replaceUserName;


    //代预约人手机号
    private String replacePhone;

    //乘车人
    @NotEmpty
    private List<UserInfoPartVO> users;

}
