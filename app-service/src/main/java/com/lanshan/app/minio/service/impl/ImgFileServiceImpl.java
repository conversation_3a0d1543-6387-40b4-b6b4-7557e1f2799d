package com.lanshan.app.minio.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.lanshan.app.common.bo.FileInfo;
import com.lanshan.app.common.bo.ServiceException;
import com.lanshan.app.common.constant.CommonConstant;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.image.entity.ImgStudent;
import com.lanshan.app.image.entity.ImgTeacher;
import com.lanshan.app.image.entity.ImgUserImage;
import com.lanshan.app.image.enums.UserTypeEnum;
import com.lanshan.app.image.service.ImgStudentService;
import com.lanshan.app.image.service.ImgTeacherService;
import com.lanshan.app.image.service.ImgUserImageService;
import com.lanshan.app.minio.config.MinioConfig;
import com.lanshan.app.minio.converter.ComMinioFileConverter;
import com.lanshan.app.minio.entity.ComMinioFile;
import com.lanshan.app.minio.service.ComMinioFileService;
import com.lanshan.app.minio.service.ImgFileService;
import com.lanshan.app.minio.service.MinIOService;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.ServerSideEncryptionCustomerKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 图片文件服务实现类
 */
@Slf4j
@Service
public class ImgFileServiceImpl implements ImgFileService {

    public static final String BUCKET_NAME = "face-img";

    public static final String BUCKET_NAME_ENCRYPT = "face-img-encrypt";

    public static final Pattern pattern = Pattern.compile("^[a-zA-Z0-9].*");

    @Resource
    private MinioConfig prop;

    @Resource
    private MinioClient minioClient;

    @Resource
    private ComMinioFileService comMinioFileService;

    @Resource
    private MinIOService minIoService;
    @Resource
    private ImgUserImageService imgUserImageService;
    @Resource
    private ImgStudentService imgStudentService;
    @Resource
    private ImgTeacherService imgTeacherService;

    @Override
    public Boolean uploadImgManage(MultipartFile file, Integer imgType, Integer userType) {
        // 先保存原文件
        minIoService.upload(BUCKET_NAME, file,true);

        List<ComMinioFile> comMinioFiles = new ArrayList<>();

        try (ZipInputStream zipInputStream = new ZipInputStream(file.getInputStream(), getFileCharset(file))) {
            ZipEntry zipentry;
            List<String> userIds = new ArrayList<>();
            while ((zipentry = zipInputStream.getNextEntry()) != null) {
                if (zipentry.isDirectory()) {
                    throw ExceptionCodeEnum.FILE_TYPE_ERROR.toServiceException();
                }
                String zipEntryName = zipentry.getName();

                if (!pattern.matcher(zipEntryName).matches()) {
                    throw ExceptionCodeEnum.FILE_TYPE_ERROR.toServiceException();
                }
                String fileName = UuidUtils.generateUuid() + zipEntryName.substring(zipEntryName.lastIndexOf("."));
                String filePath = getFileName(Objects.requireNonNull(file.getOriginalFilename()), fileName);
                String fileType = zipEntryName.substring(zipEntryName.lastIndexOf("."));
                //企业微信图片格式有要求
                if (!CharSequenceUtil.equalsAny(fileType, ".png", ".jpg", "jpeg")) {
                    throw ExceptionCodeEnum.FILE_TYPE_ERROR.toServiceException();
                }
                // 默认加密
                SecretKey key = new SecretKeySpec(Base64.decode(prop.getEncryptionKey()), "AES");
                ServerSideEncryptionCustomerKey ssecKey = new ServerSideEncryptionCustomerKey(key);
                PutObjectArgs objectArgs = PutObjectArgs.builder()
                        .bucket(BUCKET_NAME)
                        .object(filePath)
                        .stream(zipInputStream, zipentry.getSize(), -1)
                        .sse(ssecKey)
                        .build();
                //文件名称相同会覆盖
                minioClient.putObject(objectArgs);
                comMinioFiles.add(ComMinioFile.builder()
                        .bucket(BUCKET_NAME)
                        .originalName(zipEntryName)
                        .createTime(new Date())
                        .fileSize(zipentry.getSize())
                        .fileName(UuidUtils.generateUuid() + fileType)
                        .path(filePath)
                        .fileType(fileType)
                        .isEncrypt(Boolean.TRUE)
                        .accessCode(UuidUtils.generateUuid())
                        .isDeleted(CommonConstant.DELETED_NO)
                        .build());
                // 获取用户ID,根据文件名称
                userIds.add(zipEntryName.substring(0, zipEntryName.lastIndexOf(".")));
            }
            // 校验用户是否存在
            boolean exist = checkUserExist(userIds,userType);
            if(exist){
                throw ExceptionCodeEnum.USER_NOT_EXIST.toServiceException();
            }
            comMinioFileService.saveBatch(comMinioFiles);
            return batchSaveImgUserImage(comMinioFiles, imgType, userType);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            if (e instanceof ServiceException) {
                throw (ServiceException) e;
            }
            throw ExceptionCodeEnum.FILE_UPLOAD_ERROR.toServiceException();
        }
    }

    /**
     * 校验用户是否不存在
     * @param userIds
     * @param userType
     */
    public Boolean checkUserExist(List<String> userIds, Integer userType) {
        boolean isExist = false;
        // 学生
        if (UserTypeEnum.STUDENT.getType().equals(userType)) {
            List<ImgStudent> students = imgStudentService.listByUserIds(userIds);
            if (CollUtil.isNotEmpty(students) && students.size() != userIds.size()) {
                isExist = true;
            }
        } else if (UserTypeEnum.TEACHER.getType().equals(userType)) {
            // 教师
            List<ImgTeacher> teachers = imgTeacherService.listByUserIds(userIds);
            if (CollUtil.isNotEmpty(teachers) && teachers.size() != userIds.size()) {
                isExist = true;
            }
        }
        return isExist;
    }

    /**
     * 批量保存图片用户信息
     * @param comMinioFiles
     * @param imgType
     */
    public boolean batchSaveImgUserImage(List<ComMinioFile> comMinioFiles, Integer imgType, Integer userType) {
        List<ImgUserImage> imgUserImages = new ArrayList<>();
        Date nowDate = new Date();
        for(ComMinioFile item : comMinioFiles){
            ImgUserImage imgUserImage = new ImgUserImage();
            imgUserImage.setImgType(imgType);
            imgUserImage.setIsAdminUpload(Boolean.TRUE);
            imgUserImage.setImgFileId(item.getId());
            imgUserImage.setStatus(2);
            imgUserImage.setUserType(userType);
            imgUserImage.setUserId(item.getOriginalName().substring(0, item.getOriginalName().lastIndexOf(".")));
            imgUserImage.setIsMain(Boolean.FALSE);
            imgUserImage.setImgUrl(minIoService.getAccessUrl(item));
            // 这里的上传次数暂时不计入统计
            imgUserImage.setIsCount(Boolean.FALSE);
            imgUserImage.setCreateBy(SecurityContextHolder.getUserIdStr());
            imgUserImage.setCreateTime(nowDate);
            imgUserImage.setIsDeleted(Boolean.FALSE);
            imgUserImages.add(imgUserImage);
        }
        return imgUserImageService.saveBatch(imgUserImages);
    }


    /**
     * 获取文件名称
     *
     * @param fileName     原文件名称
     * @param zipEntryName 压缩文件名称
     * @return String
     */
    private String getFileName(String fileName, String zipEntryName) {
        return Base64.encodeUrlSafe(fileName.substring(0, fileName.lastIndexOf("."))) + "/" + zipEntryName;
    }

    /**
     * 获取文件编码
     *
     * @param file 原文件
     * @return String
     */
    private Charset getFileCharset(MultipartFile file) {
        if (Boolean.TRUE.equals(testFileCharset(file))) {
            return StandardCharsets.UTF_8;
        }
        return Charset.forName("GBK");
    }

    /**
     * 测试文件编码
     *
     * @param file 原文件
     * @return Boolean
     */
    private Boolean testFileCharset(MultipartFile file) {
        try (ZipInputStream zipInputStream = new ZipInputStream(file.getInputStream(), StandardCharsets.UTF_8)) {
            zipInputStream.getNextEntry();
        } catch (Exception e) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
