package com.lanshan.app.hikvision.to;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "海康威视门禁服务接口响应基类")
public class HikvisionResponseSet implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "返回码，0 – 成功，其他- 失败，参考附录E.other.1 资源目录错误码(https://open.hikvision.com/docs/docId?productId=5c67f1e2f05948198c909700&version=%2Ff95e951cefc54578b523d1738f65f0a1&curNodeId=65ad0c9fa4e84be998a3456bae56bf41#b3d660a8)")
    private String code;
    @ApiModelProperty(value = "接口执行情况说明信息")
    private String msg;
}
