package com.lanshan.app.hikvision.qo;

import com.lanshan.app.hikvision.to.HikvisionResponseSet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "海康威视<删除人脸信息>接口响应 /api/frs/v1/face/picture/check")
public class HikvisionFaceRatingQo extends HikvisionResponseSet {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("人脸图片base64")
	private String facePicBinaryData;
}
