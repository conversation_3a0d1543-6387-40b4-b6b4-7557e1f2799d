package com.lanshan.app.hikvision.service;

import com.lanshan.app.hikvision.qo.*;
import com.lanshan.app.hikvision.to.*;

import java.util.List;

/**
 * @Description:
 * @ProjectName: ccadult-work-weixin-platform
 * @Package: com.lanshan.rpc.service
 * @ClassName: HikvisionApiOperationInterface
 * @Author: zhaoyong
 * @Email: <EMAIL>
 * @Date: 2022/7/29 17:33
 * @Version: 1.0
 */
public interface HikVisionApiOperationInterface {
    /** 根据人员唯一字段获取人员详细信息 */
    HikvisionPersonInfoResponseSet conditionPersonInfo(HikvisionPersonInfoQo qo);

    /** 预约免登记 */
    HikvisionAppointmentRegistrationResponseSet appointmentRegistration(HikvisionAppointmentRegistrationQo qo);

    /** 修改访客预约 */
    HikvisionAppointmentUpdateResponseSet appointmentUpdate(HikvisionAppointmentUpdateQo qo);

    /** 取消访客预约 */
    HikvisionAppointmentCancelResponseSet appointmentCancel(List<String> appointRecordIds);

    /** 车辆管理-车位预约定制化接口 */
    HikvisionParkingSpaceReservationsAdditionResponseSet parkingSpaceReservationsAddition(HikvisionParkingSpaceReservationsAdditionQo qo);

    /** 车辆管理-取消车位预约 */
    HikvisionParkingSpaceReservationsDeletionResponseSet parkingSpaceReservationsDeletion(HikvisionParkingSpaceReservationsDeletionQo qo);

    /** 人脸管理-添加人脸 */
    HikvisionFaceSingleAddResponseSet faceSingleAdd(HikvisionFaceSingleAddQo qo);

    /** 人脸管理-更新人脸 */
    HikvisionFaceSingleUpdateResponseSet faceSingleUpdate(HikvisionFaceSingleUpdateQo qo);

    /** 人脸管理-删除人脸 */
    HikvisionFaceSingleDeleteResponseSet faceSingleDelete(HikvisionFaceSingleDeleteQo qo);

    /** 人脸评分 */
    HikvisionFaceRatingResponseSet faceRating(HikvisionFaceRatingQo qo);
}
