package com.lanshan.app.fleamarket.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 个人主页VO
 */
@Data
@ApiModel(value = "个人主页VO")
public class PersonHomePageVO implements Serializable {

    private static final long serialVersionUID = -1705602790675468311L;

    @ApiModelProperty(value = "用户信息")
    private FleaUserInfoVO fleaUserInfoVO;

    @ApiModelProperty(value = "总收藏数")
    private Integer totalFavorite;

    @ApiModelProperty(value = "商品分页信息")
    private IPage<FleaCommodityVO> commodityPage;


}
