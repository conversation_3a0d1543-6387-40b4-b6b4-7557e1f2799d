package com.lanshan.app.fleamarket.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.fleamarket.entity.FleaCommodityType;
import com.lanshan.app.fleamarket.qo.FleaCommodityTypePageQo;
import com.lanshan.app.fleamarket.qo.FleaCommodityTypeQo;
import com.lanshan.app.fleamarket.vo.FleaCommodityTypeVO;

/**
 * 商品类型表(FleaCommodityType)表服务接口
 */
public interface FleaCommodityTypeService extends IService<FleaCommodityType> {

    /**
     * 分页查询商品类型
     * @param pageQo 分页查询对象
     * @return    分页查询结果
     */
    IPage<FleaCommodityTypeVO> pageCommodityType(FleaCommodityTypePageQo pageQo);

    /**
     * 新增商品类型
     * @param qo 商品类型对象
     */
    void addCommodityType(FleaCommodityTypeQo qo);

    /**
     * 修改商品类型
     * @param qo 商品类型对象
     */
    void updateCommodityType(FleaCommodityTypeQo qo);

    /**
     * 删除商品类型
     * @param id 商品类型id
     */
    void delCommodityType(Long id);
}

