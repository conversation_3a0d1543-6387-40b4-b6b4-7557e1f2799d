package com.lanshan.app.fleamarket.converter;


import java.util.List;

import com.lanshan.app.fleamarket.entity.FleaBlacklist;
import com.lanshan.app.fleamarket.vo.FleaBlacklistVO;
import org.mapstruct.Mapper;
import org.mapstruct.Builder;
import org.mapstruct.factory.Mappers;

/**
 * 黑名单表(FleaBlacklist)bean转换工具
 */
@Mapper(builder = @Builder(disableBuilder = true))
public interface FleaBlacklistConverter {

    FleaBlacklistConverter INSTANCE = Mappers.getMapper(FleaBlacklistConverter.class);

    FleaBlacklistVO toVO(FleaBlacklist entity);

    FleaBlacklist toEntity(FleaBlacklistVO vo);
    
    List<FleaBlacklistVO> toVO(List<FleaBlacklist> entityList);

    List<FleaBlacklist> toEntity(List<FleaBlacklistVO> voList);
}


