server:
  port: 8213
  servlet:
    context-path: /
  shutdown: graceful #开启优雅停机

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: app-service
  lifecycle:
    timeout-per-shutdown-phase: 20s #优雅停机设置缓冲时间 默认30s(在规定时间内如果线程无法执行完毕则会被强制停机)
  #环境 dev|test|prod|demo
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages_common
  cloud:
    nacos:
      discovery:
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        server-addr: ${NACOS_SERVER_IP:************}:${Nacos_SERVER_PORT:8848}
        namespace: ${NACOS_NAMESPACE:acb47656-c38d-4693-bf35-47205295fe4d}
        group: ${NACOS_GROUP:DEV_GROUP}
        service: ${spring.application.name}
        #        ip: @nacos.discovery.ip@
        metadata:
          spring.profiles.active: ${spring.profiles.active}
          preserved.heart.beat.interval: 1000
          preserved.heart.beat.timeout: 3000
          preserved.ip.delete.timeout: 3000
      config:
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        server-addr: ${NACOS_SERVER_IP:************}:${Nacos_SERVER_PORT:8848}
        namespace: ${NACOS_NAMESPACE:acb47656-c38d-4693-bf35-47205295fe4d}
        group: ${NACOS_GROUP:DEV_GROUP}
        file-extension: yaml
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: sa-token-company
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: false

magic-api:
  #配置web页面入口
  web: /magic-api/web
  resource:
    #配置文件存储位置。当以classpath开头时，为只读模式
    #mac用户请改为可读写的目录
    #如果不想存到文件中，可以参考配置将接口信息存到数据库、Redis中（或自定义）
    type: database # 配置存储在数据库中
    tableName: magic_api_file_v2 # 数据库中的表名
    prefix: /magic-api
    readonly: false # 是否是只读模式
  prefix: /mapi
  secret-key: Lskj@2024 # 远程推送时的秘钥，未配置则不开启推送
  push-path: /magic-api-sync #远程推送的路径，默认为/_magic-api-sync
  page:
    size: size # 页大小的请求参数名称 缺省时为size
    page: page # 页码的请求参数名称 缺省时为page
    default-page: 1 # 自定义默认首页 缺省时为1
    default-size: 10 # 自定义为默认页大小 缺省时为10
  response-code:
    success: 0 #执行成功的code值
    invalid: 400 #参数验证未通过的code值
    exception: -2 #执行出现异常的code值
  swagger:
    enabled: true
    version: 1.0
    description: 览山低代码平台接口信息
    title: 览山低代码平台接口
    name: 览山低代码平台接口
    location: /v2/api-docs/magic-api/swagger2.json
  security:
    username: admin # 登录用的用户名
    password: lskj2017 # 登录用的密码
  task:
    thread-name-prefix: magic-task- #线程池名字前缀
    pool:
      size: 8 #线程池大小，默认值为CPU核心数
    shutdown:
      awaitTermination: false #关闭时是否等待任务执行完毕，默认为false
      awaitTerminationPeriod: 10s # 关闭时最多等待任务执行完毕的时间
  api-iplimit:
    whitelist: *************,************,************,************
  web-iplimit:
    whitelist: *************,************,************,************
  backup: #备份相关配置
    enable: true #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    table-name: magic_backup_record_v2 #使用数据库存储备份时的表名

