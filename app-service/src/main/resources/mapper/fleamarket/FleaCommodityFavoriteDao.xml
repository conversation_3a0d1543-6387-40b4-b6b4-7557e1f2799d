<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.fleamarket.dao.FleaCommodityFavoriteDao">
    <resultMap type="com.lanshan.app.fleamarket.entity.FleaCommodityFavorite" id="FleaCommodityFavoriteMap">
        <result property="id" column="id"/>
        <result property="userid" column="userid"/>
        <result property="commodityId" column="commodity_id"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

    <resultMap id="FleaCommodityVOMap" type="com.lanshan.app.fleamarket.vo.FleaCommodityVO">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="price" column="price"/>
        <result property="typeId" column="type_id"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="imgUrls" column="img_urls"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="mobile" column="mobile"/>
        <result property="status" column="status"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="violationReason" column="violation_reason"/>
        <result property="viewCount" column="view_count"/>
        <result property="favoriteCount" column="favorite_count"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="creator" column="creator"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorAlias" column="creator_alias"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="isDeleted" column="is_deleted"/>
        <association property="creatorUserInfoVO" javaType="com.lanshan.app.fleamarket.vo.FleaUserInfoVO">
            <result property="userid" column="u_userid"/>
            <result property="name" column="u_name"/>
            <result property="alias" column="u_alias"/>
            <result property="cpContact" column="u_cp_contact"/>
            <result property="mobileContact" column="u_mobile_contact"/>
        </association>
    </resultMap>

    <select id="listAllFavorite" resultMap="FleaCommodityVOMap">
        SELECT c.id,
               c.name,
               c.description,
               c.price,
               c.type_id,
               c.cover_url,
               c.img_urls,
               c.mobile,
               c.status,
               c.audit_status,
               c.violation_reason,
               c.view_count,
               c.favorite_count,
               c.avatar_url,
               c.creator,
               c.creator_name,
               c.creator_alias,
               c.create_date,
               c.update_date,
               c.is_deleted,
               u.userid         AS u_userid,
               u.name           AS u_name,
               u.alias          AS u_alias,
               u.cp_contact     AS u_cp_contact,
               u.mobile_contact AS u_mobile_contact
        FROM flea_market.flea_commodity_favorite f
                 LEFT JOIN flea_market.flea_commodity c ON f.commodity_id = c.id
                 LEFT JOIN flea_market.flea_user_info u ON c.creator = u.userid
        WHERE f.userid = #{userid}
        ORDER BY f.create_date DESC
    </select>
</mapper>

