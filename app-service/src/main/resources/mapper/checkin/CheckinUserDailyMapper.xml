<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.checkin.dao.CheckinUserDailyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.checkin.po.CheckinUserDaily">
                <id column="id" property="id" />
                <result column="user_id" property="userId" />
                <result column="user_name" property="userName" />
                <result column="dept_id" property="deptId" />
                <result column="dept_name" property="deptName" />
                <result column="belong_day" property="belongDay" />
                <result column="group_id" property="groupId" />
                <result column="group_name" property="groupName" />
                <result column="checkin_status" property="checkinStatus" />
                <result column="remark" property="remark" />
                <result column="un_school" property="unSchool" />
                <result column="create_time" property="createTime" />
                <result column="update_time" property="updateTime" />
                <result column="checkin_time" property="checkinTime" />
                <result column="time_id" property="timeId" />
                <result column="gender" property="gender" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_id,
                user_name,
                dept_id,
                dept_name,
                belong_day,
                group_id,
                group_name,
                checkin_status,
                remark,
                un_school,
                create_time,
                update_time,
                checkin_time,
                time_id,
                gender
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" >
        INSERT INTO standard_app.checkin_user_daily (
        <include refid="Base_Column_List" />
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userName},
                #{entity.deptId},
                #{entity.deptName},
                #{entity.belongDay},
                #{entity.groupId},
                #{entity.groupName},
                #{entity.checkinStatus},
                #{entity.remark},
                #{entity.unSchool},
                #{entity.createTime},
                #{entity.updateTime},
                #{entity.checkinTime},
                #{entity.timeId},
                #{entity.gender}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" >
        INSERT INTO standard_app.checkin_user_daily (
        <include refid="Base_Column_List" />
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userName},
                #{entity.deptId},
                #{entity.deptName},
                #{entity.belongDay},
                #{entity.groupId},
                #{entity.groupName},
                #{entity.checkinStatus},
                #{entity.remark},
                #{entity.unSchool},
                #{entity.createTime},
                #{entity.updateTime},
                #{entity.checkinTime},
                #{entity.timeId},
                #{entity.gender}
            )
        </foreach>
        ON CONFLICT (user_id, belong_day) DO UPDATE SET
                user_name = EXCLUDED.user_name,
                dept_id = EXCLUDED.dept_id,
                dept_name = EXCLUDED.dept_name,
                group_id = EXCLUDED.group_id,
                group_name = EXCLUDED.group_name,
                checkin_status = EXCLUDED.checkin_status,
                remark = EXCLUDED.remark,
                un_school = EXCLUDED.un_school,
                update_time = EXCLUDED.update_time,
                checkin_time = EXCLUDED.checkin_time,
                time_id = EXCLUDED.time_id,
                gender = EXCLUDED.gender
    </insert>


</mapper>
