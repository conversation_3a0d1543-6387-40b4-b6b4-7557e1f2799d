<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.vehicleregister.dao.VehicleAuditUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.vehicleregister.po.VehicleAuditUser">
                <id column="id" property="id" />
                <result column="user_id" property="userId" />
                <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_id,
                create_time
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO vehicle_audit_user (
            id,
            user_id,
            create_time
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO vehicle_audit_user (
            id,
            user_id,
            create_time
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.createTime}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                user_id = EXCLUDED.user_id,
                create_time = EXCLUDED.create_time
    </insert>

    <select id="getVehicleAuditUserList" resultType="com.lanshan.app.vehicleregister.vo.VehicleAuditUserVO">
        SELECT
        cu.id,
        cu.user_id,
        cu.create_time,
        cp.name userName,
        cp.mobile as phone,
        d.name departmentName
        FROM
        standard_app.vehicle_audit_user cu
        INNER JOIN addressbook.cp_user cp ON cp.userid = cu.user_id
        INNER JOIN addressbook.cp_department d ON cp.main_department = d.id
        <where>
            <if test="dto.userId != null and dto.userId != ''">
                AND cu.user_id like '%' || #{dto.userId} || '%'
            </if>
            <if test="dto.userName != null and dto.userName != ''">
                AND cp.name like '%' || #{dto.userName} || '%'
            </if>
        </where>
    </select>

    <select id="getVehicleAuditUserList_COUNT" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        standard_app.vehicle_audit_user cu
        INNER JOIN addressbook.cp_user cp ON cp.userid = cu.user_id
        INNER JOIN addressbook.cp_department d ON cp.main_department = d.id
        <where>
            <if test="dto.userId != null and dto.userId != ''">
                AND cu.user_id like '%' || #{dto.userId} || '%'
            </if>
            <if test="dto.userName != null and dto.userName != ''">
                AND cp.name like '%' || #{dto.userName} || '%'
            </if>
        </where>
    </select>


</mapper>
