<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.access.dao.AcApiCallLogCountDao">

    <resultMap type="com.lanshan.app.access.entity.AcApiCallLogCount" id="AcApiCallCountLogMap">
        <result property="id" column="id"/>
        <result property="apiId" column="api_id"/>
        <result property="apiName" column="api_name"/>
        <result property="companyId" column="company_id"/>
        <result property="appId" column="app_id"/>
        <result property="count" column="count"/>
        <result property="successCount" column="success_count"/>
        <result property="failCount" column="fail_count"/>
        <result property="logDate" column="log_date"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="listCallCount" resultType="com.lanshan.app.access.vo.AcApiCallLogCountVO">
        SELECT SUM(count)         AS count,
               SUM(success_count) AS successCount,
               SUM(fail_count)    AS failCount,
               log_date           AS logDate
        FROM ac_api_call_log_count
        <where>
            <if test="param.companyId != null">
                AND company_id = #{param.companyId}
            </if>
            <if test="param.appId != null">
                AND app_id = #{param.appId}
            </if>
            <if test="param.apiId != null">
                AND api_id = #{param.apiId}
            </if>
            <if test="param.startTime != null and param.endTime != null">
                AND log_date &gt;= #{param.startTime} AND log_date &lt; (DATE(#{param.endTime}) + INTERVAL '1D')
            </if>
        </where>
        GROUP BY log_date
        ORDER BY logDate
    </select>
</mapper>

