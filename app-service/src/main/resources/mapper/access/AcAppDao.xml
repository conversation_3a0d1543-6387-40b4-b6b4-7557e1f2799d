<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.access.dao.AcAppDao">
    <resultMap type="com.lanshan.app.access.entity.AcApp" id="AcAppMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="companyId" column="company_id" jdbcType="INTEGER"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="applicationReason" column="application_reason" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.ac_app(app_name, company_id, company_name, remark, application_reason, status, start_time,
        end_time, create_by, create_time, update_by, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appName} , #{entity.companyId} , #{entity.companyName} , #{entity.remark} ,
            #{entity.applicationReason} , #{entity.status} , #{entity.startTime} , #{entity.endTime} ,
            #{entity.createBy} , #{entity.createTime} , #{entity.updateBy} , #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into access.ac_app(app_name, company_id, company_name, remark, application_reason, status, start_time,
        end_time, create_by, create_time, update_by, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appName}, #{entity.companyId}, #{entity.companyName}, #{entity.remark},
            #{entity.applicationReason}, #{entity.status}, #{entity.startTime}, #{entity.endTime}, #{entity.createBy},
            #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime})
        </foreach>
        ON CONFLICT(id) DO update set
        app_name = EXCLUDED.app_name , company_id = EXCLUDED.company_id , company_name = EXCLUDED.company_name , remark
        = EXCLUDED.remark , application_reason = EXCLUDED.application_reason , status = EXCLUDED.status , start_time =
        EXCLUDED.start_time , end_time = EXCLUDED.end_time , create_by = EXCLUDED.create_by , create_time =
        EXCLUDED.create_time , update_by = EXCLUDED.update_by , update_time = EXCLUDED.update_time
    </insert>
</mapper>

