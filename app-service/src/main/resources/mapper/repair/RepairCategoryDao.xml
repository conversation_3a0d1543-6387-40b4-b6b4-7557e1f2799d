<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.repair.dao.RepairCategoryDao">
    <resultMap type="com.lanshan.app.repair.entity.RepairCategory" id="RepairCategoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="idPath" column="id_path" jdbcType="VARCHAR"/>
        <result property="namePath" column="name_path" jdbcType="VARCHAR"/>
        <result property="managerUserids" column="manager_userids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into repair.repair_category(name, parent_id, id_path, name_path, manager_userids, creator, create_date,
                                           updater, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.parentId}, #{entity.idPath}, #{entity.namePath}, #{entity.managerUserids},
             #{entity.creator}, #{entity.createDate}, #{entity.updater}, #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into repair.repair_category(name, parent_id, id_path, name_path, manager_userids, creator, create_date,
                                           updater, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.parentId}, #{entity.idPath}, #{entity.namePath}, #{entity.managerUserids},
             #{entity.creator}, #{entity.createDate}, #{entity.updater}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set name            = EXCLUDED.name,
                                      parent_id       = EXCLUDED.parent_id,
                                      id_path         = EXCLUDED.id_path,
                                      name_path       = EXCLUDED.name_path,
                                      manager_userids = EXCLUDED.manager_userids,
                                      creator         = EXCLUDED.creator,
                                      create_date     = EXCLUDED.create_date,
                                      updater         = EXCLUDED.updater,
                                      update_date     = EXCLUDED.update_date
    </insert>

    <select id="listCategoryByManagerUserid" resultMap="RepairCategoryMap">
        SELECT *
        FROM repair.repair_category
        WHERE manager_userids @> JSONB_BUILD_ARRAY(#{userid}::text)
    </select>
</mapper>

