<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.repair.dao.RepairOperateLogDao">

    <resultMap type="com.lanshan.app.repair.entity.RepairOperateLog" id="RepairOperateLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="recordId" column="record_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="isAdmin" column="is_admin" jdbcType="INTEGER"/>
        <result property="transferredUserid" column="transferred_userid" jdbcType="VARCHAR"/>
        <result property="transferredName" column="transferred_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="transferUserid" column="transfer_userid" jdbcType="VARCHAR"/>
        <result property="transferName" column="transfer_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into repair.repair_operate_log(record_id, type, userid, name, is_admin, transferred_userid,
                                              transferred_name, create_date, transfer_userid, transfer_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recordId}, #{entity.type}, #{entity.userid}, #{entity.name}, #{entity.isAdmin},
             #{entity.transferredUserid}, #{entity.transferredName}, #{entity.createDate}, #{entity.transferUserid},
             #{entity.transferName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into repair.repair_operate_log(record_id, type, userid, name, is_admin, transferred_userid,
                                              transferred_name, create_date, transfer_userid, transfer_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recordId}, #{entity.type}, #{entity.userid}, #{entity.name}, #{entity.isAdmin},
             #{entity.transferredUserid}, #{entity.transferredName}, #{entity.createDate}, #{entity.transferUserid},
             #{entity.transferName})
        </foreach>
        ON CONFLICT(id) DO update set record_id          = EXCLUDED.record_id,
                                      type               = EXCLUDED.type,
                                      userid             = EXCLUDED.userid,
                                      name               = EXCLUDED.name,
                                      is_admin           = EXCLUDED.is_admin,
                                      transferred_userid = EXCLUDED.transferred_userid,
                                      transferred_name   = EXCLUDED.transferred_name,
                                      create_date        = EXCLUDED.create_date,
                                      transfer_userid    = EXCLUDED.transfer_userid,
                                      transfer_name      = EXCLUDED.transfer_name
    </insert>

</mapper>

