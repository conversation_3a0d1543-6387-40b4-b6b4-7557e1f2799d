<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.image.dao.ImgOutsiderDao">
    <resultMap type="com.lanshan.app.image.entity.ImgOutsider" id="ImgOutsiderMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="phoneNumber" column="phone_number" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="credentialType" column="credential_type" jdbcType="INTEGER"/>
        <result property="credentialNumber" column="credential_number" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="verifyUser" column="verify_user" jdbcType="VARCHAR"/>
        <result property="verifyTime" column="verify_time" jdbcType="TIMESTAMP"/>
        <result property="userType" column="user_type" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into image.img_outsider(user_name, phone_number, gender, credential_type, credential_number, dept_id,
        create_by, create_time, update_by, update_time, status, remark, dept_name, verify_user, verify_time, user_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName} , #{entity.phoneNumber} , #{entity.gender} , #{entity.credentialType} ,
            #{entity.credentialNumber} , #{entity.deptId} , #{entity.createBy} , #{entity.createTime} ,
            #{entity.updateBy} , #{entity.updateTime} , #{entity.status} , #{entity.remark} , #{entity.deptName} ,
            #{entity.verifyUser} , #{entity.verifyTime}, #{entity.userType})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into image.img_outsider(user_name, phone_number, gender, credential_type, credential_number, dept_id,
        create_by, create_time, update_by, update_time, status, remark, dept_name, verify_user, verify_time, user_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName}, #{entity.phoneNumber}, #{entity.gender}, #{entity.credentialType},
            #{entity.credentialNumber}, #{entity.deptId}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy},
            #{entity.updateTime}, #{entity.status}, #{entity.remark}, #{entity.deptName}, #{entity.verifyUser},
            #{entity.verifyTime}, #{entity.userType})
        </foreach>
        ON CONFLICT(id) DO update set
        user_name = EXCLUDED.user_name , phone_number = EXCLUDED.phone_number , gender = EXCLUDED.gender ,
        credential_type = EXCLUDED.credential_type , credential_number = EXCLUDED.credential_number , dept_id =
        EXCLUDED.dept_id , create_by = EXCLUDED.create_by , create_time = EXCLUDED.create_time , update_by =
        EXCLUDED.update_by , update_time = EXCLUDED.update_time , status = EXCLUDED.status , remark = EXCLUDED.remark ,
        dept_name = EXCLUDED.dept_name , verify_user = EXCLUDED.verify_user , verify_time = EXCLUDED.verify_time , user_type = EXCLUDED.user_type
    </insert>

    <select id="pageOutsiderImgInfo" resultType="com.lanshan.app.image.entity.ImgOutsider">
        SELECT
            ios."id",
            ios.user_name,
            ios.phone_number,
            ios.gender,
            ios.credential_type,
            ios.credential_number,
            ios.dept_id,
            ios.create_by,
            ios.create_time,
            ios.update_by,
            ios.update_time,
            ios.status,
            ios.remark,
            ios.dept_name,
            ios.verify_user,
            ios.verify_time,
            ios.user_type
        FROM
            img_outsider ios
        <if test="outsiderSearchDTO.idPath != null and outsiderSearchDTO.idPath != ''">
            LEFT JOIN img_outsider_dept iod ON ios.dept_id = iod.dept_id AND iod.tree_type = 1
        </if>
        WHERE ios.user_type = #{outsiderSearchDTO.userType}
            <if test="outsiderSearchDTO.userName != null and outsiderSearchDTO.userName != ''">
                AND ios.user_name LIKE CONCAT('%', #{outsiderSearchDTO.userName}::text, '%')
            </if>
            <if test="outsiderSearchDTO.phoneNumber != null and outsiderSearchDTO.phoneNumber != ''">
                AND ios.phone_number LIKE CONCAT('%', #{outsiderSearchDTO.phoneNumber}::text, '%')
            </if>
            <if test="outsiderSearchDTO.verifyUser != null and outsiderSearchDTO.verifyUser != ''">
                AND ios.verify_user LIKE CONCAT('%', #{outsiderSearchDTO.verifyUser}::text, '%')
            </if>
            <if test="outsiderSearchDTO.idPath != null and outsiderSearchDTO.idPath != ''">
                AND iod.id_path like concat(#{outsiderSearchDTO.idPath}::text,'%')
            </if>
            ORDER BY ios.id DESC
    </select>
</mapper>

