<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvVenueTimeConfigDao">
    <resultMap type="com.lanshan.app.reservation.entity.RsvVenueTimeConfig" id="RsvVenueTimeConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="dayOfWeek" column="day_of_week" jdbcType="INTEGER"/>
        <result property="timeOfDay" column="time_of_day" jdbcType="VARCHAR"/>
        <result property="venueId" column="venue_id" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_venue_time_config(day_of_week, time_of_day, venue_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dayOfWeek} , #{entity.timeOfDay} , #{entity.venueId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_venue_time_config(day_of_week, time_of_day, venue_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dayOfWeek}, #{entity.timeOfDay}, #{entity.venueId})
        </foreach>
        ON CONFLICT(id) DO update set
        day_of_week = EXCLUDED.day_of_week , time_of_day = EXCLUDED.time_of_day , venue_id = EXCLUDED.venue_id
    </insert>
</mapper>

