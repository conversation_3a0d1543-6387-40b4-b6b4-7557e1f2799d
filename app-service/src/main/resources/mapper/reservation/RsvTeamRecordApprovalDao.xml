<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvTeamRecordApprovalDao">
    <resultMap type="com.lanshan.app.reservation.entity.RsvTeamRecordApproval" id="RsvTeamRecordApprovalMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="teamRecordId" column="team_record_id" jdbcType="INTEGER"/>
        <result property="spSeq" column="sp_seq" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="spType" column="sp_type" jdbcType="INTEGER"/>
        <result property="spStatus" column="sp_status" jdbcType="INTEGER"/>
        <result property="spTime" column="sp_time" jdbcType="TIMESTAMP"/>
        <result property="spRemark" column="sp_remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_team_record_approval(team_record_id, sp_seq, user_id, user_name, sp_type, sp_status,
        sp_time, sp_remark, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.teamRecordId} , #{entity.spSeq} , #{entity.userId} , #{entity.userName} , #{entity.spType} ,
            #{entity.spStatus} , #{entity.spTime} , #{entity.spRemark} , #{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_team_record_approval(team_record_id, sp_seq, user_id, user_name, sp_type, sp_status,
        sp_time, sp_remark, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.teamRecordId}, #{entity.spSeq}, #{entity.userId}, #{entity.userName}, #{entity.spType},
            #{entity.spStatus}, #{entity.spTime}, #{entity.spRemark}, #{entity.createTime})
        </foreach>
        ON CONFLICT(id) DO update set
        team_record_id = EXCLUDED.team_record_id , sp_seq = EXCLUDED.sp_seq , user_id = EXCLUDED.user_id , user_name =
        EXCLUDED.user_name , sp_type = EXCLUDED.sp_type , sp_status = EXCLUDED.sp_status , sp_time = EXCLUDED.sp_time ,
        sp_remark = EXCLUDED.sp_remark , create_time = EXCLUDED.create_time
    </insert>
</mapper>

