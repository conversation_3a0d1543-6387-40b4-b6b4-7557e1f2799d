<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.reservation.dao.RsvVenueFieldTypeDao">

    <resultMap type="com.lanshan.app.reservation.entity.RsvVenueFieldType" id="RsvVenueFieldTypeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="BOOLEAN"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_venue_field_type(name, icon_url, status, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.iconUrl} , #{entity.status} , #{entity.deleteFlag})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into reservation.rsv_venue_field_type(name, icon_url, status, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.iconUrl}, #{entity.status}, #{entity.deleteFlag})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , icon_url = EXCLUDED.icon_url , status = EXCLUDED.status , delete_flag = EXCLUDED.delete_flag     </insert>

</mapper>

