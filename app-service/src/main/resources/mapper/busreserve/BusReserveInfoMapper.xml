<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveInfo">
                <id column="id" property="id" />
                <result column="name" property="name" />
                <result column="start_location" property="startLocation" />
                <result column="end_location" property="endLocation" />
                <result column="halfway_location" property="halfwayLocation" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
                <result column="license_plate" property="licensePlate" />
                <result column="user_count" property="userCount" />
                <result column="repeat_status" property="repeatStatus" />
                <result column="start_day" property="startDay" />
                <result column="week_list" property="weekList" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
                <result column="start_time" property="startTime" />
                <result column="driver_user_id" property="driverUserId" />
                <result column="driver_name" property="driverName" />
                <result column="driver_phone" property="driverPhone" />
                <result column="deadline_hour" property="deadlineHour" />
                <result column="license_plate_today" property="licensePlateToday" />
                <result column="driver_user_id_today" property="driverUserIdToday" />
                <result column="show_status" property="showStatus" />
                <result column="create_time" property="createTime" />
                <result column="driver_name_today" property="driverNameToday" />
                <result column="driver_phone_today" property="driverPhoneToday" />
                <result column="check_code_url" property="checkCodeUrl" />
                <result column="check_code" property="checkCode" />
        <result column="start_time_today" property="startTimeToday" />
        <result column="start_time_seconds" property="startTimeSeconds" />
    </resultMap>

    <resultMap id="BusReserveInfoVOMap" type="com.lanshan.app.busreserve.vo.BusReserveInfoVO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="start_location" property="startLocation" />
        <result column="end_location" property="endLocation" />
        <result column="halfway_location" property="halfwayLocation" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="license_plate" property="licensePlate" />
        <result column="user_count" property="userCount" />
        <result column="repeat_status" property="repeatStatus" />
        <result column="start_day" property="startDay" />
        <result column="week_list" property="weekList" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="start_time" property="startTime" />
        <result column="driver_user_id" property="driverUserId" />
        <result column="driver_name" property="driverName" />
        <result column="driver_phone" property="driverPhone" />
        <result column="deadline_hour" property="deadlineHour" />
        <result column="license_plate_today" property="licensePlateToday" />
        <result column="driver_user_id_today" property="driverUserIdToday" />
        <result column="show_status" property="showStatus" />
        <result column="create_time" property="createTime" />
        <result column="driver_name_today" property="driverNameToday" />
        <result column="driver_phone_today" property="driverPhoneToday" />
        <result column="check_code_url" property="checkCodeUrl" />
        <result column="check_code" property="checkCode" />
        <result column="start_time_today" property="startTimeToday" />
        <result column="start_time_seconds" property="startTimeSeconds" />
        <result column="seatCount" property="seatCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                name,
                start_location,
                end_location,
                halfway_location,
                license_plate,
                user_count,
                repeat_status,
                start_day,
                week_list,
                start_time,
                driver_user_id,
                driver_name,
                driver_phone,
                deadline_hour,
                license_plate_today,
                driver_user_id_today,
                show_status,
                create_time,
                driver_name_today,
                driver_phone_today,
                check_code_url,
                check_code,
                start_time_today,
                start_time_seconds
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_info (
        <include refid="Base_Column_List" />
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.name},
                #{entity.startLocation},
                #{entity.endLocation},
                #{entity.halfwayLocation},
                #{entity.licensePlate},
                #{entity.userCount},
                #{entity.repeatStatus},
                #{entity.startDay},
                #{entity.weekList},
                #{entity.startTime},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.deadlineHour},
                #{entity.licensePlateToday},
                #{entity.driverUserIdToday},
                #{entity.showStatus},
                #{entity.createTime},
                #{entity.driverNameToday},
                #{entity.driverPhoneToday},
                #{entity.checkCodeUrl},
                #{entity.checkCode},
                #{entity.startTimeToday},
                #{entity.startTimeSeconds}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_info (
        <include refid="Base_Column_List" />
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.name},
                #{entity.startLocation},
                #{entity.endLocation},
                #{entity.halfwayLocation},
                #{entity.licensePlate},
                #{entity.userCount},
                #{entity.repeatStatus},
                #{entity.startDay},
                #{entity.weekList},
                #{entity.startTime},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.deadlineHour},
                #{entity.licensePlateToday},
                #{entity.driverUserIdToday},
                #{entity.showStatus},
                #{entity.createTime},
                #{entity.driverNameToday},
                #{entity.driverPhoneToday},
                #{entity.checkCodeUrl},
                #{entity.checkCode},
                #{entity.startTimeToday},
                #{entity.startTimeSeconds}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                start_location = EXCLUDED.start_location,
                end_location = EXCLUDED.end_location,
                halfway_location = EXCLUDED.halfway_location,
                license_plate = EXCLUDED.license_plate,
                user_count = EXCLUDED.user_count,
                repeat_status = EXCLUDED.repeat_status,
                start_day = EXCLUDED.start_day,
                week_list = EXCLUDED.week_list,
                start_time = EXCLUDED.start_time,
                driver_user_id = EXCLUDED.driver_user_id,
                driver_name = EXCLUDED.driver_name,
                driver_phone = EXCLUDED.driver_phone,
                deadline_hour = EXCLUDED.deadline_hour,
                license_plate_today = EXCLUDED.license_plate_today,
                driver_user_id_today = EXCLUDED.driver_user_id_today,
                show_status = EXCLUDED.show_status,
                create_time = EXCLUDED.create_time,
                driver_name_today = EXCLUDED.driver_name_today,
                driver_phone_today = EXCLUDED.driver_phone_today,
                check_code_url = EXCLUDED.check_code_url,
                check_code = EXCLUDED.check_code,
                start_time_today = EXCLUDED.start_time_today,
                start_time_seconds = EXCLUDED.start_time_seconds
    </insert>

    <update id="updateBusReserveInfoShowStatus">
        UPDATE standard_app.bus_reserve_info
        SET show_status = #{showStatus}
        WHERE id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateCheckCode">
        UPDATE standard_app.bus_reserve_info
        SET check_code = #{code}, check_code_url = #{codeUri}
        WHERE id = #{infoId}
    </update>

    <select id="getBusReserveInfoByLocationAndDay" resultMap="BusReserveInfoVOMap">
        SELECT bi.id, name, start_location, end_location, halfway_location, license_plate, user_count, repeat_status, start_day,
               week_list, start_time, driver_user_id, driver_name, driver_phone, deadline_hour, license_plate_today,
               driver_user_id_today, show_status, create_time, driver_name_today, driver_phone_today, check_code_url, check_code, start_time_today, start_time_seconds
        FROM standard_app.bus_reserve_info bi
        <where>
            and bi.show_status = 1
            <if test="startLocation != null and startLocation != ''">
                and bi.start_location = #{startLocation}
            </if>
            <if test="endLocation != null and endLocation != ''">
                and bi.end_location = #{endLocation}
            </if>
            <if test="startDay != null and startDay != '' and week != null ">
                and (
                    bi.start_day = #{startDay} or bi.week_list @> jsonb_build_array(#{week}::int)
                )
            </if>
            <if test="seconds != null">
                and bi.start_time_seconds &gt; #{seconds}
            </if>
        </where>
        ORDER BY bi.start_time_seconds ASC,bi.id ASC
    </select>

    <select id="getBusReserveInfoByIdAndDay" resultMap="BusReserveInfoVOMap">
        SELECT bi.id, name, start_location, end_location, halfway_location, license_plate, user_count, repeat_status, start_day,
               week_list, start_time, driver_user_id, driver_name, driver_phone, deadline_hour, license_plate_today,
               driver_user_id_today, show_status, create_time, driver_name_today, driver_phone_today, check_code_url, check_code, start_time_today, start_time_seconds
        FROM standard_app.bus_reserve_info bi
        WHERE
            bi.id = #{dto.id}
            and (
                bi.start_day = #{dto.startDay} or bi.week_list @> jsonb_build_array(#{dto.week}::int)
            )
    </select>

    <select id="getBusReserveInfoByDriver" resultMap="BusReserveInfoVOMap">
        SELECT bi.id, name, start_location, end_location, halfway_location, license_plate, user_count, repeat_status, start_day,
               week_list, start_time, driver_user_id, driver_name, driver_phone, deadline_hour, license_plate_today,
               driver_user_id_today, show_status, create_time, driver_name_today, driver_phone_today, check_code_url, check_code, start_time_today, start_time_seconds
        FROM standard_app.bus_reserve_info bi
        <where>
            and bi.show_status = 1
            <if test="dto.userId != null and dto.userId != ''">
                and bi.driver_user_id_today = #{dto.userId}
            </if>
            <if test="dto.startDay != null and dto.startDay != '' and dto.week != null ">
                and (
                    bi.start_day = #{dto.startDay} or bi.week_list @> jsonb_build_array(#{dto.week}::int)
                )
            </if>
            <if test="dto.seconds != null">
                and bi.start_time_seconds &gt; #{dto.seconds}
            </if>
        </where>
        ORDER BY bi.start_time_seconds ASC,bi.id ASC
    </select>


</mapper>
